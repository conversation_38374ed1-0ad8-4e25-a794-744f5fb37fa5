<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\CachePre;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Exception\BusinessException;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\AllotServiceInterface;
use App\JsonRpc\BackOrderServiceInterface;
use App\JsonRpc\BrandServiceInterface;
use App\JsonRpc\CategoryServiceInterface;
use App\JsonRpc\DifferenceOrderServiceInterface;
use App\JsonRpc\GoodsPackingServiceInterface;
use App\JsonRpc\ListServiceInterface;
use App\JsonRpc\LogisticsInfoServiceInterface;
use App\JsonRpc\LogisticsServiceInterface;
use App\JsonRpc\MessageLogServiceInterface;
use App\JsonRpc\OutStoreServiceInterface;
use App\JsonRpc\PurchaseOrderServiceInterface;
use App\JsonRpc\SkuServiceInterface;
use App\JsonRpc\SpuServiceInterface;
use App\JsonRpc\SupplierServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use App\Library\Facades\AllotService;
use App\Library\Facades\OutStoreService;
use App\Library\Facades\SendService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;

/**
 * @Controller()
 */
class BackOrderController extends AbstractController
{
    /**
     * @Inject()
     * @var ValidatorFactoryInterface
     */
    private $validator;
    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;
    /**
     * @Inject()
     * @var SkuServiceInterface
     */
    private $SkuService;
    /**
     * @Inject()
     * @var SpuServiceInterface
     */
    private $SpuService;
    /**
     * @Inject()
     * @var CategoryServiceInterface
     */
    private $CategoryService;
    /**
     * @Inject()
     * @var BrandServiceInterface
     */
    private $BrandService;
    /**
     * @Inject()
     * @var SupplierServiceInterface
     */
    private $SupplierService;
    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $AdminService;
    /**
     * @Inject()
     * @var BackOrderServiceInterface
     */
    private $BackOrderService;

    /**
     * @Inject()
     * @var AllotServiceInterface
     */
    private $allotService;
    /**
     * @Inject()
     * @var DifferenceOrderServiceInterface
     */
    private $DifferenceOrderService;
    /**
     * @Inject()
     * @var PurchaseOrderServiceInterface
     */
    private $PurchaseOrderService;
    /**
     * @Inject()
     * @var LogisticsInfoServiceInterface
     */
    private $LogisticsInfoService;
    /**
     * @Inject()
     * @var LogisticsServiceInterface
     */
    private $LogisticsService;
    /**
     * @Inject()
     * @var ListServiceInterface
     */
    private $ListService;
    /**
     * @Inject()
     * @var MessageLogServiceInterface
     */
    private $MessageLogService;
    /**
     * @Inject()
     * @var OutStoreServiceInterface
     */
    private $OutStoreService;
    /**
     * @Inject()
     * @var GoodsPackingServiceInterface
     */
    private $GoodsPackingService;

    /**
     * 列表
     * @RequestMapping(path="/backOrder/list", methods="get,post")
     */
    public function list(RequestInterface $request)
    {
        $w_id = $request->input('w_id','');
        $start_time = $request->input('start_time','');
        $end_time = $request->input('end_time','');

        $userWIds = $this->AdminService->organizeWareHouseData($this->getUserId());

        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 状态
        $orderStatus = PublicCode::back_order_status;

        //模式
        $coModel = PublicCode::co_model;

        // 供应商
        $supplier = $this->SupplierService->getSuppliers([], ['id', 'name']);
        $supplier = $supplier ? array_column($supplier, 'name', 'id') : [];
        // 查找类型
        $selectType = [
            1 => '店内码查找',
            2 => '货号查找',
            3 => '条形码查找',
            4 => 'SPU查找',
            5 => 'SKU查找'
        ];

        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            if (!empty($search['w_id'])) {
                $search['w_ids'] = explode(",", $search['w_id']);
                unset($search['w_id']);
            } else {
                $search['w_ids'] = $userWIds; // 权限-仓库
            }
            $search['status_list'] = array_keys($orderStatus);// wms可展示的状态
            $search['no_logistics_detail'] = true;
            $export = $params['export'] ?? 0;// 0列表 1导出 2导出明细

            //2导出明细
            if($export == 2){
                $url = $this->BackOrderService->exportDetailList($search);
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
            }

            logger()->info('wms退返', [$search]);
            $list = $this->BackOrderService->list($export, (int)$page, (int)$pageLimit, $search);

            logger()->info('wms退返列表', [$list]);
            if ($list['data']) {
                //查询出库数量
                foreach ($list['data'] as &$item) {
                    unset($item['logistics_detail']);
                    //发货数量
                    $item['sup_name'] = $supplier[$item['sup_id']];
                    $item['w_name'] = $warehouse[$item['w_id']];
                    $item['status_text'] = $orderStatus[$item['status']];
                    $item['pur_name'] = $coModel[$item['pur_type']];
                    //出库状态
                    $item['out_status_text'] = "-";
                    if (!empty($item['out_num']) && $item['out_num'] < $item['apply_num']) {
                        $item['out_status_text'] = "部分出库";
                    } elseif (!empty($item['out_num']) && $item['out_num'] >= $item['apply_num']) {
                        $item['out_status_text'] = "全部出库";
                    }

                    if (empty($item['send_num'])) $item['send_num'] = 0;

                    //发货数量 real_return_num
                    //发货状态
                    $item['delivery_status_text'] = "-";
                    if (!empty($item['send_num']) && $item['send_num'] < $item['apply_num']) {
                        $item['delivery_status_text'] = "部分发货";
                    } elseif (!empty($item['send_num']) && $item['send_num'] >= $item['apply_num']) {
                        $item['delivery_status_text'] = "全部发货";
                    }

                    //签收数量 real_collect_num
                    //签收状态
                    $item['sign_status_text'] = "-";
                    if (!empty($item['real_collect_num']) && $item['real_collect_num'] > $item['send_num']) {
                        $item['sign_status_text'] = "部分签收";
                    } elseif (!empty($item['real_collect_num']) && $item['real_collect_num'] >= $item['send_num']) {
                        $item['sign_status_text'] = "全部签收";
                    }
                }
            }

            if ($export == 1) {
                if (!$list['data']) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }
                $url = exportToExcel($this->exportListHeader(), $list['data'], '退返任务单');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        return $this->show('backOrder/list', [
            'w_ids' => explode(",", $w_id),
            'start_time' => $start_time,
            'end_time' => $end_time,
            'warehouse_list' => $warehouse,
            'order_status' => $orderStatus,
            'supplier_list' => $supplier,
            'select_type' => $selectType
        ]);
    }

    private function exportListHeader()
    {
        return [
            'serial_no' => '任务单号',
            'sup_name' => '供应商',
            'w_name' => '仓库',
            'brand_names' => '品牌',
            'status_text' => '状态',
            'apply_num' => '申请数',
            'out_num' => '出库数量',
            'box_goods_num' => '装箱数',
            'send_num' => '发货数',
            'created_at' => '创建时间',
            'checked_at' => '下发时间',
            'express_time' => '发货时间',
        ];
    }

    /**
     * 明细
     * @RequestMapping(path="/backOrder/detail", methods="get,post")
     */
    public function detail()
    {
        $id = (int)$this->request->input('id');
        $order = $this->BackOrderService->getOrder(['id' => $id]);
        if (!$order) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '不存在');
        }

        // 基本信息
        $info = [
            'id' => $order['id'],
            'serial_no' => $order['serial_no'],
            'w_name' => $this->WarehouseService->getWarehouse(['id' => $order['w_id']], ['name'])['name'] ?? '',
            'status_text' => PublicCode::back_order_status[$order['status']],
            'created_at' => $order['created_at'],
            'original_no' => $order['original_no'],
            'sup_name' => $this->SupplierService->getSupplierOne($order['sup_id'])['name'] ?? '',
            'pur_type' => PublicCode::co_model[$order['pur_type']],
            'freight_bear' => PublicCode::back_order_freight_bear[$order['freight_bear']],
            'delivery_type' => PublicCode::back_order_delivery_type[$order['delivery_type']],
            'take_info' => implode(',', [$order['recelver'], $order['mobile'], $order['address']]),
            'remark' => $order['remark'],
            'code_type' => $order['code_type'],
        ];
        if ($order['back_type'] == PublicCode::back_order_type_diff) { // 差异退返
            $purchase_no = $this->DifferenceOrderService->getOrder(['serial_no' => $order['original_no']], ['purchase_no'])['purchase_no'];
        } else { // 采购退返
            $purchase_no = $order['original_no'];
        }
        $purchase_order = $this->PurchaseOrderService->getOrder(['serial_no' => $purchase_no], ['admin_name']);
        $info['pur_admin_name'] = $purchase_order['admin_name']; // 采购负责人

        // 发货信息
        $logistics_list = [];// 物流公司下拉框
        $express_list = [];// 物流公司下拉框
        $l_info = [];// 物流与装箱信息
        $detail = $this->BackOrderService->getOrderDetail(['back_order_id' => $id], ['apply_num']);
        $total_apply_num = array_sum(array_column($detail, 'apply_num')); // 总申请发货数量
        if (!in_array($order['status'], [PublicCode::back_order_status_already_delivery, PublicCode::back_order_status_already_sign])) {
            $table_type = 0;// 未发货
            $logisticsList = collect($this->LogisticsService->getLogisticsConfigByWId($order['w_id']));
            $logistics_list = $logisticsList->where('type', 2)->pluck('name', 'id')->toArray();
            $express_list = $logisticsList->where('type', 1)->pluck('name', 'id')->toArray();
            $packList = $this->BackOrderService->packList(1, 0, 0, [
                'task_type' => 2,
                'source_no' => $order['serial_no'],
                'status' => 1
            ]);
            $l_info = [
                'send_box' => count($packList['data']),
                'send_num' => array_sum(array_column($packList['data'], 'pk_num'))
            ];
            $l_info['diff_num'] = $total_apply_num - $l_info['send_num'];
        } else {
            $table_type = 1;// 已发货
            $logistics = $this->LogisticsInfoService->getLogisticsInfo(['rela_no' => $order['serial_no']]);
            $l_info = [
                'send_box' => $logistics['send_box'],
                'send_num' => $logistics['send_num'],
                'diff_num' => $total_apply_num - $logistics['send_num'],
                'express_name' => $this->LogisticsService->getLogistics(['id' => $logistics['express_id']])['name'] ?? '',
                'express_code' => $logistics['express_code'],
                'freight_price' => fen2yuan($logistics['freight_price']),
                'express_time' => $logistics['express_time'],
                'remark' => $logistics['remark'],
                'image_list' => $logistics['image_list'] ? json_decode($logistics['image_list'], true) : [],
            ];
        }

        // 操作记录
        $log = $this->MessageLogService->getLogList(['system' => 'wms', 'model_name' => 'BackOrder', 'op_id' => $info['id']]);
        $log_list = $log['data'] ? array_column($log['data'], 'res_params') : [];

        // 出库任务 决定货品信息表头不同
        $outStoreInfo = $this->OutStoreService->getOutStore([
            'order_no' => $info['serial_no'],
            'status_list' => PublicCode::out_store_status_valid
        ]);
        $detail_table_type = 1;
        if ($outStoreInfo) {
            $detail_table_type = 2;
        }

        return $this->show('backOrder/detail', [
            'info' => $info,
            'l_table_type' => $table_type,
            'l_info' => $l_info,
            'logistics_list' => $logistics_list,
            'express_list' => $express_list,
            'log' => $log_list,
            'detail_table_type' => $detail_table_type
        ]);
    }

    /**
     * 货品信息
     * @RequestMapping(path="/backOrder/detailList", methods="get,post")
     */
    public function detailList()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();

            $order = $this->BackOrderService->getOrder(['serial_no' => $params['serial_no']], ['id']);
            if (!$order) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '单据不存在');
            }

            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $export = (int)$params['export'] ?? 0;// 0列表 1导出

            // 查-出库任务
            $outStoreInfo = $this->OutStoreService->getOutStore([
                'order_no' => $params['serial_no'],
                'status_list' => PublicCode::out_store_status_valid
            ]);

            if (!$outStoreInfo) { // 无匹配店内码/条码数据
                $table_type = 1;

                $search['back_order_id'] = $order['id'];
                $list = $this->BackOrderService->detailList($export, (int)$page, (int)$pageLimit, $search);

                if ($list['data']) {
                    $spuInfo = $this->getSpuInfo(array_column($list['data'], 'spu_id'));
                    foreach ($list['data'] as &$item) {
                        $item['brand_name'] = $spuInfo[$item['spu_id']]['brand_name'];
                        $item['category_name'] = $spuInfo[$item['spu_id']]['category'];
                        $item['apply_num'] = $item['apply_num']+$item['defective_num'];
                    }
                }
            } else { // 有匹配店内码/条码数据
                $table_type = 2;

                $search['out_store_id'] = $outStoreInfo['id'];
                if ($export == 1) {
                    $list = $this->OutStoreService->detailGroupList($search, 0);
                } else {
                    $list = $this->OutStoreService->detailGroupList($search, (int)$page, (int)$pageLimit);
                }
                if ($list['data']) {
                    foreach ($list['data'] as &$item) {
                        $item['sign_type'] = $item['unique_code'] ? '店内码' : '条码';
                        $item['content'] = $item['unique_code'] ? $item['unique_code'] : $item['barcode'];
                    }
                }
            }

            if ($export == 1) {
                if (!$list['data']) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }
                $url = exportToExcel($this->exportDetailHeader($table_type), $list['data'], '退返任务单明细');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }
    }

    private function exportDetailHeader($type)
    {
        if ($type == 1) {
            return [
                'brand_name' => '品牌',
                'category_name' => '品类',
                'sku_id' => 'SKU',
                'apply_num' => '任务数',
            ];
        } else {
            return [
                'brand_name' => '品牌',
                'category_name' => '品类',
                'sku_id' => 'SKU',
                'sign_type' => '类型',
                'content' => '店内码/条形码',
                'num' => '任务数',
                'pack_num' => '装箱数',
                'send_num' => '发货数'
            ];
        }
    }

    private function getSpuInfo(array $spuIds)
    {
        $data = [];

        $spuList = $this->SpuService->getSpus(['ids' => $spuIds], ['id', 'brand_id', 'category_id']);

        // 品牌
        $brandIds = array_unique(array_column($spuList, 'brand_id'));
        $brandList = $this->BrandService->getBrands(['ids' => $brandIds], ['id', 'name']);
        $spuBrand = array_column($spuList, 'brand_id', 'id');// spu_id=>brand_id
        $brandName = $brandList ? array_column($brandList, 'name', 'id') : [];// brand_id=>brand_name

        // 分类
        $categoryIds = array_unique(array_column($spuList, 'category_id'));
        $categoryList = $this->CategoryService->getCategory(['category_ids' => $categoryIds]);
        $spuCategory = array_column($spuList, 'category_id', 'id');// spu_id=>category_id
        $categoryName = $categoryList ? array_column($categoryList, 'name', 'id') : [];// category_id=>category_name

        foreach ($spuIds as $spuId) {
            $data[$spuId] = [
                'brand_name' => $brandName[$spuBrand[$spuId]] ?? '',
                'category' => $categoryName[$spuCategory[$spuId]] ?? '',
            ];
        }

        return $data;
    }

    /**
     * 装箱信息
     * @RequestMapping(path="/backOrder/getPackList", methods="get,post")
     */
    public function getPackList()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['task_type'] = $params['task_type'] ?? 2;// 退返任务
            $search['source_no'] = $params['serial_no'];// 退返单号
            $export = intval($params['export']?? 0) ;// 0列表 1导出
            $search['status'] = [1, 2, 3];
            $search['is_quality'] = $params['is_quality'];

            if(empty($search['box_no']) || (is_array($search['box_no']) && empty(array_filter($search['box_no'])))) unset($search['box_no']);//去空

            //处理status条件
            if(isset($params['status']) && $params['status'] != '') $search['status'] = $params['status'];

            $order = $this->BackOrderService->getOrder(['serial_no' => $params['serial_no']]);
            if (!$order) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '退返不存在');
            }

            $list = $this->BackOrderService->packList($export, (int)$page, (int)$pageLimit, $search);
            if ($list['data']) {
                foreach ($list['data'] as &$item) {
                    if ($item['status'] == 0 && empty($item['lg_id'])) $item['status_text'] = '已作废';
                    if ($item['status'] == 1 && empty($item['lg_id'])) $item['status_text'] = '已装箱';
                    if ($item['status'] == 2 && !empty($item['lg_id'])) $item['status_text'] = '已发货';
                    if ($item['status'] == 3) $item['status_text'] = '发货中';
                    //箱号已发货，单据未发货 = 发货中
//                    if ($item['status'] == 2 && !in_array($order['status'], [PublicCode::back_order_status_already_delivery, PublicCode::back_order_status_already_sign])) {
//                        $item['status_text'] = '发货中';
//                    }
                    $item['quality_text'] = PublicCode::goods_packing_quality[$item['is_quality']];

                    //重量保留一位小数
                    $item['weight'] = sprintf("%.1f",$item['weight']);

                    //品牌展示
                    $item['tips'] = '';
                    if(!empty($item['brand_names'])){
                        $brandNames = explode(",",$item['brand_names']);
                        $item['tips'] = $brandNames[0];
                        if (count($brandNames) > 1) {
                            $item['tips'].= "等多品牌";
                        }
                    }

                    //已发货不允许修改称重
                    $item['useWeight'] = 1;
                    if($order['status'] >= PublicCode::back_order_status_already_delivery){
                        $item['useWeight'] = 0;
                    }
                }
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit, 'already_out_quality' => $list['already_out_quality'], 'no_quality' => $list['no_quality'], 'already_num' => $list['already_num']]);
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 上传图片
     * @RequestMapping(path="/backOrder/upload", methods="get,post")
     */
    public function upload()
    {
        $file = $this->request->file('file')->toArray();
        if (!$file) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '未接收到图片');
        }

        $upload_path = 'runtime/upload/backOrder/';
        $image_name = $file['name'];
        $image_path = $file['tmp_file'];
        if (!is_dir($upload_path)) {
            @mkdir($upload_path, 0777, true);
        }
        $save_path = $upload_path . $image_name;

        move_uploaded_file($image_path, $save_path);

        $uniqueName = md5(time() . mt_rand(1, 10000));
        $up_res = ossUpload($uniqueName . '.' . pathinfo($file['name'])['extension'], $save_path, 4);
        if (!$up_res) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '上传失败');
        }

        return $this->returnApi(ResponseCode::SUCCESS, '上传成功', ['url' => $up_res]);
    }

    /**
     * 发货登记
     * @RequestMapping(path="/backOrder/logisticsAdd", methods="get,post")
     */
    public function logisticsAdd()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $rule = [
                'id' => ['required'],
                'logistics_id' => ['required', 'integer'],
                'logistics_no' => ['required', 'string'],
                'send_time' => ['required', 'string'],
                'freight_price' => ['string'],
                'remark' => ['string'],
                'image' => ['array']
            ];
            $errors = $this->validator->make($params, $rule);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }
            if (yuan2fen($params['freight_price']) > 100000000) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '运费超额');
            }

            $order = $this->BackOrderService->getOrder(['id' => $params['id']]);
            if (!$order) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '不存在');
            }
            if ($order['status'] != PublicCode::back_order_status_wait_out) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无发货中数据！');
            }

            $sendData = $this->BackOrderService->checkIsDiffDelivery($order['serial_no']);
            $userInfo = $this->session->get('userInfo');

            $back_delivery = [
                'express_id' => $params['logistics_id'],
                'express_code' => $params['logistics_no'],
                'delivery_diff' => $sendData['is_diff'],
                'freight_price' => $params['freight_price'] ? yuan2fen($params['freight_price']) : 0,
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
                'send_num' => $sendData['send_num'],
                'send_box' => $sendData['send_box_num'],
                'remark' => $params['remark'],
                'express_time' => $params['send_time'],
                'image_list' => $params['image'] ? json_encode($params['image']) : ''
            ];
            // 差异发货
            $back_delivery_info = [];
            if ($back_delivery['delivery_diff'] == 1) {
                $back_delivery_info = $sendData['data'];
            }

            try {
                $logistics_id = $this->ListService->listDelivery(1, (int)$params['id'], [
                    'back_delivery' => $back_delivery,
                    'out_data' => $sendData['out_data'] ?? [],
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'back_delivery_info' => $back_delivery_info
                ], 'wms');
                if ($logistics_id) {
                    $logData = [
                        'snow_id' => $this->request->getAttribute('snow_id'),
                        'op_id' => $params['id'],
                        'status' => PublicCode::back_order_status_already_delivery,
                        'admin_id' => $userInfo['uid'],
                        'admin_name' => $userInfo['nickname'],
                        'handle_time' => date('Y-m-d H:i:s'),
                        'handle_type' => '完成发货',
                        'remark' => "备注：{$params['remark']}"
                    ];
                    wlog((string)$logData['snow_id'], $logData);

                    // 店内码流程日志
                    $sendUqs = $this->GoodsPackingService->getSendUqByLogisticsId($logistics_id);
                    if ($sendUqs) {
                        foreach ($sendUqs as $code) {
                            $uqLineLog = [
                                'unique_code' => $code,
                                'operation_type' => PublicCode::UNIQUE_CODE_LOG_TYPE_MAP['back_delivery'], // 退返单发货
                                'admin_id' => $userInfo['uid'],
                                'admin_name' => $userInfo['nickname'],
                                'operation_desc' => "退返单号：{$order['serial_no']}；发货备注{$params['remark']}",
                                'operation_time' => date('Y-m-d H:i:s'),
                                'snow_id' => newSnowId()
                            ];
                            addUniqueCodeLog($uqLineLog['snow_id'], $uqLineLog);
                        }
                    }
                }
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 生成出库任务 - wms接单
     * @RequestMapping(path="/backOrder/outStoreTask", methods="get,post")
     */
    public function outStoreTask()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            if (!isset($params['serial_no']) || !$params['serial_no']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
            }

            $order = $this->BackOrderService->getOrder(['serial_no' => $params['serial_no']]);
            if (!$order) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '不存在');
            }

            // 出库单创建成功，退返单状态由待接单更为待出库，出库单作废时，暂不会更新退款单的状态
            // 则退返单的，待接单、待出库都可点击生成出库任务
            if (!in_array($order['status'], [PublicCode::back_order_status_wait_take, PublicCode::back_order_status_wait_out])) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '状态不符合，无法操作');
            }
            // 出库任务
            $outStoreInfo = $this->OutStoreService->getOutStore([
                'order_no' => $params['serial_no'],
                'status_list' => PublicCode::out_store_status_valid
            ]);
            if ($outStoreInfo) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '已有生效中的出库任务');
            }

            $userInfo = $this->session->get('userInfo');

            try {

                // 更新退返单状态为待出库
                if ($order['status'] == PublicCode::back_order_status_wait_take) {
                    $res = $this->BackOrderService->updateOrder(
                        ['serial_no' => $params['serial_no']],
                        ['status' => PublicCode::back_order_status_wait_out]
                    );
                    if (!$res) {
                        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '更新状态失败');
                    }
                }

                //退返类型 1采购退返 2差异退返 3供应商退返 4临期退返 5残次退返
                //单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
                $typeMap = [
                    2 => 21,
                    1 => 22,
                    3 => 22,
                    4 => 22,
                    5 => 22
                ];

                if (!isset($typeMap[$order['back_type']])) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '返回类型不支持创建出库单！');
                }
                $params = [
                    'serial_no' => $params['serial_no'],
                    'admin_id' => $userInfo['uid'],
                    'type' => $typeMap[$order['back_type']],
                    'admin_name' => $userInfo['nickname']
                ];
                $res = OutStoreService::add($params);
                if (!$res) {
                    $this->returnApi(ResponseCode::VALIDATE_ERROR, '生成出库任务失败');
                }

                $logData = [
                    'snow_id' => $this->request->getAttribute('snow_id'),
                    'op_id' => $order['id'],
                    'status' => PublicCode::back_order_status_wait_out,
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'handle_time' => date('Y-m-d H:i:s'),
                    'handle_type' => '接单',
                    'remark' => "接单了，待出库装箱发货"
                ];
                wlog((string)$logData['snow_id'], $logData);

            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 发货详情
     * @RequestMapping(path="/backOrder/express", methods="get,post")
     */
    public function express()
    {
        $id = (int)$this->request->input('id');
        $boxIdStr = $this->request->input('box_id');
        $taskType = $this->request->input('task_type',2);
        if (empty($boxIdStr)) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误！');
        }
        $boxIds = explode(",", $boxIdStr);
        logger()->info('发货箱号', [$boxIds]);
        if($taskType == 2){
            $order = $this->BackOrderService->getOrder(['id' => $id]);
            if (!$order) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '不存在');
            }
            $wId = $order['w_id'];
        }else{
            $order = AllotService::info($id);
            if (empty($order)){
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '任务单不存在');
            }
            $wId = $order['info']['out_w_id'];
        }


        //箱号数据
        $packList = $this->BackOrderService->packList(1, 0, 0, [
            'task_type' => $taskType,
            'ids' => $boxIds,
            'status' => 1
        ]);

        $boxData = collect($packList['data']);
        if ($boxData->isEmpty()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '箱号不存在或已作废！');
        }

        $doubleStatus = $boxData->pluck('status')->unique()->count();
        if ($doubleStatus > 1) {
            $boxErrorNos = $boxData->where('status', '!=', 1)->unique('box_no')->implode('box_no');
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '箱号' . $boxErrorNos . '已作废不允许操作！');
        }

        logger()->info('箱号数据', [$packList]);
        $logisticsList = collect($this->LogisticsService->getLogisticsConfigByWId($wId));
        $logistics_list = $logisticsList->where('type', 2)->pluck('name', 'id')->toArray();
        $express_list = $logisticsList->where('type', 1)->pluck('name', 'id')->toArray();
        return $this->show('/backOrder/express', [
            'logistics_list' => $logistics_list,
            'express_list' => $express_list,
            'id' => $id,
            'task_type' => $taskType,
            'box_ids' => $boxIdStr
        ]);
    }

    /**
     * 发货详情
     * @RequestMapping(path="/backOrder/boxDelivery", methods="get,post")
     */
    public function boxDelivery()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $rule = [
                'id' => ['required'],
                'express_id' => ['integer'],
                'express_code' => ['string'],
                'express_time' => ['required', 'string'],
                'logistics_price' => ['string'],
                'remark' => ['string'],
                'image' => ['array']
            ];
            $errors = $this->validator->make($params, $rule);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }
            if (yuan2fen($params['logistics_price']) > 100000000) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '运费超额');
            }

            $order = $this->BackOrderService->getOrder(['id' => $params['id']]);
            if (!$order) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '不存在');
            }
            if ($order['status'] != PublicCode::back_order_status_wait_out) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无发货中数据!');
            }

            $boxIdStr = $this->request->input('box_ids');
            if (empty($boxIdStr)) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '箱号参数错误！');
            }
            $boxIds = explode(",", $boxIdStr);
            logger()->info('发货箱号', [$boxIds]);
            //箱号数据
            $packList = $this->BackOrderService->packList(1, 0, 0, [
                'task_type' => 2,
                'ids' => $boxIds,
                'status' => 1
            ]);

            $boxData = collect($packList['data']);
            if ($boxData->isEmpty()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '箱号不存在或已作废！');
            }

            $doubleStatus = $boxData->pluck('status')->unique()->count();
            if ($doubleStatus > 1) {
                $boxErrorNos = $boxData->where('status', '!=', 1)->unique('box_no')->implode('box_no', "、");
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '箱号' . $boxErrorNos . '已作废不允许操作！');
            }

            $boxMaps = $boxData->keyBy('id');
            $userInfo = $this->session->get('userInfo');

            $backDeliverys = [];
            foreach ($boxIds as $boxId) {
                $backDelivery = [
                    'rela_no' => $order['serial_no'],
                    'rela_type' => 2,
                    'box_id' => $boxId,
                    'send_type' => $params['send_type'],
                    'express_id' => !empty($params['express_id']) ? $params['express_id'] : 0,
                    'express_code' => $params['express_code']??0,
                    'delivery_diff' => 0,
                    'freight_price' => $params['logistics_price'] ? yuan2fen($params['logistics_price']) : 0,
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'send_num' => $boxMaps[$boxId]['pk_num'] ?? 0,
                    'send_box' => 1,
                    'remark' => $params['remark'],
                    'express_time' => $params['express_time'],
                    'image_list' => $params['image'] ? json_encode($params['image']) : ''
                ];

                $backDeliverys[] = $backDelivery;
            }

            try {
                $this->BackOrderService->boxDelivery($backDeliverys);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 发货记录
     * @RequestMapping(path="/backOrder/getBoxDeliveryList", methods="get,post")
     */
    public function getBoxDeliveryList()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['task_type'] = $params['task_type'] ?? 2;// 退返任务
            $search['source_no'] = $params['serial_no'];// 退返单号
            $export = $params['export'] ?? 0;// 0列表 1导出
            $search['status'] = [1, 2,3];
            if (!empty($params['box_no'])) $search['box_no'] = array_filter($params['box_no']);

//            $order = $this->BackOrderService->getOrder(['serial_no' => $params['serial_no']]);
//            if (!$order) {
//                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '退返不存在');
//            }

            $list = $this->BackOrderService->boxDeliveryList($export, (int)$page, (int)$pageLimit, $search);
            if ($list['data']) {
                foreach ($list['data'] as &$item) {
                    $item['quality_text'] = PublicCode::goods_packing_quality[$item['is_quality']];
                    $item['send_type_text'] = PublicCode::logistics_company_type[$item['type']];
                }
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 发货作废
     * @RequestMapping(path="/backOrder/boxVoidDelivery", methods="get,post")
     */
    public function boxVoidDelivery()
    {
        if ($this->isAjax()) {
            $id = $this->request->input('id');
            $boxIdStr = $this->request->input('box_id');
            if (empty($boxIdStr)) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '箱号参数错误！');
            }

            $boxIds = explode(",", $boxIdStr);

            $order = $this->BackOrderService->getOrder(['id' => $id]);
            if (!$order) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '退返不存在');
            }

            //检测状态
            if ($order['status'] >= PublicCode::back_order_status_already_delivery) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '退返单已发货、不允许操作！');
            }

            try {
                $this->BackOrderService->boxVoidDelivery($boxIds);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }
            return $this->returnApi(ResponseCode::SUCCESS, '作废成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 箱号质检
     * @RequestMapping(path="/backOrder/boxQualitys", methods="get,post")
     */
    public function boxQualitys()
    {
        if ($this->isAjax()) {
            $id = $this->request->input('id');
            $boxNos = $this->request->input('boxNos');
            if (empty($boxNos)) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '箱号参数错误！');
            }

            $order = $this->BackOrderService->getOrder(['id' => $id]);
            if (!$order) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '退返不存在');
            }

            //检测状态
            if ($order['status'] >= PublicCode::back_order_status_already_delivery) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '退返单已发货、不允许操作！');
            }

            //箱号数据
            $packList = $this->BackOrderService->packList(1, 0, 0, [
                'task_type' => 2,
                'box_no' => $boxNos,
                #'status' => [1,2]
            ]);

            $boxData = collect($packList['data']);
            $boxDataMap = [];
            if ($boxData->isNotEmpty()) {
                $boxDataMap = $boxData->keyBy('box_no');
            }

            $yesNum = 0;
            $wrongNum = 0;
            $data = [];
            $qualitys = [];
            $isCheck = [];
            try {
                foreach ($boxNos as $key => $boxNo) {
                    $item['box_no'] = $boxNo;
                    $item['pk_num'] = 0;
                    $item['status'] = 0;
                    if (isset($boxDataMap[$boxNo])) {
                        $item['pk_num'] = $boxDataMap[$boxNo]['pk_num'];
                        //if ($boxDataMap[$boxNo]['status'] == 2) $item['text'] = '箱号已发货';
                        if ($boxDataMap[$boxNo]['status'] == -1) $item['text'] = '箱号已作废';

                        if ($boxDataMap[$boxNo]['source_no'] != $order['serial_no']) $item['text'] = '箱号不属于此退返单';
                        if ($boxDataMap[$boxNo]['is_quality'] == 1) $item['text'] = '箱号已质检';
                    } else {
                        $item['text'] = '箱号不属于此退返单';
                    }

                    if (isset($isCheck[$boxNo])) {
                        $item['text'] = '箱号输入重复';
                    }

                    if (!empty($item['text'])) $wrongNum++;

                    if (empty($item['text'])) {
                        $item['status'] = 1;
                        $item['text'] = '无异常';
                        $qualitys[] = $boxNo;
                        $yesNum++;
                    }
                    $isCheck[$boxNo] = true;
                    $data[$key] = $item;
                    unset($item);
                }

                $this->BackOrderService->addQualitys($qualitys);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }
            return $this->returnApi(ResponseCode::SUCCESS, '质检成功', ['yesNum' => $yesNum, 'wrongNum' => $wrongNum, 'data' => $data, 'total' => count($boxNos)]);
        }
        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 退返发货完成
     * @RequestMapping(path="/backOrder/backFinishDelivery", methods="get,post")
     */
    public function backFinishDelivery()
    {
        $params = $this->request->all();
        $rule = [
            'id' => ['required']
        ];
        $errors = $this->validator->make($params, $rule);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }

        $order = $this->BackOrderService->getOrder(['id' => $params['id']]);
        if (!$order) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '不存在');
        }

        if ($order['status'] != PublicCode::back_order_status_wait_out) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无发货中数据!');
        }

        //箱号数据
        $packList = $this->BackOrderService->packList(1, 0, 0, [
            'task_type' => 2,
            'source_no' => $order['serial_no'],
            'status' => [1, 2, 3]
        ]);

        $boxData = collect($packList['data']);
        if ($boxData->isEmpty()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无退返装箱数据！');
        }

        $boxNoDelivery = $boxData->where('status',1)->count();
        if(!empty($boxNoDelivery)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '存在未发货的箱号！');
        }
        $userInfo = $this->session->get('userInfo');

        $back_delivery_info = [];
        $back_delivery = [];
        try {
            $sendData = $this->BackOrderService->checkIsDiffDelivery($order['serial_no']);
            if(!empty($sendData['is_diff'])){
                $back_delivery_info = $sendData['data'];
            }
            $result = $this->ListService->listDelivery(1, (int)$params['id'], [
                'back_delivery' => $back_delivery,
                'out_data' => $sendData['out_data'] ?? [],
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
                'back_delivery_info' => $back_delivery_info,
                'delivery_diff' => $sendData['is_diff']
            ], 'wms');
            if ($result) {
                $logData = [
                    'snow_id' => $this->request->getAttribute('snow_id'),
                    'op_id' => $params['id'],
                    'status' => PublicCode::back_order_status_already_delivery,
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'handle_time' => date('Y-m-d H:i:s'),
                    'handle_type' => '完成发货',
                    'remark' => "备注：{$params['remark']}"
                ];
                wlog((string)$logData['snow_id'], $logData);

                //处理装箱店内码日志
                $boxData->pluck('lg_id')->unique()->map(function ($lg_id) use ($userInfo,$order,$params){
                    // 店内码流程日志
                    $sendUqs = $this->GoodsPackingService->getSendUqByLogisticsId($lg_id);
                    if ($sendUqs) {
                        foreach ($sendUqs as $code) {
                            $uqLineLog = [
                                'unique_code' => $code,
                                'operation_type' => PublicCode::UNIQUE_CODE_LOG_TYPE_MAP['back_delivery'], // 退返单发货
                                'admin_id' => $userInfo['uid'],
                                'admin_name' => $userInfo['nickname'],
                                'operation_desc' => "退返单号：{$order['serial_no']}",
                                'operation_time' => date('Y-m-d H:i:s'),
                                'snow_id' => newSnowId()
                            ];
                            addUniqueCodeLog($uqLineLog['snow_id'], $uqLineLog);
                        }
                    }
                });

                //发送邮件
//                $data = $this->generateMailData($params['id'], 1);
//                $sendMail = [
//                    'title' => "好超值公司退返完成发货（" . $order['serial_no'] . "）",
//                    'task_type' => 1,
//                    'info' => $data,
//                ];
//                $this->sendRealMail($sendMail);
            }
        } catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
    }

    /**
     * 箱号称重
     * @RequestMapping(path="/backOrder/pickWeight", methods="get,post")
     */
    public function pickWeight()
    {
        $params = $this->request->all();
        $rule = [
            'pk_id' => ['required'],
            'weight' => ['required']
        ];
        $errors = $this->validator->make($params, $rule);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }

        $userInfo = $this->session->get('userInfo');
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        //$params['weight'] = $params['weight']*2;
        try {
            $this->BackOrderService->pickWeight($params);
        } catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
        return $this->returnApi(ResponseCode::SUCCESS, '称重成功！');
    }

    /**
     * 退返作废
     * @RequestMapping(path="/backOrder/backVoid", methods="get,post")
     */
    public function backVoid($params)
    {
        $params = $this->request->all();
        $rule = [
            'id' => ['required']
        ];
        $errors = $this->validator->make($params, $rule);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }

        $order = $this->BackOrderService->getOrder(['id' => $params['id']]);
        if (!$order) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '不存在');
        }

        if ($order['status'] == -1) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '改单据已作废');
        }

        if ($order['status'] > PublicCode::back_order_status_wait_out) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '单据状态不允许作废');
        }

        try {
            $userInfo = $this->session->get('userInfo');
            $params['admin_id'] = $userInfo['uid'];
            $params['admin_name'] = $userInfo['nickname'];
            $this->BackOrderService->backVoid((int)$params['id'],$params);
        } catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
        return $this->returnApi(ResponseCode::SUCCESS, '作废成功');
    }

    /**
     * 发送邮件
     * @RequestMapping(path="/backOrder/email", methods="get,post")
     */
    public function email()
    {
        $idStr = $this->request->input('ids');
        $taskType = $this->request->input('task_type', 1);
        if (empty($idStr)) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '请选择要发送的单据！');
        }
        $data = $this->generateMailData($idStr, $taskType);
        return $this->show('/backOrder/email', [
            'data' => $data,
            'task_type' => $taskType,
            'task_title' => $cacheData['title'] ?? '',
            'images' => empty($cacheData['images'] ) ? new \stdClass() : $cacheData['images'],
        ]);
    }

    /**
     * 缓存邮件数据
     * @RequestMapping(path="/backOrder/cacheEmail", methods="get,post")
     */
    public function cacheEmail()
    {
        $params = $this->request->all();
        $params['task_type'] = $params['task_type'] ?? 1;
        $cacheKey = CachePre::getKey(CachePre::EMAIL_CACHE_DATA,$this->getUserId(),$params['task_type']);

        if(!empty($params)){
            $timeout = strtotime(date('Y-m-d 23:59:59')) - time();
            redis()->set($cacheKey,json_encode($params,JSON_UNESCAPED_UNICODE),$timeout);
        }

        return $this->returnApi(ResponseCode::SUCCESS, '保存成功');
    }

    /**
     * 发送邮件
     * @RequestMapping(path="/backOrder/sendEmail", methods="get,post")
     */
    public function sendEmail()
    {
        $params = $this->request->all();
        if (empty($params['title'])) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '请输入邮件标题！');
        }

        if (empty($params['info'])) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '邮件明细不存在！');
        }

        if (!in_array($params['task_type'],[1,2])) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '任务类型有误！');
        }

        //发送邮件
        $result = $this->sendRealMail($params);

        return $this->returnApi(ResponseCode::SUCCESS, sprintf('发送成功(%s/%s)%s', $result['sendNum'], $result['mailCount'], $result['errorMsg']));
    }


    /**
     * 拆分出发送邮件逻辑
     * @param $params
     * @return array
     * @throws \RedisException
     */
    private function sendRealMail($params)
    {
        $title = $params['title'];
        $data = collect($params['info']);
        $destMails = [];
        $wMails = [];

        //查询收件人
        $destIds = $data->pluck('dest_id')->unique()->toArray();
        $mailTpl = 'emails/back/send';
        $outType = 22;
        if ($params['task_type'] == 1){ //退返
            $wIds = $data->pluck('w_id')->unique()->toArray();
            $wNotifyMails = $this->WarehouseService->getWarehouses(['ids' => $wIds], ['id', 'name', 'email', 'notify_mail']);
            $idNameMap = collect($wNotifyMails)->keyBy('id')->toArray();
            $wName = $wNotifyMails[0]['name'] ?? '';
            $title = "BIGOFFS" . $wName . (count($wIds) > 1 ? '等' : '') . "-" . $title;
            $supplierNotifyMails = $this->SupplierService->getSuppliers(['ids' => $destIds], ['id','name','email','notify_mail']);
            foreach ($supplierNotifyMails as $supplierNotifyMail) {
                if (!empty($supplierNotifyMail['notify_mail'])) {
                    $mails = explode(",", $supplierNotifyMail['notify_mail']);
                    $destMails[$supplierNotifyMail['id']]['mailto'] = array_shift($mails);
                    $destMails[$supplierNotifyMail['id']]['org_name'] = $supplierNotifyMail['name'];
                    $destMails[$supplierNotifyMail['id']]['cc'] = $mails;
                }else{
                    throw new BusinessException(sprintf('供应商%s邮件为空',$supplierNotifyMail['name']));
                }
            }

            //查询抄送人
            foreach ($wNotifyMails as $wNotifyMail) {
                if (!empty($wNotifyMail['notify_mail'])) {
                    $wMails[$wNotifyMail['id']]['mailto'] = [];//$wNotifyMail['email'];
                    $wMails[$wNotifyMail['id']]['cc'] = explode(",", $wNotifyMail['notify_mail']) ?? [];
                }else{
                    throw new BusinessException(sprintf('仓库%s邮件为空',$wNotifyMail['name']));
                }
            }
        }else{
            $mailTpl = 'emails/allot/send';
            $outType = 23;
            //查询抄送人
            $wIds = $data->pluck('w_id')->unique()->toArray();
            $wNotifyMails = $this->WarehouseService->getWarehouses(['ids' => array_merge($wIds,$destIds)], ['id','name','email','notify_mail']);
            foreach ($wNotifyMails as $wNotifyMail) {
                if (empty($wNotifyMail['notify_mail'])) {
                    throw new BusinessException(sprintf('仓库%s邮件为空',$wNotifyMail['name']));
                }
                if(in_array($wNotifyMail['id'],$destIds)){
                    $mails = explode(",", $wNotifyMail['notify_mail']);
                    $destMails[$wNotifyMail['id']]['mailto'] = array_shift($mails) ?? [];
                    $destMails[$wNotifyMail['id']]['org_name'] = $wNotifyMail['name'];
                    $destMails[$wNotifyMail['id']]['cc'] = $mails ?? [];
                }

                if(in_array($wNotifyMail['id'],$wIds)){
                    $wMails[$wNotifyMail['id']]['mailto'] = [];//$wNotifyMail['email'];
                    $wMails[$wNotifyMail['id']]['cc'] = explode(",", $wNotifyMail['notify_mail']) ?? [];
                }
            }
        }


        //获取附件信息
        $serialNos = $data->pluck('serial_no')->unique()->toArray();
        $sendDetails = $this->OutStoreService->getSendMailAnnexDetail($serialNos, $outType);

        $mailList = [];
        foreach ($data as $item ){
            if(!isset($mailList[$item['dest_id']])){
                $mailList[$item['dest_id']] = [
                    'to_email' => $destMails[$item['dest_id']]['mailto'] ,
                    'cc' => array_unique(array_merge((is_array($wMails[$item['w_id']]['mailto']) ? $wMails[$item['w_id']]['mailto'] : [$wMails[$item['w_id']]['mailto']]), $wMails[$item['w_id']]['cc'], $destMails[$item['dest_id']]['cc'])),
                    'total' => 0,
                    'data' => [],
                    'serial_nos' => [],
                    'detailList' => []
                ];
            }
            $item['org_name'] = $destMails[$item['dest_id']]['org_name'];
            $item['w_name'] = isset($idNameMap[$item['w_id']]) ? $idNameMap[$item['w_id']]['name'] : '';
            $mailList[$item['dest_id']]['total'] += $item['num'];
            $mailList[$item['dest_id']]['box_total'] += $item['box_num'];
            $mailList[$item['dest_id']]['data'][] = $item;
            $mailList[$item['dest_id']]['serial_nos'][] = $item['serial_no'];
            $mailList[$item['dest_id']]['detailList'] = array_merge($mailList[$item['dest_id']]['detailList'],$sendDetails[$item['serial_no']]);
            $sendDetails[$item['serial_no']] = [];
        }
        //发送邮件
        logger()->debug('发送邮件数据', [$data, $destMails, $wMails,$mailList,$title]);
        $sendNum = 0;
        $errorSend = [];
        foreach ($mailList as $sendItem){
            if(!empty($sendItem['detailList'])){
                if($params['task_type'] == 1){
                    $fileUrl = exportToExcel([
                        'batch_serial_no'=>'退返父单号',
                        'order_no' => '退返子单号',
                        'unique_code' => '店内码',
                        'barcode' => '条形码',
                        'send_num' => '数量',
                        'w_name' => '仓库名称',
                        'brand_name' => '品牌',
                        'spu_no' => '货号',
                        'packag_size' => '尺码',
                        'box_no' => '箱号',
                        'name' => '物流公司',
                        'express_code' => '物流单号',
                        'tag_price' => '吊牌价',
                        'cost_price' => '成本价',
                        'express_time' => '发货时间',
                    ],$sendItem['detailList']);
                }else{
                    $fileUrl = exportToExcel([
                        'order_no'=>'单据编号',
                        'unique_code' => '店内码',
                        'barcode' => '条形码',
                        'send_num' => '数量'],$sendItem['detailList']);
                }
            }

            $sendParam = [];
            $sendParam['subject'] = $title;
            $sendParam['to_email'] = $sendItem['to_email'];
            $sendParam['cc'] = $sendItem['cc'];
            $sendParam['tpl'] = $mailTpl;
            $sendParam['data'] = [
                'total' => $sendItem['total'],
                'box_total' => $sendItem['box_total'],
                'file_url' => $fileUrl ?? '',
                'data' => $sendItem['data'],
                'images' => $params['images'] ?? [],
            ];
            $result = SendService::email($sendParam);
            if (isset($result['success']) && $result['success'] == true) {
                $sendNum ++;
            }else{
                $errorSend[] = array_merge($errorSend,$sendItem['serial_nos']);
            }
        }
        $errorMsg = '';
        if(!empty($errorSend)){
            $errorMsg = '发送失败单号:'.implode(',',$errorSend);
        }else{
            $cacheKey = CachePre::getKey(CachePre::EMAIL_CACHE_DATA,$this->getUserId(),$params['task_type']);
            redis()->del($cacheKey);
        }
        return ['sendNum' => $sendNum, 'mailCount' => count($mailList), 'errorMsg' => $errorMsg];
    }

    /**
     * 生成邮件预览数据
     * @param $idStr
     * @param $taskType
     * @return \Hyperf\Utils\Collection|\Illuminate\Support\Collection|\think\Collection
     * @throws \RedisException
     */
    private function generateMailData($idStr,$taskType = 1)
    {
        $cacheKey = CachePre::getKey(CachePre::EMAIL_CACHE_DATA."1", $this->getUserId(), $taskType);
        $cacheData = redis()->get($cacheKey);
        logger()->info('email cache', [$cacheKey, $cacheData]);
        if (!empty($cacheData)) {
            $cacheData = json_decode($cacheData, true);
            $existsList = collect($cacheData['info'] ?? [])->keyBy('serial_no');
        } else {
            $existsList = collect();
        }
        // 状态
        $ids = explode(",", $idStr);

        if ($taskType == 1) {//退返
            $list = $this->BackOrderService->list(1, 1, count($ids), ['ids' => $ids, 'status_list' => array_keys(PublicCode::back_order_status)]);
            logger()->debug('退返信息', [$list]);
        } else {//调拨
            $list = $this->allotService->list(0, 0, ['id' => $ids]);
        }

        $data = collect($list['data'])->map(function ($item) use ($existsList, $taskType) {
            $existItem = $existsList->get($item['serial_no']) ?? [];
            if (!empty($item['logistics_detail'])) {
                $logisticsLists = [];
                $logisticsDetails = collect($item['logistics_detail'])->groupBy('express_code');
                foreach ($logisticsDetails as $destNo => $logisticsDetail) {
                    if(!empty($destNo)){
                        $logisticsLists[] = [
                            'id' => $item['id'],
                            'serial_no' => $item['serial_no'],
                            'dest_no' => $destNo,
                            'brand_names' => $item['brand_names'],
                            'num' => intval($existItem['num']) ? $existItem['num'] : $logisticsDetail->sum('send_num'),
                            'box_num' => intval($existItem['box_num']) ? $existItem['box_num'] : $logisticsDetail->sum('send_box'),
                            'remark' => empty($existItem['remark']) ? '' : $existItem['remark'],
                            'w_id' => $taskType == 1 ? $item['w_id'] : $item['out_w_id'],
                            'dest_id' => $taskType == 1 ? $item['sup_id'] : $item['in_w_id']
                        ];
                    }else{
                        $logisticsDetailRemarks = $logisticsDetail->groupBy('remark');
                        foreach ($logisticsDetailRemarks as $remark => $detailRemark){
                            $logisticsLists[] = [
                                'id' => $item['id'],
                                'serial_no' => $item['serial_no'],
                                'dest_no' => $remark,
                                'brand_names' => $item['brand_names'],
                                'num' => intval($existItem['num']) ? $existItem['num'] : $logisticsDetail->sum('send_num'),
                                'box_num' => intval($existItem['box_num']) ? $existItem['box_num'] : $logisticsDetail->sum('send_box'),
                                'remark' => empty($existItem['remark']) ? '' : $existItem['remark'],
                                'w_id' => $taskType == 1 ? $item['w_id'] : $item['out_w_id'],
                                'dest_id' => $taskType == 1 ? $item['sup_id'] : $item['in_w_id']
                            ];
                        }
                    }
                }
                return $logisticsLists;
            }
        })->collapse();
        return $data;
    }

}