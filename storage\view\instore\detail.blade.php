<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/layui/css/layui.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/static/css/myui.css"/>
    <link href="/static/css/pearCommon.css" rel="stylesheet"/>
    <script src="/static/layui/layui.js" charset="utf-8"></script>
    <script src="/static/js/jquery.min.js"></script>
</head>
<style>
    .clear{ clear:both}
</style>

<body class="pear-container">
<div class="layui-card">
    <hr class="layui-bg-gray">
    <div class="layui-card-body">
        @if($info['type'] == 1)
            <div class="layui-row" style="padding: 0px 20px 0px 20px;">
                <div class="layui-col-xs4 layui-col-sm4 layui-col-md3">
                    <div class="layui-row">
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;text-align: right;">批次号:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['in_stock_no']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;text-align: right;">仓库:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['w_name']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;text-align: right;">状态:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['status_name']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;text-align: right;">负责人:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['relaInfo']['admin_name']}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs4 layui-col-sm4 layui-col-md3">
                    <div class="layui-row">
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">采购单号:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['order_no']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">供应商:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['sup_name']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">采购模式:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['relaInfo']['co_model_name']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;font-weight: bold;">三方发货单号:</label>
                            <div class="layui-input-inline" style="line-height: 36px;font-weight: bold; font-size: 16px;">
                                {{$info['relaInfo']['third_party_no']}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs4 layui-col-sm4 layui-col-md3">
                    <div class="layui-row">
                        <div class="layui-col-xs6 layui-col-sm6 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">完成时间:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['finish_at']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">入库方式:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['batch_type_name']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">入库数量:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['final_num']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">预约到货门店:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['allot_w_name']}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs4 layui-col-sm4 layui-col-md3">
                    <div class="layui-row">
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label layui-col-sm4" style="width: 80px !important;" >备注信息:</label>
                            <div class="layui-input-inline layui-col-sm8" style="padding-top: 8px;">
                                {{$info['arrivalInfo']['remark']??'无'}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label layui-col-sm4" style="width: 80px !important;" >入库类型:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                @if($info['in_type'] == 1)
                                    质检装箱入库
                                @else
                                    wms上传入库
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
        @if($info['type'] == 3)
            <div class="layui-row" style="padding: 20px;">
                <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                    <div class="layui-row">
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;" >入库单号:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['serial_no']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">发货仓库:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['relaInfo']['out_w_name']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">状态:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['status_name']}}
                            </div>
                        </div>
                        <div class="layui-col-xs6 layui-col-sm6 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">创建时间:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['created_at']}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                    <div class="layui-row">
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;" >调拨单号:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['order_no']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">收货仓库:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['relaInfo']['in_w_name']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">收货人:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['relaInfo']['linkman']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">收货时间:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['relaInfo']['in_at']}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                    <div class="layui-row">
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label  class="layui-form-label layui-col-sm4" style="width: 80px !important;">运费承担:</label>
                            <div class="layui-input-inline layui-col-sm8" style="line-height: 36px;">
                                {{$info['relaInfo']['payer']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label  class="layui-form-label layui-col-sm4" style="width: 80px !important;">收货信息:</label>
                            <div class="layui-input-inline layui-col-sm8" style="line-height: 36px;">
                                {{$info['relaInfo']['linkman']}} {{$info['relaInfo']['phone']}} {{$info['relaInfo']['address']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label layui-col-sm4" style="width: 80px !important;" >备注信息:</label>
                            <div class="layui-input-inline layui-col-sm8" style="padding-top: 8px;">
                                {{$info['relaInfo']['remark']??'无'}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        @if($info['type'] == 6)
            <div class="layui-row" style="padding: 20px;">
                <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                    <div class="layui-row">
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;" >入库单号:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['serial_no']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">发货仓库:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['relaInfo']['out_w_name']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">差异数量:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['relaInfo']['diff_num']}}
                            </div>
                        </div>
                        <div class="layui-col-xs6 layui-col-sm6 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">创建时间:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['created_at']}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                    <div class="layui-row">
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;" >调拨单号:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['relaInfo']['allot_no']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">收货仓库:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['relaInfo']['in_w_name']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">责任方:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['relaInfo']['w_name']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label class="layui-form-label" style="width: 100px !important;">完成时间:</label>
                            <div class="layui-input-inline" style="line-height: 36px;">
                                {{$info['relaInfo']['checked_at']}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                    <div class="layui-row">
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label  class="layui-form-label layui-col-sm4" style="width: 80px !important;">调拨差异单号:</label>
                            <div class="layui-input-inline layui-col-sm8" style="line-height: 36px;">
                                {{$info['relaInfo']['allot_diff_no']}}
                            </div>
                        </div>
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <label  class="layui-form-label layui-col-sm4" style="width: 80px !important;">收货信息:</label>
                            <div class="layui-input-inline layui-col-sm8" style="line-height: 36px;">
                                {{$info['order_no']}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
    <hr class="layui-bg-gray">
    <div class="layui-tab" lay-filter="tab-all">
        <ul class="layui-tab-title">
            <li class="layui-this">入库信息</li>
            <li @if($info['type'] != 1) style="display: none" @endif >差异信息</li>
            <li>操作记录</li>
        </ul>

        <div class="layui-tab-content">
            <div class="layui-tab-item layui-show">
                <form class="layui-form" action="">
                    <div class="layui-form-item">
                        <div class="layui-row">
                            <input type="hidden" id="in_store_id" name="in_store_id" value="{{$info['id']}}">
                            <div class="layui-col-md5">
                                <div class="layui-input-inline">
                                    <select name="search_type" lay-filter="search_type">
                                        <option value="">请选择查询方式</option>
                                        <option value="1" selected="">店内码查询</option>
                                        <option value="3">条形码查询</option>
                                        <option value="5">SKU查询</option>
                                    </select>
                                </div>
                                <div class="layui-input-inline">
                                    <input type="text" id="search_value" name="search_value" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-btn-group">
                                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="query">
                                        <i class="layui-icon layui-icon-search"></i>
                                        查询
                                    </button>
                                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="export">
                                        <i class="layui-icon layui-icon-export"></i>
                                        导出
                                    </button>
                                </div>
                            </div>
                            <div class="layui-col-md4">
                                <div class="layui-btn-group layui-layout-right">
                                    @if($info['status'] !== -1)
                                        @if($info['status'] == 1)
                                            @if ($info['in_type'] == 0)
                                                <button type="button" style="margin-right: 50px" class="pear-btn pear-btn-md layui-btn-disabled  import_btn" disabled data-id="{{$info['id']}}">货品入库</button>
                                            @endif
                                            <button type="button" style="margin-right: 50px" class="pear-btn pear-btn-md layui-btn-disabled cancel_btn" disabled data-id="{{$info['id']}}" >作废入库批次</button>
                                            <button type="button" class="pear-btn pear-btn-md layui-btn-disabled  finish_btn" disabled data-id="{{$info['id']}}">完成入库</button>
                                        @endif

                                        @if($info['status'] == 0)
                                            @if ($info['in_type'] == 0)
                                                <button type="button" style="margin-right: 50px" class="pear-btn pear-btn-md pear-btn-primary  import_btn" data-id="{{$info['id']}}">货品入库</button>
                                            @endif
                                            <button type="button" style="margin-right: 50px" class="pear-btn pear-btn-md pear-btn-danger cancel_btn" data-id="{{$info['id']}}" >作废入库批次</button>
                                            <button type="button" class="pear-btn pear-btn-md pear-btn-primary  finish_btn" data-id="{{$info['id']}}">完成入库</button>
                                        @endif
                                    @endif
                                </div>
                            </div>
                        </div>

                    </div>
                </form>
                <table class="layui-hide" id="detail_list"></table>
            </div>
            <div class="layui-tab-item">
                <form class="layui-form" action="">
                    <div class="layui-form-item">
                        <div class="layui-row">
                            <input type="hidden" id="in_store_id" name="in_store_id" value="{{$info['id']}}">
                            <div class="layui-col-md5">
                                <div class="layui-input-inline">
                                    <select name="diff_type" lay-filter="diff_type">
                                        <option value="">请选择差异类型</option>
                                        <option value="1">多到货</option>
                                        <option value="2">未采购</option>
                                        <option value="3">无商品信息</option>
                                    </select>
                                </div>
                                <div class="layui-input-inline">
                                    <input type="text" id="search_code" name="search_code" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-btn-group">
                                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="queryDiff">
                                        <i class="layui-icon layui-icon-search"></i>
                                        查询
                                    </button>
                                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="exportDiff">
                                        <i class="layui-icon layui-icon-export"></i>
                                        导出
                                    </button>
                                </div>
                            </div>
                            <div class="layui-col-md4">
                                <div class="layui-btn-group layui-layout-left">
                                    @if($info['status'] !== -1)
                                        <button type="button" style="margin-right: 50px" class="pear-btn pear-btn-md pear-btn-danger batchChangeBarcode" data-id="{{$info['id']}}" >批量变更条码</button>
                                    @endif
                                </div>
                                <div class="layui-btn-group layui-layout-right">
                                    @if($info['status'] !== -1)
                                        <button type="button" style="margin-right: 50px" class="pear-btn pear-btn-md pear-btn-danger changeBarcode" data-id="{{$info['id']}}" >变更条码</button>
                                    @endif
                                </div>
                            </div>
                        </div>

                    </div>
                </form>
                <table class="layui-hide" id="diff_list"></table>
            </div>
            <div class="layui-tab-item">
                <form class="layui-form" action="">
                    <div class="layui-form-item">
                        <div class="layui-row" style="height: 34px;">
                            <div class="layui-col-md5 layui-col-md-offset7">
                                <div class="layui-btn-group layui-layout-right">
                                    @if($info['status'] == 1)
                                        @if ($info['in_type'] == 0)
                                        <button type="button" style="margin-right: 50px" class="pear-btn pear-btn-md layui-btn-disabled  import_btn" disabled data-id="{{$info['id']}}">货品入库</button>
                                        @endif
                                        <button type="button" style="margin-right: 50px" class="pear-btn pear-btn-md layui-btn-disabled cancel_btn" disabled data-id="{{$info['id']}}" >作废入库批次</button>
                                        <button type="button" class="pear-btn pear-btn-md layui-btn-disabled  finish_btn" disabled data-id="{{$info['id']}}">完成入库</button>
                                    @endif

                                    @if($info['status'] == 0)
                                        @if ($info['in_type'] == 0)
                                        <button type="button" style="margin-right: 50px" class="pear-btn pear-btn-md pear-btn-primary  import_btn" data-id="{{$info['id']}}">货品入库</button>
                                        @endif
                                        <button type="button" style="margin-right: 50px" class="pear-btn pear-btn-md pear-btn-danger cancel_btn" data-id="{{$info['id']}}" >作废入库批次</button>
                                        <button type="button" class="pear-btn pear-btn-md pear-btn-primary  finish_btn" data-id="{{$info['id']}}">完成入库</button>
                                    @endif
                                </div>
                            </div>
                        </div>

                    </div>
                </form>
                <div class="layui-row">
                    <table class="layui-table">
                        <thead>
                        <tr>
                            <th>序号</th>
                            <th>操作类型</th>
                            <th>操作内容</th>
                            <th>操作人</th>
                            <th>操作时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($log_list as $key => $log)
                            <tr>
                                <td>{{$key+1}}</td>
                                <td>{{$log['req_router_name'] ?? ''}}</td>
                                <td>{{$log['remark'] ?? ''}}</td>
                                <td>{{$log['admin_name'] ?? ''}}</td>
                                <td>{{$log['req_time'] ?? ''}}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>

            </div>
            <div class="clear"></div>
        </div>
    </div>
</div>

<form style="display:none" id="downArrivalProduct" action="/arrivalOrder/down_arrival_product" method="post">
</form>

<script>
    layui.use(['element', 'table','iframeTools'], function () {
        // Tab的切换功能，切换事件监听等，需要依赖element模块
        var $ = layui.jquery, table = layui.table, element = layui.element;
        var form = layui.form;
        var layer = layui.layer;
        var arrival_id = $('#arrival_id').val();
        var iframeTools = layui.iframeTools;
        var batch_type = {{$info["batch_type"] ?? 0}};

        $('#logistics').on('click', function(){
            iframeTools.layer_iframe('到货登记', '/LogisticsInfo/index?id='+arrival_id+'&rela_type=3');
        });

        table.render({
            elem: '#detail_list'
            ,url:'/instore/detail_list?in_store_id={{$info["id"]}}'
            ,cols: [[
                {type:'numbers', width:80, title: 'ID'}
                ,{field:'unique_code', width:80, title: '店内码'}
                ,{field:'barcode', title: '条形码'}
                ,{field:'sku_id',title: 'sku'}
                ,{field:'brand_name', title: '品牌'}
                ,{field:'category_name', title: '品类'}
                ,{field:'num', width:80, title: '数量'}
                ,{field:'shelf_code', width:80, title: '货架位'}
                ,{field:'operate_name',  title: '操作人'}
            ]]
            ,page: true
        });

        table.render({
            elem: '#diff_list'
            ,url:'/instore/diff_list?in_store_id={{$info["id"]}}'
            ,cols: [[
                {type: 'checkbox', fixed: 'left'},
                {type:'numbers', width:80, title: 'ID'}
                ,{field:'unique_code', width:130, title: '店内码'}
                ,{field:'id', hide:true }
                ,{field:'barcode', title: '条形码'}
                ,{field:'old_barcode', title: '原始条形码'}
                ,{field:'sku_id',title: 'sku'}
                ,{field:'brand_name', title: '品牌'}
                ,{field:'category_name', title: '品类'}
                ,{field:'num', width:80, title: '数量'}
                ,{field:'diff_num',width:100, title: '差异数量'}
                ,{field:'shelf_code',  title: '货架位'}
                ,{field:'diff_type', hide: true}
                ,{field:'diff_type_name', width:110, title: '差异类型'}
                ,{field:'operate_name',  title: '操作人'}
            ]]
            ,page: true
            ,done:function(res, curr, count){
                let table_data = res.data
                let trNum = count;
                if (res.data){
                    for(let i = 0;i<res.data.length;i++){
                        let diff_type = res.data[i].diff_type;
                        if(diff_type != 3){
                            var index = res.data[i]['LAY_TABLE_INDEX'];
                            $("div[lay-id='diff_list'] .layui-table tr[data-index="+index+"] input[type='checkbox']").prop('disabled',true);
                            $("div[lay-id='diff_list'] .layui-table tr[data-index="+index+"] input[type='checkbox']").next().addClass('layui-btn-disabled');
                            $("div[lay-id='diff_list'] .layui-table tr[data-index=' + index + '] input[type='checkbox']").prop('name', 'eee');
                        }
                    }
                }
            }
        });
        //查询
        form.on('submit(query)', function (data) {
            table.reload('detail_list', {where: data.field,
                page: {
                    curr: 1
                }})
            return false;
        });

        //查询
        form.on('submit(queryDiff)', function (data) {
            table.reload('diff_list', {where: data.field,
                page: {
                    curr: 1
                }})
            return false;
        });

        //查询
        form.on('select(search_type)', function (data) {
            $("#search_value").val('');
        });

        //导出
        form.on('submit(export)', function (data) {
            $.post('/instore/detail_export', data.field, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    layer.alert(r.msg, function(index){
                        layer.close(index);
                        window.open(r.data.url);
                    });
                } else {
                    layer.alert(r.msg);
                }
                loading.hide();
            });
            return false;
        });

        //导出
        form.on('submit(exportDiff)', function (data) {
            $.post('/instore/diff_export', data.field, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    layer.alert(r.msg, function(index){
                        layer.close(index);
                        window.open(r.data.url);
                    });
                } else {
                    layer.alert(r.msg);
                }
                loading.hide();
            });
            return false;
        });
        $('.cancel_btn').click(function () {
            $(this).addClass("layui-btn-disabled").attr("disabled",true);
            $.post('/instore/cancel?id='+$(this).attr('data-id'), {}, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    layer.alert(r.msg, function(index){
                        layer.close(index);
                        location.reload();
                    });
                } else {
                    layer.alert(r.msg);
                }
                loading.hide();
            }).always(function(){
                $('.cancel_btn').removeClass("layui-btn-disabled").attr("disabled",false);
            });
        })

        $('.batchChangeBarcode').click(function () {
            layer.open({
                title: '批量条码变更',
                type: 2,
                area: ['70%', '50%'],
                content: "/instore/changeImportPage?id={{$info["id"]}}&batch_type="+batch_type,
                end: function () {
                    //刷新表单
                    table.reload('diff_list', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        }
                    });
                    return false;
                }
            });
        })

        $('.changeBarcode').click(function () {
            var checkStatus = table.checkStatus('diff_list');
            var data = checkStatus.data;
            var codes = [];
            console.log(data);
            var contentUrl = "";
            if( batch_type == 2){
                $.each(data, function (k, v) {
                    codes.push(v['barcode']);
                })
                if (codes.length == 0) {
                    layer.msg('请选择需要变更条码的数据', {icon: 2});
                    return false;
                }
                var barcodeStr = codes.join(',');
                contentUrl = "/instore/changePage?id={{$info["id"]}}&barcodes="+barcodeStr;
            }else if( batch_type == 1){
                $.each(data, function (k, v) {
                    codes.push(v['id']);
                })
                if (codes.length == 0) {
                    layer.msg('请选择需要变更条码的数据', {icon: 2});
                    return false;
                }
                var codeStr = codes.join(',');
                contentUrl = "/instore/changeUniqueCodePage?id={{$info["id"]}}&ids="+codeStr;
            }else{
                layer.msg('无法获取入库方式', {icon: 2});
                return false;
            }

            layer.open({
                title: '条码变更',
                type: 2,
                area: ['70%', '80%'],
                content: contentUrl,
                end: function () {
                    //刷新表单
                    table.reload('diff_list', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        }
                    });
                    return false;
                }
            });
        })

        $('.finish_btn').click(function () {
            $(this).addClass("layui-btn-disabled").attr("disabled",true);
            var id = $(this).attr('data-id');
            var transfer = {{$info["is_transfer"]??0}};
            var finalNum = {{$info['final_num']??0}};//入库数量
            var diffNum = {{$info['diff_num']??0}};//差异数量
            var rate = {{@round(($info["diff_num"]/$info['final_num'])*100,2)}};
            var msg = "";
            if (finalNum > 0) {
                if (transfer == 1) {
                    msg = '新品和未采购差异只能入库到当前仓无法调拨入【{{$info["allot_w_name"] ?? ''}}】，对应数量【{{$info['diff_num']}}】，请再次确认本操作 ！！'
                } else {
                    if (diffNum > 0) {
                        msg = '不在这个采购单中的货品差异数量 {{$info["diff_num"] ?? 0}} ，占比 ' + rate + '% ，请再次确认本操作 ！！'
                    }
                }
            }
            //提示不为空 切入库数>0
            if (msg && finalNum > 0) {
                layer.confirm(msg, {
                    btn: ['确认', '取消'] //可以无限个按钮
                }, function (index) {
                    //按钮【按钮一】的回调
                    console.log("确认！")
                    $.post('/instore/finish?id='+id, {}, function (r) {
                        var loading = $(this).find('.layui-icon');
                        if (loading.is(":visible")) return;
                        loading.show();
                        if (r.code === 200) {
                            layer.alert(r.msg, function(index){
                                layer.close(index);
                                location.reload();
                            });
                        } else {
                            layer.alert(r.msg);
                        }

                        loading.hide();
                    }).always(function(){
                        $('.finish_btn').removeClass("layui-btn-disabled").attr("disabled",false);
                    });
                    return;
                }, function (index) {
                    //按钮【按钮二】的回调
                    console.log("取消！")
                    $('.finish_btn').removeClass("layui-btn-disabled").attr("disabled",false);
                    return;
                });
            }else{
                $.post('/instore/finish?id='+id, {}, function (r) {
                    var loading = $(this).find('.layui-icon');
                    if (loading.is(":visible")) return;
                    loading.show();
                    if (r.code === 200) {
                        layer.alert(r.msg, function(index){
                            layer.close(index);
                            location.reload();
                        });
                    } else {
                        layer.alert(r.msg);
                    }

                    loading.hide();
                }).always(function(){
                    $('.finish_btn').removeClass("layui-btn-disabled").attr("disabled",false);
                });
            }
        })


        $('.import_btn').click(function () {
            // iframeTools.addTab('货品入库', '/instore/import?id=' + $(this).attr('data-id'));
            var id = $(this).attr('data-id');
            var url = '/instore/import?id=' + id;

            layer.open({
                type: 2,
                title: '货品入库',
                fix: false,
                maxmin: true,
                area: ['100%', '100%'],
                content: url,
                cancel: function (index, layero) {
                    // 关闭时刷新当前页面
                    location.reload();
                }
            });
        })
    })

    $(function () {
        //下载货品明细
        $('#down_arrival').click(function () {
            var arrival_id = $('#arrival_id').val();
            var $formArrival = $("#downArrivalProduct");
            $formArrival.append($('<input name = "arrival_id" value = "' + arrival_id + '" />'));
            $formArrival.submit();
        })
    })
</script>
</body>

</html>
