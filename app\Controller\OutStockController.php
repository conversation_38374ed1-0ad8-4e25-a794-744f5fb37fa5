<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Exception\BusinessException;
use App\Library\Facades\AdminService;
use App\Library\Facades\InStoreService;
use App\Library\Facades\OutStoreService;
use App\Library\Facades\SyncTaskService;
use App\Library\Facades\UniqueCodeService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use App\Library\RedisLock;
use App\Constants\CachePre;
use Hyperf\HttpServer\Contract\RequestInterface;
use App\JsonRpc\UniqueCodeLogServiceInterface;
use Hyperf\Validation\Rule;
use function foo\func;
use function Symfony\Component\HttpFoundation\isSecure;
use App\Amqp\Producer\AchievementProducer;
use App\Library\Facades\AmqpProducer;

/**
 * @Controller()
 */
class OutStockController extends AbstractController
{
    /**
     * @Inject()
     * @var \App\JsonRpc\WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var \App\JsonRpc\PickOrderServiceInterface
     */
    private $PickOrderService;

    /**
     * @Inject()
     * @var \App\JsonRpc\BrandServiceInterface
     */
    private $BrandService;

    /**
     * @Inject()
     * @var \App\JsonRpc\CategoryServiceInterface
     */
    private $CategoryService;

    /**
     * @Inject()
     * @var \App\Service\HashService
     */
    private $HashService;

    /**
     * @Inject()
     * @var \App\JsonRpc\AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject()
     * @var \App\JsonRpc\OutStoreServiceInterface
     */
    private $OutStoreService;

    /**
     * @Inject()
     * @var \App\JsonRpc\ShelfGoodsCodeMapServiceInterface
     */
    private $ShelfGoodsCodeMapService;

    /**
     * @Inject()
     * @var \App\JsonRpc\UniqueCodeServiceInterface
     */
    private $UniqueCodeService;

    /**
     * @Inject()
     * @var \App\JsonRpc\ShelfServiceInterface
     */
    private $ShelfService;

    /**
     * @Inject()
     * @var \App\JsonRpc\SkuServiceInterface
     */
    private $SkuService;

    /**
     * @Inject ()
     * @var UniqueCodeLogServiceInterface
     */
    private $UniqueCodeLogService;

    //状态 0=待出库，1=出库中，2=已完成，-1=作废
    private $status = [
        '0' => '待出库',
        '1' => '出库中',
        '2' => '已完成',
        '-1' => '作废',
    ];

    private $search_type = [
        '1' => '店内码查找',
        '2' => '货号查找',
        '3' => '条形码查询',
        '4' => 'SPU查询',
        '5' => 'SKU查询',
    ];

    private $out_type = [
        '1' => '店内码级',
        '2' => '条形码级'
    ];

    private $searchTypeMap = [
        '1' => 'unique_code',
        '2' => 'spu_no',
        '3' => 'barcode',
        '4' => 'spu_id',
        '5' => 'sku_id',
    ];
    //单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
    private $taskTypeMap = [
        '25' => '销售任务',
        '22' => '退返任务',
        '28' => '拍照任务',
        '23' => '调拨任务',
        '27' => '配送报损任务',
        '21' => '差异调整单-出',
        '31' => '报损任务单-出',
    ];

    const PICK_GROUP = [
        1 => '门店',
        2 => '来源单号',
        3 => '任务单号',
        4 => '性别',
        5 => '品牌',
    ];

    const PICK_TYPE = [
        1 => 'RFID分拣',
        2 => '店内码分拣',
    ];

    const PICK_REPLACE = [
        1 => "是",
        0 => "否"
    ];

    /**
     * @RequestMapping(path="/outStock/add", methods="get,post")
     * @return mixed
     */
    public function add(RequestInterface $request)
    {
        if ($this->isAjax()) {
            $params = $this->validate($request->all(), 'add');

            $userInfo = $this->getUserInfo();

            try {
                $params['admin_id'] = $userInfo['uid'];
                $params['admin_name'] = $userInfo['nickname'];
                $info = OutStoreService::add($params);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }
            $data = [];
            $data['op_id'] = $info['id'];
            $data['req_router_name'] = '创建任务单';
            $data['snow_id'] = strval($request->getAttribute('snow_id'));
            wlog($data['snow_id'], $data);
            return $this->returnApi(ResponseCode::SUCCESS, '创建成功', $info);
        }
    }

    /**
     * @RequestMapping(path="/outStock/batchAdd", methods="get,post")
     * @return mixed
     */
    public function batchAdd(RequestInterface $request)
    {
        if ($this->isAjax()) {
            $requestParams = $this->validate($request->all(), 'batchAdd');

            $userInfo = $this->getUserInfo();

            try {
                $requestParams['admin_id'] = $userInfo['uid'];
                $requestParams['admin_name'] = $userInfo['nickname'];
                $requestParams['snow_id'] = strval($request->getAttribute('snow_id'));

                $params = [];
                $params['service_name'] = 'out-store:batchAdd';
                $params['admin_id'] = $userInfo['uid'];
                $params['admin_name'] = $userInfo['nickname'];
                $params['task_name'] = '出库任务批量创建';
                $params['sys_type'] = 'wms';
                $params['params'] = json_encode([$requestParams]);
                $taskInfo = SyncTaskService::add($params,60);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }
            return $this->returnApi(ResponseCode::SUCCESS, '批量创建提交成功，任务将异步处理，执行结果请查看异步列表！',$taskInfo);
        }
    }

    /**
     * @RequestMapping(path="/outStock/detail", methods="get,post")
     * @return mixed
     */
    public function detail(RequestInterface $request)
    {
        $data['title'] = "任务单明细";
        // 存在则返回，不存在则返回默认值 null
        $id = $request->input('id');
        $info = OutStoreService::info($id);

        if (empty($info)) {
            $data['error_info'] = "任务单不存在";
        } else {
            $data['info'] = $info;
        }
        $logInfo = getLog(['model_name' => 'OutStock', 'op_id' => $id]);
        $data['log_list'] = $logInfo['data'] ?? [];
        $data['exception_list'] = PublicCode::OUT_STORE_EXCEPTION_REMARK;//异常原因
        logger()->debug('detail_data', ['data' => $data]);
        return $this->show('outStock/detail', $data);
    }

    /**
     * 详情页列表
     * @Inject()
     * @var \App\JsonRpc\OrderServiceInterface
     */
    private $OrderService;

    /**
     * @RequestMapping(path="/outStock/index", methods="get,post")
     * @return mixed
     */
    public function index()
    {
        $data['title'] = '货品出库';
        return $this->show('outStock/index', $data);
    }

    /**
     * @RequestMapping(path="/outStock/detail_list", methods="get,post")
     * @return mixed
     */
    public function detailList(RequestInterface $request){
        $where = $this->validate($request->all(),'detail_search');
        $page = intval($request->input('page',1));
        $limit = intval($request->input('limit',30));

        if (!empty($where['search_type']) && array_key_exists($where['search_type'],$this->search_type)){
            $searchKey = $this->searchTypeMap[$where['search_type']];
            $where[$searchKey] = $where['search_value'];
        }
        unset($where['search_type']);
        unset($where['search_value']);

        $list = OutStoreService::OutDetailList($where,$page ,$limit);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
    }

    /**
     * @RequestMapping(path="/outStock/detail_export", methods="get,post")
     * @return mixed
     */
    public function detailExport(RequestInterface $request)
    {
        $where = $this->validate($request->all(),'detail_search');

        if (!empty($where['search_type']) && array_key_exists($where['search_type'],$this->search_type)){
            $searchKey = $this->searchTypeMap[$where['search_type']];
            $where[$searchKey] = $where['search_value'];
        }
        unset($where['search_type']);
        unset($where['search_value']);

        $list = OutStoreService::OutDetailList($where,0);
        if($list['data']){
            try {
                $url = exportToExcel(config('file_header.outstore_detail_export'),$list['data'],'入库详情');
            }catch (\Exception $e){
                throw new BusinessException('导出失败！',ResponseCode::SERVER_ERROR,$e);
            }
        }else{
            throw new BusinessException('无数据，导出失败！');
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }
    /**
     * 详情页列表
     * @RequestMapping(path="/outStock/blockInfo", methods="get,post")
     * @return mixed
     */
    public function blockInfo(RequestInterface $request)
    {
        $params = $this->validate($request->all(), 'blockInfo');
        $list = OutStoreService::blockInfo($params['out_store_ids']);
        if (!$list) {
            throw new BusinessException('无法获取货品信息!');
        }
        $list['auto_pick_people'] = implode("\r\n", $list['auto_dispatch_workers'] ?? []);
        unset($list['auto_dispatch_workers']);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list);
    }

    /**
     * @RequestMapping(path="/outStock/list", methods="get,post")
     * @return mixed
     */
    public function list(RequestInterface $request)
    {
        $data['title'] = '货品出库';
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        //获取仓库信息
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds], ['id', 'name']);

        if ($this->isAjax()) {
            $where = $this->validate($request->all(), 'search');
            logger()->debug('list_where', $where);
            $page = intval($request->input('page', 1));
            $limit = intval($request->input('limit', 30));

            if (!empty($where['date_range'])) {
                $dateRangeArray = explode(' - ', $where['date_range']);
                $where['start_time'] = $dateRangeArray[0];
                $where['end_time'] = $dateRangeArray[1];
            }
            if (!empty($where['search_type']) && array_key_exists($where['search_type'], $this->search_type)) {
                $searchKey = $this->searchTypeMap[$where['search_type']];
                $where[$searchKey] = $where['search_value'];

            }
            if (!empty($where['warehouse_ids'])) {
                $where['w_id'] = explode(',', $where['warehouse_ids']);
                unset($where['warehouse_ids']);
            }else{
                $where['w_id'] = $wIds;
            }

            if (!empty($where['serial_no'])){
                $serialNos = explode("\n",$where['serial_no']);
                $where['serial_no'] = [];
                foreach ($serialNos as $item){
                    $serial_no = trim($item);
                    if(!empty($serial_no)){
                        $where['serial_no'][] = $serial_no;
                    }
                }
                if(empty($where['serial_no'])){
                    unset($where['serial_no']);
                }
            }else{
                unset($where['serial_no']);
            }

            if (!empty($where['order_no'])){
                $serialNos = explode("\n",$where['order_no']);
                $where['order_no'] = [];
                foreach ($serialNos as $item){
                    $serial_no = trim($item);
                    if(!empty($serial_no)){
                        $where['order_no'][] = $serial_no;
                    }
                }
                if(empty($where['order_no'])){
                    unset($where['order_no']);
                }
            }else{
                unset($where['order_no']);
            }

            if (!empty($where['export_num'])) {
                $where['export'] = 1;
            }
            if (!empty($where['exception_num'])) {
                $where['exception'] = 1;
            }
            unset($where['date_range']);
            unset($where['search_type']);
            unset($where['search_value']);
            unset($where['exception_num']);
            unset($where['export_num']);
            $list = OutStoreService::list($page, $limit, $where);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
        }

        $data = [
            'warehouse_list' => $warehouse_list,
            'status_list' => $this->status,
            'search_type' => $this->search_type,
            'pick_type' => self::PICK_TYPE,
            'pick_group' => self::PICK_GROUP,
            'task_type' => $this->taskTypeMap,
            'pick_replace' => self::PICK_REPLACE
        ];

        return $this->show('outStock/list', $data);
    }

    /**
     * @RequestMapping(path="/outStock/export", methods="get,post")
     * @return mixed
     */
    public function export(RequestInterface $request)
    {
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $where = $this->validate($request->all(), 'search');
        logger()->debug('list_where', $where);
        $page = intval($request->input('page', 1));
        $limit = intval($request->input('limit', 30));

        if (!empty($where['date_range'])) {
            $dateRangeArray = explode(' - ', $where['date_range']);
            $where['start_time'] = $dateRangeArray[0];
            $where['end_time'] = $dateRangeArray[1];
        }
        if (!empty($where['search_type']) && array_key_exists($where['search_type'], $this->search_type)) {
            $searchKey = $this->searchTypeMap[$where['search_type']];
            $where[$searchKey] = $where['search_value'];

        }
        if (!empty($where['warehouse_ids'])) {
            $where['w_id'] = explode(',', $where['warehouse_ids']);
            unset($where['warehouse_ids']);
        }else{
            $where['w_id'] = $wIds;
        }

        if (!empty($where['export_num'])) {
            $where['export'] = 1;
        }
        if (!empty($where['exception_num'])) {
            $where['exception'] = 1;
        }
        unset($where['date_range']);
        unset($where['search_type']);
        unset($where['search_value']);
        unset($where['exception_num']);
        unset($where['export_num']);
        $list = OutStoreService::list(0,0, $where);
        if($list['data']){
            try {
                $url = exportToExcel(config('file_header.outstore_export'),$list['data'],'调拨详情');
            }catch (\Exception $e){
                throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
            }
        }else{
            throw new BusinessException('无数据，导出失败！');
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);

    }

    /**
     * @RequestMapping(path="/outStock/cancel", methods="get,post")
     * @return mixed
     */
    public function cancel(RequestInterface $request)
    {
        $id = $request->input('id');
        if (!$id) {
            throw new BusinessException('任务id不能为空');
        }
        $result = OutStoreService::cancel($id);
        if ($result) {
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        } else {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '操作失败');
        }
    }

    /**
     * @RequestMapping(path="/outStock/finish", methods="get,post")
     * @return mixed
     */
    public function finish(RequestInterface $request){
        $id = $request->input('id');
        if(!$id){
            throw new BusinessException('任务id不能为空');
        }
        $result = OutStoreService::finish($id);
        if($result){
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }else{
            return $this->returnApi(ResponseCode::SERVER_ERROR, '操作失败');
        }
    }

    /**
     * @RequestMapping(path="/outStock/secondOut", methods="get,post")
     * @return mixed
     */
    public function secondOut(RequestInterface $request){
        $id = $request->input('out_store_id');
        if(!$id){
            throw new BusinessException('任务id不能为空');
        }
        $result = OutStoreService::secondOut($id);
        if($result){
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }else{
            return $this->returnApi(ResponseCode::SERVER_ERROR, '操作失败');
        }
    }

    /**
     * 根据类型获取需要出库的单据信息
     * @RequestMapping(path="/outStock/get_out_info", methods="get,post")
     * @param RequestInterface $request
     */
    public function get_out_info(RequestInterface $request)
    {
        $params = $request->all();
        if (!isset($params['pick_order']) || empty($params['pick_order'])) {
            return $this->returnApi('501', '单号不能为空');
        }
        $data = [];
        if ($params['out_stock_type'] == 'pick_order') {#拣货单
            //清空缓存
            $wait_redis_key = 'pick_order_wait_' . $params['pick_order'];
            $checked_redis_key = 'pick_order_checked' . $params['pick_order'];
            $error_redis_key = 'pick_order_error' . $params['pick_order'];
            $out_redis_key = 'pick_order_out' . $params['pick_order'];
            //判断key是否存在
            if ($this->HashService->keyIsExist($wait_redis_key)) {
                $this->HashService->del($wait_redis_key);
            }
            if ($this->HashService->keyIsExist($checked_redis_key)) {
                $this->HashService->del($checked_redis_key);
            }
            if ($this->HashService->keyIsExist($error_redis_key)) {
                $this->HashService->del($error_redis_key);
            }
            if ($this->HashService->keyIsExist($out_redis_key)) {
                $this->HashService->del($out_redis_key);
            }

            //获取拣货单信息
            $pick_order_detail = $this->PickOrderService->getPickOrderBySerialNoDetail($params['pick_order'], ['id', 'serial_no', 'w_id', 'status']);
            if (empty($pick_order_detail)) {
                return $this->returnApi('501', '拣货单不存在');
            }
            if (!in_array($pick_order_detail['status'], [1, 2])) {#检测拣货单是否为拣货中状态
                return $this->returnApi('501', '拣货单状态不符合出库要求');
            }
            //获取拣货单商品明细
            $pick_detail_where = [
                'pick_order_id' => $pick_order_detail['id'],
                'status' => 1
            ];
            $pick_order_detail_list = $this->PickOrderService->getPickOrderDetailLists($pick_detail_where);
            $data['wait_num'] = 0;
            $data['wait_list'] = [];
            if (!empty($pick_order_detail_list) && isset($pick_order_detail_list['data']) && !empty($pick_order_detail_list['data'])) {
                //获取品牌品类
                $brand_ids = array_unique(array_column($pick_order_detail_list['data'], 'brand_id'));
                $category_ids = array_unique(array_column($pick_order_detail_list['data'], 'category_id'));
                $brand_info = $category_info = [];
                if (!empty($brand_ids)) {
                    $brand_list = $this->BrandService->getBrands(['ids' => $brand_ids], ['id', 'name']);
                    $brand_info = array_column($brand_list, 'name', 'id');// brand_id=>brand_name
                }
                if (!empty($category_ids)) {
                    $category_list = $this->CategoryService->getCategory(['category_ids' => $category_ids]);
                    $category_info = array_column($category_list, 'name', 'id');
                }

                $wait_list = [];
                foreach ($pick_order_detail_list['data'] as $detail) {
                    if ($detail['pick_type'] == 1) {#条形码
                        if (!isset($wait_list[$detail['barcode']])) {
                            if ($detail['already_pick_num'] < $detail['pick_num']) {
                                $nums = $detail['pick_num'] - $detail['already_pick_num'];
                                $wait_list[$detail['barcode']] = [
                                    'incode' => '-',
                                    'barcode' => $detail['barcode'],
                                    'brand_name' => $brand_info[$detail['brand_id']] ?? '未知',
                                    'category_name' => $category_info[$detail['category_id']] ?? '未知',
                                    'nums' => $nums,
                                    'pick_type' => $detail['pick_type']
                                ];
                            }

                        } else {
                            $nums = $detail['pick_num'] - $detail['already_pick_num'];
                            $wait_list[$detail['barcode']]['nums'] += $nums;
                        }
                    } else {//店内码
                        if ($detail['pick_num'] > $detail['already_pick_num']) {#可拣数 > 已拣数
                            $wait_list[] = [
                                'incode' => $detail['unique_code'],
                                'barcode' => '-',
                                'brand_name' => $brand_info[$detail['brand_id']] ?? '未知',
                                'category_name' => $category_info[$detail['category_id']] ?? '未知',
                                'nums' => $detail['pick_num'],
                                'pick_type' => $detail['pick_type']
                            ];
                        }
                    }
                }
                $data['wait_num'] = array_sum(array_column($wait_list, 'nums'));
                $data['wait_list'] = $wait_list;
                $data['order_info'] = [
                    'id' => $pick_order_detail['id'],
                    'serial_no' => $pick_order_detail['serial_no']
                ];

                //将数据写入缓存
                $this->HashService->saveData($wait_redis_key, $wait_list, 86400);
            }
        } elseif ($params['out_stock_type'] == 'out_store') {#出库单
            //清空缓存
            $wait_redis_key = 'out_store_wait_' . $params['pick_order'];
            $checked_redis_key = 'out_store_checked' . $params['pick_order'];
            $error_redis_key = 'out_store_error' . $params['pick_order'];
            $out_redis_key = 'out_store_out' . $params['pick_order'];
            //判断key是否存在
            if ($this->HashService->keyIsExist($wait_redis_key)) {
                $this->HashService->del($wait_redis_key);
            }
            if ($this->HashService->keyIsExist($checked_redis_key)) {
                $this->HashService->del($checked_redis_key);
            }
            if ($this->HashService->keyIsExist($error_redis_key)) {
                $this->HashService->del($error_redis_key);
            }
            if ($this->HashService->keyIsExist($out_redis_key)) {
                $this->HashService->del($out_redis_key);
            }

            //获取出库任务单信息
            $out_store_info = $this->OutStoreService->getOutStoreOneBySerialNo($params['pick_order']);
            if (empty($out_store_info)) {
                return $this->returnApi('501', '出库任务单不存在');
            }
            //检测出库任务单状态
            if (!in_array($out_store_info['status'], [0, 1])) {
                return $this->returnApi('501', '出库任务单状态不符');
            }

            //检测出库任务单是否存在有效的拣货单，如果存在则抛出异常，只能通过拣货单出库
            $pick_where = [
                'original_code' => $params['pick_order'],
                'status_list' => [1, 2, 3]
            ];
            $pick_order_detail = $this->PickOrderService->getPickOrderDetailLists($pick_where);
            if (!empty($pick_order_detail['data'])){
                return $this->returnApi('501', '此出库任务单存在有效的拣货单，无法通过出库任务单出库');
            }

            $data['wait_num'] = 0;
            $data['wait_list'] = [];
            $data['order_info'] = [
                'id' => $out_store_info['id'],
                'serial_no' => $out_store_info['serial_no']
            ];

            //获取品牌品类
            $brand_info = $category_info = [];
            if (isset($out_store_info['details']) && !empty($out_store_info['details'])) {
                $brand_ids = array_unique(array_column($out_store_info['details'], 'brand_id'));
                $category_ids = array_unique(array_column($out_store_info['details'], 'category_id'));
                if (!empty($brand_ids)) {
                    $brand_list = $this->BrandService->getBrands(['ids' => $brand_ids], ['id', 'name']);
                    $brand_info = array_column($brand_list, 'name', 'id');// brand_id=>brand_name
                }
                if (!empty($category_ids)) {
                    $category_list = $this->CategoryService->getCategory(['category_ids' => $category_ids]);
                    $category_info = array_column($category_list, 'name', 'id');
                }
            }

            //组装待扫描数据
            $wait_list = [];
            foreach ($out_store_info['details'] as $out_detail) {
                if (in_array($out_detail['status'],[0,8,3])) {#获取待导弹和回库的数据
                    if (isset($out_detail['unique_code']) && !empty($out_detail['unique_code'])) {
                        $wait_list[] = [
                            'incode' => $out_detail['unique_code'],
                            'barcode' => '-',
                            'brand_name' => $brand_info[$out_detail['brand_id']] ?? '未知',
                            'category_name' => $category_info[$out_detail['category_id']] ?? '未知',
                            'nums' => $out_detail['num'] - $out_detail['out_num'],
                            'pick_type' => 2
                        ];
                    } else {
                        if (!isset($wait_list[$out_detail['barcode']])) {
                            $wait_list[$out_detail['barcode']] = [
                                'incode' => '-',
                                'barcode' => $out_detail['barcode'],
                                'brand_name' => $brand_info[$out_detail['brand_id']] ?? '未知',
                                'category_name' => $category_info[$out_detail['category_id']] ?? '未知',
                                'nums' => $out_detail['num'] - $out_detail['out_num'],
                                'pick_type' => 2
                            ];
                        } else {
                            $wait_list[$out_detail['barcode']]['nums'] += ($out_detail['num'] - $out_detail['out_num']);
                        }
                    }
                }

            }

            $data['wait_list'] = $wait_list;
            $data['wait_num'] = array_sum(array_column($wait_list, 'nums'));

            //将数据写入缓存
            $this->HashService->saveData($wait_redis_key, $wait_list, 86400);

        }
        return $this->returnApi('200', '操作成功', $data);
    }

    /**
     * 校验出库店内码
     * @RequestMapping(path="/outStock/check_incode", methods="get,post")
     * @param RequestInterface $request
     */
    public function check_incode(RequestInterface $request)
    {
        $is_wrong = false;//是否弹出错误声音
        $params = $request->all();
        if (isset($params['unique_codes'])){
            $unique_codes = explode("\n", $params['unique_codes']);
            $params['unique_codes'] = array_filter($unique_codes , "trim_val");
        }
        $abnormal_out_num = $abnormal_other_num = 0;//错下架的数量
        $error_info = $wait_list = $error_list = $checked_list = $out_list = [];

        if ($params['out_stock_type'] == 'pick_order') {//拣货单
            $pick_order = trim($params['pick_order']);
            //将数据写入缓存
            $wait_redis_key = 'pick_order_wait_' . $pick_order;
            $checked_redis_key = 'pick_order_checked' . $pick_order;
            $error_redis_key = 'pick_order_error' . $pick_order;
            $out_redis_key = 'pick_order_out' . $pick_order;
        }
        elseif ($params['out_stock_type'] == 'out_store') {//出库单
            $out_store = trim($params['pick_order']);
            //将数据写入缓存
            $wait_redis_key = 'out_store_wait_' . $out_store;
            $checked_redis_key = 'out_store_checked' . $out_store;
            $error_redis_key = 'out_store_error' . $out_store;
            $out_redis_key = 'out_store_out' . $out_store;
        }

        $wait_list = $this->HashService->getDataAllFromHash($wait_redis_key, true);
        $checked_list = $this->HashService->getDataAllFromHash($checked_redis_key, true);
        $error_list = array_values($this->HashService->getDataAllFromHash($error_redis_key, true));
        $out_list = $this->HashService->getDataAllFromHash($out_redis_key, true);

        if ($params['type'] == 2){//单条码
            if (empty($params['shelf_list'])){
                return $this->returnApi(501, '货架号不能为空');
            }

            $shelf_list = $params['shelf_list'];
            $shelf_list = array_combine(array_column($shelf_list, 'shelf'), array_values($shelf_list));
            $shelf_nums = array_column($shelf_list, 'nums', 'shelf');
            if (array_sum($shelf_nums) <= 0){
                return $this->returnApi('501', '异常信息', '出库总数量不能小于0');
            }
            if (!is_int(array_sum($shelf_nums))){
                return $this->returnApi('501', '异常信息', '出库总数量必须是整数');
            }

            //获取条码对应的主条码
            $sku_barcode_map = $this->SkuService->getSkuBarcodeMap(['barcode' => trim($params['barcode']), 'id_asc' => true]);
            if (empty($sku_barcode_map)){
                return $this->returnApi(501, '货品在软件中不存在，请重新扫描');
            }
            $sku_barcode_map = $this->SkuService->getSkuBarcodeMap(['sku_id' => $sku_barcode_map[0]['sku_id'], 'id_asc' => true]);
            $barcode = $sku_barcode_map[0]['barcode'];

            //获取出库单条码拣货数量
            $out_barcode_list = $out_list['barcode'] ?? [];

            if ($params['out_stock_type'] == 'pick_order') {//拣货单
                //校验提交的数量是否大于最多拣货数量
                //根据条形码获取拣货单商品信息
                $where = [
                    'pick_order_id' => $params['pick_id'],
                    'barcode' => $barcode,
                    'pick_type' => 1,
                    'status' => 1,
                    'group_shelf_code' => true,
                    'get_sum_pick_num' => true
                ];
                $pick_order_detail_list = $this->PickOrderService->getPickOrderDetailLists($where);
                $error_key = count($error_list);
                if (isset($pick_order_detail_list['data']) && !empty($pick_order_detail_list['data'])) {
                    foreach ($pick_order_detail_list['data'] as $b_info) {
                        //已出库数量
                        $out_num = 0;
                        if (isset($out_barcode_list[$b_info['barcode']]) && $out_barcode_list[$b_info['barcode']][$b_info['shelf_code']]){
                            $out_num = $out_barcode_list[$b_info['barcode']][$b_info['shelf_code']]['nums'];
                        }
                        if (isset($shelf_nums[$b_info['shelf_code']]) && $shelf_nums[$b_info['shelf_code']] > ($b_info['pick_num']-$out_num)) {
                            $error_list[$error_key] = [
                                'code_type' => 2,
                                'code' => $b_info['barcode'],
                                'msg' => '货品错下架',
                                'shelf_code' => $b_info['shelf_code'],
                                'out_num' => $shelf_nums[$b_info['shelf_code']] - ($b_info['pick_num']-$out_num),
                                'status' => 1,
                                'is_abnormal' => true
                            ];
                            $is_wrong = true;
                            $error_key++;
                            //更新货架位可出库数量
                            $shelf_list[$b_info['shelf_code']]['nums'] = $b_info['pick_num']-$out_num;
                            //更新扫描数量
                            $shelf_nums[$b_info['shelf_code']] = $b_info['pick_num']-$out_num;
                        }
                    }
                }
            }
            elseif ($params['out_stock_type'] == 'out_store')
            {//出库单
                //校验提交的数量是否大于最多出库数量
                //根据条形码获取出库单商品明细
                $where = [
                    'get_sum_num' => true,
                    'out_store_id' => $params['pick_id'],
                    'barcode' => $barcode,
                    'status_list' => [0, 1, 2, 3, 5],
                    'group_shelf_code' => true,
                    'not_unique_code' => true
                ];
                $out_store_detail_list = $this->OutStoreService->getOutStoreDetalList($where);
                $error_key = count($error_list);
                if (isset($out_store_detail_list['data']) && !empty($out_store_detail_list['data'])) {
                    foreach ($out_store_detail_list['data'] as $out_info) {
                        //已出库数量
                        $out_num = 0;
                        if (isset($out_barcode_list[$out_info['barcode']]) && isset($out_barcode_list[$out_info['barcode']][$out_info['shelf_code']])){
                            $out_num = $out_barcode_list[$out_info['barcode']][$out_info['shelf_code']]['nums'];
                        }
                        if (isset($shelf_nums[$out_info['shelf_code']]) && $shelf_nums[$out_info['shelf_code']] > ($out_info['num']-$out_num)) {
                            $error_list[$error_key] = [
                                'code_type' => 2,
                                'code' => $out_info['barcode'],
                                'msg' => '货品错下架',
                                'shelf_code' => $out_info['shelf_code'],
                                'out_num' => $shelf_nums[$out_info['shelf_code']] - ($out_info['num']-$out_num),
                                'status' => 1,
                                'is_abnormal' => true
                            ];
                            $is_wrong = true;
                            $error_key++;
                            //更新货架位可出库数量
                            $shelf_list[$out_info['shelf_code']]['nums'] = $out_info['num']-$out_num;
                            //更新扫描数量
                            $shelf_nums[$out_info['shelf_code']] = $out_info['num']-$out_num;
                        }
                    }
                }
            }
            //将出库数据加入到缓存
            foreach ($shelf_list as $s_item){
                if (isset($out_list['barcode'][$barcode]) && isset($out_list['barcode'][$barcode][$s_item['shelf']])){
                    $out_list['barcode'][$barcode][$s_item['shelf']]['nums'] += $s_item['nums'];
                }else{
                    $out_list['barcode'][$barcode][$s_item['shelf']]['shelf'] = $s_item['shelf'];
                    $out_list['barcode'][$barcode][$s_item['shelf']]['nums'] = $s_item['nums'];
                }
            }

            //加入到已扫描
            if (!isset($checked_list[$barcode])){
                $checked_list[$barcode] = [
                    'code' => $barcode,
                    'nums' => array_sum($shelf_nums)
                ];
            }else{
                $checked_list[$barcode]['nums'] += array_sum($shelf_nums);
            }

            //记录条形码的扫描数量
            if (!isset($out_barcode_nums[$barcode])){
                $out_barcode_nums[$barcode] = 0;
            }
            $out_barcode_nums[$barcode] += array_sum($shelf_nums);

            //计算错下架和异常下架的商品数量
            krsort($error_list);
            foreach ($error_list as $key => $e_item){
                if ($e_item['code_type'] != 2){
                    continue;
                }
                $abnormal_out_num += $e_item['out_num'];
                $error_info[$key] = [
                    'code' => $e_item['code'],
                    'msg' => $e_item['msg'],
                    'shelf_code' => $e_item['shelf_code'],
                    'out_num' => $e_item['out_num']
                ];
            }
        }
        elseif ($params['type'] == 3) {//批量店内码
            $unique_codes = array_unique(array_filter($params['unique_codes']));
            foreach ($unique_codes as &$u_code){
                $u_code = trim($u_code);
            }
            if (empty($unique_codes)) {
                return $this->returnApi('501', '店内码不能为空，请扫描店内码', []);
            }
            //待扫描店内码
            $wait_indoces = [];
            foreach ($wait_list as $w_info) {
                if ($w_info['pick_type'] == 2) {
                    $wait_indoces[] = $w_info['incode'];
                }
            }

            //检测店内码是否在系统中
            $unique_shelf_map_list = $this->UniqueCodeService->getUniqueCodes($unique_codes, ['*']);
            $unique_shelf_map = [];
            if (!empty($unique_shelf_map_list)){
                $unique_shelf_map = array_combine(array_column($unique_shelf_map_list, 'unique_code'), array_values($unique_shelf_map_list));
            }

            $error_key = count($error_list);
            foreach ($unique_codes as $unique_code) {
                if(array_key_exists($unique_code,$checked_list)){//已加入到扫描列表
                    continue;
                }elseif(in_array($unique_code, array_column($error_list, 'code'))){
                    continue;
                }elseif(!array_key_exists($unique_code, $unique_shelf_map)){//货品在软件中不存在
                    $error_list[$error_key] = [
                        'code_type' => 1,
                        'code' => $unique_code,
                        'msg' => '货品在软件中不存在，请重新扫描',
                        'shelf_code' => '',
                        'out_num' => 1,
                        'status' => 1,
                        'is_abnormal' => false
                    ];
                    $is_wrong = true;
                }elseif($unique_shelf_map[$unique_code]['status'] != 3){//货品状态不符合
                    $error_list[$error_key] = [
                        'code_type' => 1,
                        'code' => $unique_code,
                        'msg' => '货品状态不符合',
                        'shelf_code' => $unique_shelf_map[$unique_code]['shelf_code'],
                        'out_num' => 1,
                        'status' => 1,
                        'is_abnormal' => true
                    ];
                    $is_wrong = true;
                }elseif (!in_array($unique_code, $wait_indoces)) {#货品不在单据中
                    $error_list[$error_key] = [
                        'code_type' => 1,
                        'code' => $unique_code,
                        'msg' => '货品错下架',
                        'shelf_code' => $unique_shelf_map[$unique_code]['shelf_code'],
                        'out_num' => 1,
                        'status' => 1,
                        'is_abnormal' => true
                    ];
                    $is_wrong = true;
                } else {
                    //加入到已扫描
                    $checked_list[$unique_code] = [
                        'code' => $unique_code,
                        'nums' => 1
                    ];
                    //将出库数据加入到缓存
                    $out_list['unique_code'][$unique_code] = [
                        'code' => $unique_code,
                        'nums' => 1,
                        'barcode' => $unique_shelf_map[$unique_code]['barcode'],
                        'shelf_code' => $unique_shelf_map[$unique_code]['shelf_code'],
                        'w_id' => $unique_shelf_map[$unique_code]['w_id']
                    ];
                }
                $error_key += 1;
            }

            //计算错下架和异常下架的商品数量
            krsort($error_list);
            foreach ($error_list as $key => $e_item){
                if ($e_item['code_type'] != 1){
                    continue;
                }
                if ($e_item['is_abnormal']){
                    $abnormal_out_num += 1;
                }else{
                    $abnormal_other_num += 1;
                }
                $error_info[$key] = [
                    'code' => $e_item['code'],
                    'msg' => $e_item['msg']
                ];
            }
        }
        elseif ($params['type'] == 4) {//批量条码
            $error_list = [];
            //检测是否已提交文件
            if ($this->HashService->keyIsExist($checked_redis_key)) {
                return $this->returnApi(ErrorCode::REQUEST_FILE_ERROR, '文件已上传，不能重复上传');
            }

            //处理传上来的数据
            $file = $request->file('file');
            $filePathName = $file->getPathname();
            $open = fopen($filePathName, "r") or $this->returnApi(ResponseCode::SERVER_ERROR, '打开文件出错');
            $import_data = fread($open, filesize($filePathName));
            $import_row_data = array_filter(explode("@", str_replace(["\r\n", "\r", "\n", "\t", "\s"], "@", $import_data)));
            $file_info = [];
            logger()->info('$import_row_data', $import_row_data);
            foreach ($import_row_data as $row_data) {
                $row_line = explode(',', $row_data);
                logger()->info('$row_line', $row_line);
                $shelf_code = trim($row_line[0]);
                $barcode = trim($row_line[1]);
                if (empty($row_line[0])){
                    $error_info[] = [
                        'shelf_code' => $shelf_code,
                        'code' => $barcode,
                        'msg' => '货架号不能为空',
                        'num' => trim($row_line[2]),
                    ];
                    $is_wrong = true;
                    continue;
                }
                if (empty($row_line[1])){
                    $error_info[] = [
                        'shelf_code' => $shelf_code,
                        'code' => $barcode,
                        'msg' => '条形码不能为空',
                        'num' => trim($row_line[2]),
                    ];
                    $is_wrong = true;
                    continue;
                }
                if (empty($row_line[2]) || $row_line[2] <= 0){
                    $error_info[] = [
                        'shelf_code' => $shelf_code,
                        'code' => $barcode,
                        'msg' => '数量不符',
                        'num' => trim($row_line[2]),
                    ];
                    $is_wrong = true;
                    continue;
                }
                if (isset($file_info[$shelf_code.'_'.$barcode])){//检测数据是否重复
                    $error_info[] = [
                        'shelf_code' => $shelf_code,
                        'code' => $barcode,
                        'msg' => '数据重复',
                        'num' => trim($row_line[2]),
                    ];
                    $is_wrong = true;
                    continue;
                }
                //将不重复的数据写入待检测数据中
                $file_info[$shelf_code.'_'.$barcode] = [
                    'shelf_code' => $shelf_code,
                    'barcode' => $barcode,
                    'num' => trim($row_line[2])
                ];
            }
            if (!empty($file_info)){
                $file_info = collect($file_info);
                $barcodes = $file_info->pluck('barcode')->toArray();
                $barcodes_map = $this->SkuService->getSkuBarcodeMap(['barcodes' => $barcodes, 'id_asc' => true]);
                if (empty($barcodes_map)) {
                    foreach ($file_info as $f_item) {
                        $error_info[] = [
                            'code' => $f_item['barcode'],
                            'msg' => '货品在软件中不存在，请重新扫描',
                            'shelf_code' => $f_item['shelf_code'],
                            'out_num' => $f_item['num']
                        ];
                        $abnormal_other_num += $f_item['num'];
                    }
                    $is_wrong = true;
                }
                else{
                    //获取货架位
                    $shelf_codes = $file_info->pluck('shelf_code')->toArray();
                    $shelf_code_list = $this->ShelfService->getShelfS(['shelf_codes'=>$shelf_codes], ['shelf_code']);
                    if (!empty($shelf_code_list)){
                        $shelf_code_list = array_column($shelf_code_list, 'shelf_code');
                    }
                    $barcodes_sku_map = collect($barcodes_map)->pluck('sku_id', 'barcode')->toArray();

                    $detail_barcode_shelf_num_map = [];
                    if ($params['out_stock_type'] == 'pick_order') {//拣货单
                        //获取出库任务单条码出库数据源
                        $pick_detail_where = [
                            'get_sum_pick_num' => true,
                            'pick_order_id' => $params['pick_id'],
                            'status' => 1,
                            'pick_type' => 1
                        ];
                        $pick_detail_list = $this->PickOrderService->getPickOrderDetailLists($pick_detail_where);
                        if (!empty($pick_detail_list['data'])){
                            foreach ($pick_detail_list['data'] as $pd_item){
                                $detail_barcode_shelf_num_map[$pd_item['barcode']][$pd_item['shelf_code']] = $pd_item['pick_num'];
                            }
                        }
                    }
                    elseif($params['out_stock_type'] == 'out_store'){
                        //获取出库任务单条码出库数据源
                        $out_store_detail_where = [
                            'get_sum_num' => true,
                            'out_store_id' => $params['pick_id'],
                            'status_list' => [0, 1, 2, 3, 5, 8],
                            'not_unique_code' => true,
                            'group_shelf_code' => true,
                            'group_barcode' => true
                        ];
                        $out_store_detail_list = $this->OutStoreService->getOutStoreDetalList($out_store_detail_where);
                        if (!empty($out_store_detail_list['data'])){
                            foreach ($out_store_detail_list['data'] as $osd_item){
                                $detail_barcode_shelf_num_map[$osd_item['barcode']][$osd_item['shelf_code']] = $osd_item['num'];
                            }
                        }
                    }

                    $check_upload = [];
                    $barcodes_shelf_codes = [];
                    foreach ($file_info as $fi) {
                        if (empty($fi['shelf_code'])) {//货架号不能为空
                            $error_list[] = [
                                'code_type' => 2,
                                'code' => $fi['barcode'],
                                'msg' => '货架号不能为空',
                                'shelf_code' => $fi['shelf_code'],
                                'out_num' => $fi['num'],
                                'status' => 1,
                                'is_abnormal' => false
                            ];
                            $is_wrong = true;
                            continue;
                        }elseif (!isset($barcodes_sku_map[$fi['barcode']])) {//条码在软件中不存在
                            $error_list[] = [
                                'code_type' => 2,
                                'code' => $fi['barcode'],
                                'msg' => '货品在软件中不存在，请重新扫描',
                                'shelf_code' => $fi['shelf_code'],
                                'out_num' => $fi['num'],
                                'status' => 1,
                                'is_abnormal' => false
                            ];
                            $is_wrong = true;
                            continue;
                        } elseif (!in_array($fi['shelf_code'], $shelf_code_list)){//货架号在软件中不存在
                            $error_list[] = [
                                'code_type' => 2,
                                'code' => $fi['barcode'],
                                'msg' => '货架号在软件中不存在，请重新扫描',
                                'shelf_code' => $fi['shelf_code'],
                                'out_num' => $fi['num'],
                                'status' => 1,
                                'is_abnormal' => false
                            ];
                            $is_wrong = true;
                            continue;
                        } elseif (!is_numeric($fi['num'])) {//数量类型不符
                            $error_list[] = [
                                'code_type' => 2,
                                'code' => $fi['barcode'],
                                'msg' => '数量类型不符',
                                'shelf_code' => $fi['shelf_code'],
                                'out_num' => $fi['num'],
                                'status' => 1,
                                'is_abnormal' => false
                            ];
                            $is_wrong = true;
                            continue;
                        } elseif (intval($fi['num']) <= 0) {//出库数量必须大于0
                            $error_list[] = [
                                'code_type' => 2,
                                'code' => $fi['barcode'],
                                'msg' => '出库数量必须大于0',
                                'shelf_code' => $fi['shelf_code'],
                                'out_num' => $fi['num'],
                                'status' => 1,
                                'is_abnormal' => false
                            ];
                            $is_wrong = true;
                            continue;
                        } elseif (!array_key_exists($fi['barcode'], $detail_barcode_shelf_num_map)) {//条码在单据中不存在
                            $error_list[] = [
                                'code_type' => 2,
                                'code' => $fi['barcode'],
                                'msg' => '货品错下架',
                                'shelf_code' => $fi['shelf_code'],
                                'out_num' => $fi['num'],
                                'status' => 1,
                                'is_abnormal' => true
                            ];
                            $is_wrong = true;
                            continue;
                        } elseif(isset($detail_barcode_shelf_num_map[$fi['barcode']]) && !isset($detail_barcode_shelf_num_map[$fi['barcode']][$fi['shelf_code']])){//货架号在单据中不存在
                            $error_list[] = [
                                'code_type' => 2,
                                'code' => $fi['barcode'],
                                'msg' => '货品错下架',
                                'shelf_code' => $fi['shelf_code'],
                                'out_num' => $fi['num'],
                                'status' => 1,
                                'is_abnormal' => true
                            ];
                            $is_wrong = true;
                            continue;
                        } elseif(isset($detail_barcode_shelf_num_map[$fi['barcode']]) && isset($detail_barcode_shelf_num_map[$fi['barcode']][$fi['shelf_code']]) && $fi['num'] > $detail_barcode_shelf_num_map[$fi['barcode']][$fi['shelf_code']]){//上传出库数量是否 > 需出库量
                            $error_list[] = [
                                'code_type' => 2,
                                'code' => $fi['barcode'],
                                'msg' => '货品错下架',
                                'shelf_code' => $fi['shelf_code'],
                                'out_num' => $fi['num'] - $detail_barcode_shelf_num_map[$fi['barcode']][$fi['shelf_code']],
                                'status' => 1,
                                'is_abnormal' => true
                            ];
                            $is_wrong = true;
                            $fi['num'] = $detail_barcode_shelf_num_map[$fi['barcode']][$fi['shelf_code']];
                        }
                        $barcodes_shelf_codes[$fi['barcode']][] = $fi;
                        $check_upload[$fi['barcode']][$fi['shelf_code']] = $fi;
                    }
                    $barcodes_map_list = [];
                    $shelf_nums = [];//兼容以前逻辑
                    foreach ($barcodes_shelf_codes as $barcode => $barcode_info) {
                        $barcodes_map_list[$barcodes_sku_map[$barcode]]['upload_barcode'] = $barcode;
                        foreach ($barcode_info as $item) {
                            if (!isset($barcodes_map_list[$barcodes_sku_map[$barcode]]['shelf_info'])){
                                $barcodes_map_list[$barcodes_sku_map[$barcode]]['shelf_info'] = [];
                            }
                            if (!isset($barcodes_map_list[$barcodes_sku_map[$barcode]]['shelf_info'][$item['shelf_code']])){
                                $barcodes_map_list[$barcodes_sku_map[$barcode]]['shelf_info'][$item['shelf_code']] = 0;
                            }
                            $barcodes_map_list[$barcodes_sku_map[$barcode]]['shelf_info'][$item['shelf_code']] += $item['num'];
                        }
                        $barcodes_map_list[$barcodes_sku_map[$barcode]]['master_barcode'] = $barcode;
                    }
                    $sku_barcode_map = $this->SkuService->getSkuBarcodeMap(['sku_ids' => array_values($barcodes_sku_map), 'id_asc' => true]);
                    $sku_barcode_map = collect($sku_barcode_map)->groupBy('sku_id')->toArray();
                    foreach ($barcodes_map_list as $sku_id => $sku_info) {
                        //赋值主条码
                        if (isset($sku_barcode_map[$sku_id]) && count($sku_barcode_map[$sku_id]) > 1) {
                            $barcodes_map_list[$sku_id]['master_barcode'] = $sku_barcode_map[$sku_id][0]['barcode'];
                        }

                        //兼容旧逻辑
                        $shelf_nums[$barcodes_map_list[$sku_id]['master_barcode']] = $sku_info['shelf_info'];
                    }
                    /*获取条码对应的主条码 -- END -------------------------------------*/

                    foreach ($barcodes_map_list as $barcode_info) {
                        $barcode = $barcode_info['master_barcode'];
                        //将出库数据加入到缓存
                        if (!empty($barcode_info['shelf_info'])) {
                            foreach ($barcode_info['shelf_info'] as $shelf_code => $num) {
                                if (isset($out_list['barcode'][$barcode]) && isset($out_list['barcode'][$barcode][$shelf_code])) {
                                    $out_list['barcode'][$barcode][$shelf_code]['nums'] += $num;
                                } else {
                                    $out_list['barcode'][$barcode][$shelf_code]['shelf'] = $shelf_code;
                                    $out_list['barcode'][$barcode][$shelf_code]['nums'] = $num;
                                }
                            }
                        }

                        //加入到已扫描
                        if (!isset($checked_list[$barcode])) {
                            $checked_list[$barcode] = [
                                'code' => $barcode,
                                'nums' => array_sum($barcode_info['shelf_info'])
                            ];
                        } else {
                            $checked_list[$barcode]['nums'] += array_sum($barcode_info['shelf_info']);
                        }

                        //记录条形码的扫描数量
                        if (!isset($out_barcode_nums[$barcode])) {
                            $out_barcode_nums[$barcode] = 0;
                        }
                        $out_barcode_nums[$barcode] += array_sum($barcode_info['shelf_info']);
                    }

                    foreach ($error_list as $key => $e_item){
                        if ($e_item['code_type'] != 2){
                            continue;
                        }
                        if ($e_item['is_abnormal']){
                            $abnormal_out_num += $e_item['out_num'];
                        }else{
                            $abnormal_other_num += $e_item['out_num'];
                        }
                        $error_info[$key] = [
                            'code' => $e_item['code'],
                            'msg' => $e_item['msg'],
                            'shelf_code' => $e_item['shelf_code'],
                            'out_num' => $e_item['out_num']
                        ];
                    }
                }
            }
        }

        foreach ($wait_list as $w_key => &$w_val) {
            if (isset($checked_list[$w_val['incode']])){#店内码
                unset($wait_list[$w_key]);
            }elseif (isset($checked_list[$w_val['barcode']]) && isset($shelf_nums) && !empty($shelf_nums)){
                if ($params['type'] == 4) {
                    $w_val['nums'] = $w_val['nums'] - array_sum($shelf_nums[$w_val['barcode']] ?? []);//兼容旧逻辑
                } else {
                    if ($w_key == $params['barcode']){
                        $w_val['nums'] = $w_val['nums'] - array_sum($shelf_nums);
                    }
                }

                if ($w_val['nums'] == 0) {
                    unset($wait_list[$w_key]);
                }
            }
        }
        //将数据写入缓存
        $this->HashService->del($wait_redis_key);
        $this->HashService->saveData($wait_redis_key, $wait_list, 86400);
        $this->HashService->saveData($checked_redis_key, $checked_list, 86400);
        $this->HashService->saveData($error_redis_key, $error_list, 86400);
        $this->HashService->saveData($out_redis_key, $out_list, 86400);

        $data['wait_num'] = array_sum(array_column($wait_list, 'nums'));//待扫描数量
        $data['abnormal_out_num'] = $abnormal_out_num;//错下架数量
        $data['abnormal_other_num'] = $abnormal_other_num;//其他异常数量
        $data['checked_num'] = array_sum(array_column($checked_list, 'nums'));//已扫描数量
        $data['wait_list'] = $wait_list;//待带扫描列表
        $data['checked_list'] = $checked_list;//已扫描列表
        $data['error_info'] = array_values($error_info);//错误列表
        $data['is_wrong'] = $is_wrong;
        return $this->returnApi('200', '操作成功', $data);
    }

    /**
     * 校验出库条码
     * @RequestMapping(path="/outStock/check_barcode", methods="get,post")
     * @param RequestInterface $request
     */
    public function check_barcode(RequestInterface $request)
    {
        $params = $request->all();
        if (empty(trim($params['barcode']))){
            return $this->returnApi(501, '条码不能为空');
        }

        //获取条码对应的主条码
        $sku_barcode_map = $this->SkuService->getSkuBarcodeMap(['barcode' => trim($params['barcode']), 'id_asc' => true]);
        if (empty($sku_barcode_map)){
            return $this->returnApi(501, '货品在软件中不存在，请重新扫描');
        }
        $sku_barcode_map = $this->SkuService->getSkuBarcodeMap(['sku_id' => $sku_barcode_map[0]['sku_id'], 'id_asc' => true]);
        $sku_barcode = $sku_barcode_map[0]['barcode'];

        $data = [];
        if ($params['out_stock_type'] == 'pick_order') {//拣货单
            //获取出库单条码拣货数量
            $out_redis_key = 'pick_order_out' . $params['pick_order'];
            $out_list = $this->HashService->getDataAllFromHash($out_redis_key, true);
            $barcode_out_list = $out_list['barcode'] ?? [];

            //根据条形码获取拣货单商品信息
            $where = [
                'pick_order_id' => $params['pick_id'],
                'barcode' => $sku_barcode,
                'pick_type' => 1,
                'group_shelf_code' => true,
                'get_sum_pick_num' => true,
                'status' => 1
            ];
            $pick_order_detail_list = $this->PickOrderService->getPickOrderDetailLists($where);
            if (empty($pick_order_detail_list['data'])){
                return $this->returnApi(501, '货品在单据中不存在，请重新扫描');
            }

            //校验出库数量
            $out_store_nums = array_sum( array_column($pick_order_detail_list['data'], 'pick_num'));
            $out_nums = 0;
            if (isset($barcode_out_list[$sku_barcode])){
                $out_nums = array_sum(array_column($barcode_out_list[$sku_barcode], 'nums'));
            }
            if ($out_nums >= $out_store_nums){
                return $this->returnApi(501, '已出库，无需再出库');
            }

            if (isset($pick_order_detail_list['data']) && !empty($pick_order_detail_list['data'])) {
                foreach ($pick_order_detail_list['data'] as $b_info) {
                    if ($b_info['pick_num'] > 0) {
                        $pick_num = $b_info['pick_num'];
                        if (isset($barcode_out_list[$b_info['barcode']]) && isset($barcode_out_list[$b_info['barcode']][$b_info['shelf_code']])){
                            $pick_num = $pick_num - $barcode_out_list[$b_info['barcode']][$b_info['shelf_code']]['nums'];
                        }

                        if ($pick_num > 0){
                            $data[] = [
                                'barcode' => $b_info['barcode'],
                                'shelf_code' => $b_info['shelf_code'],
                                'pick_num' => $pick_num
                            ];
                        }
                    }
                }
            }
        } else {
            //获取出库单信息
            $out_store_info = $this->OutStoreService->getOutStore(['id' => $params['pick_id']], ['id', 'serial_no']);
            if (empty($out_store_info)){
                return $this->returnApi(501, '出库单不存在');
            }
            //获取出库单条码拣货数量
            $out_redis_key = 'out_store_out' . $out_store_info['serial_no'];
            $out_list = $this->HashService->getDataAllFromHash($out_redis_key, true);
            $barcode_out_list = $out_list['barcode'] ?? [];

            //根据条形码获取出库单商品明细
            $where = [
                'get_sum_num' => true,
                'out_store_id' => $params['pick_id'],
                'barcode' => $sku_barcode,
                'status_list' => [0,1,3,8],
                'group_shelf_code' => true,
                'not_unique_code' => true
            ];
            $out_store_detail_list = $this->OutStoreService->getOutStoreDetalList($where);
            if (empty($out_store_detail_list['data'])){
                return $this->returnApi(501, '货品在单据中不存在，请重新扫描');
            }

            //校验出库数量
            $out_store_nums = array_sum( array_column($out_store_detail_list['data'], 'num'));
            $out_nums = 0;
            if (isset($barcode_out_list[$sku_barcode])){
                $out_nums = array_sum(array_column($barcode_out_list[$sku_barcode], 'nums'));
            }
            if ($out_nums >= $out_store_nums){
                return $this->returnApi(501, '已出库，无需再出库');
            }

            //获取可出库数据
            if (isset($out_store_detail_list['data']) && !empty($out_store_detail_list['data'])) {
                foreach ($out_store_detail_list['data'] as $out_info) {
                    if ($out_info['num'] > 0) {
                        $pick_num = $out_info['num'];
                        if (isset($barcode_out_list[$out_info['barcode']]) && isset($barcode_out_list[$out_info['barcode']][$out_info['shelf_code']])){
                            $pick_num = $pick_num - $barcode_out_list[$out_info['barcode']][$out_info['shelf_code']]['nums'];
                        }
                        if (!empty($pick_num)){
                            $data[] = [
                                'barcode' => $out_info['barcode'],
                                'shelf_code' => $out_info['shelf_code'],
                                'pick_num' => $pick_num
                            ];
                        }
                    }
                }
            }
        }

        if (!empty($data)) {
            return $this->returnApi(200, '操作成功', $data);
        } else {
            return $this->returnApi(501, '条码信息不存在');
        }
    }

    /**
     * 货品出库
     * @RequestMapping(path="/outStock/out_stock", methods="get,post")
     * @param RequestInterface $request
     */
    public function out_stock(RequestInterface $request)
    {
        $params = $request->all();

        $user_info = $this->session->get('userInfo');

        //添加锁
        $token = RedisLock::lock(CachePre::getKey(CachePre::OUT_STOCK_LOCK,$user_info['uid'], $params['pick_order']), 600);
        if($token === false) {
            throw new BusinessException("正在出库，请勿重复点击!");
        }

        $operation_type_map = [
            '21' => '差异调整',
            '22' => '退返',
            '23' => '调拨',
            '24' => '盘亏',
            '25' => '销售',
            '26' => '配送丢失',
            '27' => '配送报损'
        ];#店内码log类型

        $unique_code_log_type = [
            '21' => 'diff_out_store',
            '22' => 'back_out_store',
            '23' => 'allot_out_store',
            '24' => 'deficit_out_store',
            '25' => 'sale_out_store',
            '26' => 'lose_out_store',
            '27' => 'loss_out_store'
        ];

        $need_update_pick_detail_info = $need_update_store_detail_info = $produce_area_detail_list = $update_order_branch_detail = $out_unique_code_info = $out_stok_info = $unique_code_log = $abnormal_out_stock = $abnormal_out_stock_detail = $out_unique_need_update_store_detail_ids = $out_unique_diff_update_store_detail_ids = $out_unique_codes = $out_unique_need_update_pick_detail_ids = $not_out_unique_need_update_pick_detail_ids = $achievement_unique_list = $achievement_barcode_list = [];
        $out_nums = 0;//错下架数量
        if ($params['out_stock_type'] == 'pick_order') {//拣货单
            $wait_redis_key = 'pick_order_wait_' . $params['pick_order'];
            $checked_redis_key = 'pick_order_checked' . $params['pick_order'];
            $error_redis_key = 'pick_order_error' . $params['pick_order'];
            $out_redis_key = 'pick_order_out' . $params['pick_order'];

            //获取拣货单信息
            $pick_order_detail = $this->PickOrderService->getPickOrderBySerialNoDetail($params['pick_order'], ['id', 'serial_no', 'w_id', 'status', 'type']);
            if (empty($pick_order_detail)) {
                return $this->returnApi('501', '拣货单不存在');
            }
            if (!in_array($pick_order_detail['status'], [1, 2])) {#检测拣货单是否为拣货中状态
                return $this->returnApi('501', '拣货单状态不符合出库要求');
            }

            //获取出库错误的数据
            $error_list = array_values($this->HashService->getDataAllFromHash($error_redis_key, true));
            if (!empty($error_list)){
                foreach ($error_list as $e_item){
                    if (!$e_item['is_abnormal']) continue;//不需要加入错下架的跳过
                    $abnormal_out_stock_detail[] = [
                        'code_type' => $e_item['code_type'],
                        'code' => $e_item['code'],
                        'shelf_code' => $e_item['shelf_code'],
                        'out_num' => $e_item['out_num'],
                        'status' => 1
                    ];

                    $out_nums += $e_item['out_num'];
                }

                //获取仓库信息
                $warehouse = $this->WarehouseService->getWarehouseOne(intval($pick_order_detail['w_id']));
                if (!empty($out_nums)){
                    $abnormal_out_stock = [
                        'source_serial_no' => trim($params['pick_order']),
                        'source_type' => 1,
                        'w_id' => $pick_order_detail['w_id'],
                        'w_name' => !empty($warehouse) ? $warehouse['name'] : '',
                        'out_num' => $out_nums,
                        'status' => 1,
                        'admin_id' => $user_info['uid'],
                        'admin_name' => $user_info['nickname'],
                    ];
                }
            }

            //获取拣货单商品明细
            $pick_detail_where = [
                'pick_order_id' => $pick_order_detail['id'],
//                'status' => 1
            ];
            $pick_order_detail_list = $this->PickOrderService->getPickOrderDetailLists($pick_detail_where);

            //获取拣货单的仓库、类型
            $w_id = $pick_order_detail['w_id'];
            $type = $pick_order_detail['type'];

            //需要出库的商品
            $out_list = $this->HashService->getDataAllFromHash($out_redis_key, true);

            //组装需要修改的拣货单/出库单 详情信息
            if (isset($pick_order_detail_list['data']) && !empty($pick_order_detail_list['data'])) {
                //获取sku对应的spu
                $sku_ids = array_unique(array_column($pick_order_detail_list['data'], 'sku_id'));
                if (!empty($sku_ids)){
                    $sku_info = $this->SkuService->checkSkuInfo(['sku_ids' => $sku_ids], 1);
                    if (!empty($sku_info)){
                        $sku_spu_map = array_column($sku_info, 'spu_id', 'id');
                    }
                    //获取sku对应的主条码
                    $sku_barcode_map = $this->SkuService->getSkuBarcodeMap(['sku_ids' => $sku_ids, 'id_asc' => true]);
                    $sku_barcode_map_list = [];
                    foreach ($sku_barcode_map as $map){
                        if (!isset($sku_barcode_map_list[$map['sku_id']])){
                            $sku_barcode_map_list[$map['sku_id']] = $map['barcode'];
                        }
                    }
                }

                //来源单号与拣货单详情map
                $pick_original_map = [];
                foreach ($pick_order_detail_list['data'] as $pick_detail){
                    $pick_original_map[$pick_detail['original_code']][] = $pick_detail;
                }
                unset($pick_detail);

                //获取拣货单详情中对应的出库任务的详情信息
                $original_codes = array_unique(array_column($pick_order_detail_list['data'], 'original_code'));
                $out_store_where = [
                    'serial_nos' => $original_codes,
                    'get_export_info' => true
                ];
                $out_store_list = $this->OutStoreService->getOutStoreDetail($out_store_where);
                $out_store_detail = [];
                if (isset($out_store_list['data']) && !empty($out_store_list['data'])) {
                    foreach ($out_store_list['data'] as $o_item) {
                        $d_key = $o_item['barcode'];
                        if (isset($o_item['unique_code']) && !empty($o_item['unique_code'])) {
                            $d_key = $o_item['unique_code'];
                        }
                        if (isset($pick_original_map[$o_item['serial_no']])){
                            foreach ($pick_original_map[$o_item['serial_no']] as $ori_item){
                                if ($ori_item['pick_type'] == 1 && $ori_item['shelf_code'] == $o_item['shelf_code'] && $ori_item['barcode'] == $o_item['barcode']){//条形码
                                    $o_item['exported_num'] = $ori_item['pick_num'];
                                }elseif($ori_item['pick_type'] == 2 && $ori_item['shelf_code'] == $o_item['shelf_code'] && $ori_item['unique_code'] == $o_item['unique_code']){
                                    $o_item['exported_num'] = $ori_item['pick_num'];
                                }
                            }
                        }
                        $out_store_detail[$o_item['serial_no']][$d_key][] = $o_item;
                    }
                }
                //组装需要修改的拣货单详情信息
                $w_log_data = [];#需要记录log的出库任务单信息
                foreach ($pick_order_detail_list['data'] as $p_detail) {
                    $p_barcode = $p_detail['barcode'];
                    $p_shelf = $p_detail['shelf_code'];
                    $original_code = $p_detail['original_code'];#拣货单详情对应的来源单号
                    if ($p_detail['pick_type'] == 1) {#条码拣货
                        if (isset($out_list['barcode']) && !empty($out_list['barcode']) && isset($out_list['barcode'][$p_barcode]) && isset($out_list['barcode'][$p_barcode][$p_shelf])) {#条形码
                            $out_info = $out_list['barcode'][$p_barcode][$p_shelf];
                            //存在需要出库的货架位时，标记成已出库，且修改出库数量
                            //匹配真实的拣出数量
                            $already_pick_num = 0;#真实拣出数量
                            $already_status = 3;#出库状态
                            if ($out_info['nums'] >= $p_detail['pick_num']) {
                                $already_pick_num = $p_detail['pick_num'];
                                $out_list['barcode'][$p_barcode][$p_shelf]['nums'] = $out_info['nums'] - $p_detail['pick_num'];
                                $already_status = 2;
                            } elseif ($out_info['nums'] < $p_detail['pick_num'] && $out_info['nums'] > 0) {
                                $already_pick_num = $out_info['nums'];
                                $out_list['barcode'][$p_barcode][$p_shelf]['nums'] = 0;
                                $already_status = 2;
                            }

                            if ($type == 25) {#销售拣货
                                for ($x = 1; $x <= $already_pick_num; $x++) {
                                    $update_order_branch_detail[] = [
                                        "detail_where" => [
                                            'branch_serial_no' => $original_code,
                                            'pick_type' => 1,
                                            'barcode' => $p_barcode,
                                            'shelf_code' => $p_shelf,
                                            'current_generation' => 4
                                        ],
                                        'data' => [
                                            'current_generation' => 5
                                        ]
                                    ];
                                }
                            }

                            //需修改数据
                            $need_update_pick_detail_info[] = [
                                'id' => $p_detail['id'],
                                'already_pick_num' => $already_pick_num,
                                'status' => $already_status
                            ];
                            $achievement_barcode_list[] = [
                                'code' => $p_barcode,
                                'num' => $already_pick_num
                            ];
                            if ($already_status == 2) {#出库的商品加入到生产区
                                //生产区货品
                                $produce_area_detail_list[] = [
                                    'w_id' => $w_id,
                                    'source_type' => $type,
                                    'source_order_no' => $original_code,
                                    'sign_type' => 2,
                                    'unique_code' => '',
                                    'barcode' => $p_barcode,
                                    'num' => $already_pick_num,
                                    'produce_num' => 0,
                                    'reback_num' => 0,
                                    'surplus_num' => $already_pick_num,
                                    'status' => 0,
                                    'admin_id' => $user_info['uid'],
                                    'admin_name' => $user_info['nickname'],
                                    'sku_id' => $p_detail['sku_id'],
                                    'spu_id' => $sku_spu_map[$p_detail['sku_id']]??0,
                                    'brand_id' => $p_detail['brand_id'],
                                    'category_id' => $p_detail['category_id']
                                ];

                                $out_stok_info[] = [
                                    'w_id' => $w_id,
                                    'sku_id' => $p_detail['sku_id'],
                                    'shelf_code' => $p_detail['shelf_code'],
                                    'out_num' => $already_pick_num
                                ];
                            }

                            //出库任务单详情
                            if (isset($out_store_detail[$original_code]) && isset($out_store_detail[$original_code][$p_barcode]) && !empty($out_store_detail[$original_code][$p_barcode])) {
                                foreach ($out_store_detail[$original_code][$p_barcode] as $key => $o_info) {
                                    if ($o_info['shelf_code'] == $out_info['shelf']) {
                                        $o_status = 3;#部分出库
                                        if ($o_info['num'] == ($o_info['out_num'] + $already_pick_num)) {#总出库出 == 已出库数 则将状态改为4（已出库）
                                            $o_status = 4;#已出库
                                        }
                                        $need_update_store_detail_info[] = [
                                            'id' => $o_info['id'],
                                            'out_num' => $o_info['out_num'] + $already_pick_num,
                                            'status' => $o_status,
                                            'exception_num' => $o_info['exported_num'] - $already_pick_num,
                                            'admin_id' => $user_info['uid'],
                                            'admin_name' => $user_info['nickname'],
                                        ];
                                    }
//                                    unset($out_store_detail[$original_code][$p_barcode][$key]);
                                }
                            }
                            //拣货单的出库数量
                            if (isset($w_log_data[$original_code])){
                                $w_log_data[$original_code]['out_nums'] += $already_pick_num;
                            }else{
                                $w_log_data[$original_code]['out_nums'] = $already_pick_num;
                            }
                        } else {#标记成未出库
                            $need_update_pick_detail_info[] = [
                                'id' => $p_detail['id'],
                                'already_pick_num' => 0,
                                'status' => 3
                            ];

//                            if (isset($out_store_detail[$original_code]) && isset($out_store_detail[$original_code][$p_barcode]) && !empty($out_store_detail[$original_code][$p_barcode])) {
//                                foreach ($out_store_detail[$original_code][$p_barcode] as $key => $o_info) {
//                                    $need_update_store_detail_info[] = [
//                                        'id' => $o_info['id'],
//                                        'status' => 1,
//                                        'export_num' => $o_info['num'] - $o_info['out_num'],
//                                        'admin_id' => $user_info['uid'],
//                                        'admin_name' => $user_info['nickname'],
//                                    ];
//                                    unset($out_store_detail[$p_detail['original_code']][$p_detail['barcode']][$key]);
//                                }
//                            }

                            if (isset($out_store_detail[$original_code]) && isset($out_store_detail[$original_code][$p_barcode]) && !empty($out_store_detail[$original_code][$p_barcode])) {
                                foreach ($out_store_detail[$original_code][$p_barcode] as $key => $o_info) {
                                    $need_update_store_detail_info[] = [
                                        'id' => $o_info['id'],
                                        'status' => 5,
                                        'exception_num' => $o_info['num'] - $o_info['out_num'],
                                        'admin_id' => $user_info['uid'],
                                        'admin_name' => $user_info['nickname'],
                                    ];
                                    unset($out_store_detail[$p_detail['original_code']][$p_detail['barcode']][$key]);
                                }
                            }


                        }
                    } else {#店内码拣货
                        if (isset($out_list['unique_code']) && !empty($out_list['unique_code']) && isset($out_list['unique_code'][$p_detail['unique_code']])) {
                            //拣货类型是销售(订单)时，需要将订单商品项的操作环节改为已出库
                            if ($type == 25) {#销售拣货
                                $update_order_branch_detail[] = [
                                    "detail_where" => [
                                        'branch_serial_no' => $original_code,
                                        'unique_code' => $p_detail['unique_code'],
                                        'pick_type' => 2,
                                        'current_generation' => 4
                                    ],
                                    'data' => [
                                        'current_generation' => 5
                                    ],
                                    'opertaion_user' => $user_info
                                ];
                            }

                            //店内码出库log
                            $unique_code_log[$p_detail['unique_code']] = [
                                'unique_code' => $p_detail['unique_code'],//店内码
                                'operation_type' => PublicCode::UNIQUE_CODE_LOG_TYPE_MAP[$unique_code_log_type[$type]],//操作类型
                                'admin_id' => $user_info['uid'],//操作人id
                                'admin_name' => $user_info['nickname'],//操作人姓名
                                'operation_desc' => $operation_type_map[$type].'出库',//操作描述
                                'operation_time' => date( 'Y-m-d H:i:s' ),
                                'snow_id' => strval(generateSnowId())
                            ];

                            $out_unique_need_update_pick_detail_ids[] = $p_detail['id'];
                            $achievement_unique_list[] = $p_detail['unique_code'];

                            //生产区货品
                            $produce_area_detail_list[] = [
                                'w_id' => $w_id,
                                'source_type' => $type,
                                'source_order_no' => $original_code,
                                'sign_type' => 1,
                                'unique_code' => $p_detail['unique_code'],
                                'barcode' => $sku_barcode_map_list[$p_detail['sku_id']]??'',
                                'num' => 1,
                                'surplus_num' => 1,
                                'admin_id' => $user_info['uid'],
                                'admin_name' => $user_info['nickname'],
                                'sku_id' => $p_detail['sku_id'],
                                'spu_id' => $sku_spu_map[$p_detail['sku_id']]??0,
                                'brand_id' => $p_detail['brand_id'],
                                'category_id' => $p_detail['category_id']
                            ];

                            if (!isset($out_unique_code_info[$p_detail['sku_id']][$out_list['unique_code'][$p_detail['unique_code']]['barcode']][$out_list['unique_code'][$p_detail['unique_code']]['shelf_code']])){
                                $out_unique_code_info[$p_detail['sku_id']][$out_list['unique_code'][$p_detail['unique_code']]['barcode']][$out_list['unique_code'][$p_detail['unique_code']]['shelf_code']] = [];
                            }
                            $out_unique_code_info[$p_detail['sku_id']][$out_list['unique_code'][$p_detail['unique_code']]['barcode']][$out_list['unique_code'][$p_detail['unique_code']]['shelf_code']][] = $p_detail['unique_code'];


                            if (!isset($out_stok_info[$w_id.'-'.$p_detail['sku_id'].'-'.$p_detail['shelf_code']])){
                                $out_stok_info[$w_id.'-'.$p_detail['sku_id'].'-'.$p_detail['shelf_code']] = [
                                    'w_id' => $w_id,
                                    'sku_id' => $p_detail['sku_id'],
                                    'shelf_code' => $p_detail['shelf_code'],
                                    'out_num' => 0
                                ];
                            }
                            $out_stok_info[$w_id.'-'.$p_detail['sku_id'].'-'.$p_detail['shelf_code']]['out_num'] += 1;

                            if (isset($out_store_detail[$p_detail['original_code']]) && isset($out_store_detail[$p_detail['original_code']][$p_detail['unique_code']]) && !empty($out_store_detail[$p_detail['original_code']][$p_detail['unique_code']])) {
                                foreach ($out_store_detail[$p_detail['original_code']][$p_detail['unique_code']] as $key => $o_info) {
                                    $out_unique_need_update_store_detail_ids[] = $o_info['id'];
                                    unset($out_store_detail[$p_detail['original_code']][$p_detail['unique_code']][$key]);
                                }
                            }
                            //拣货单的出库数量
                            if (isset($w_log_data[$original_code])){
                                $w_log_data[$original_code]['out_nums'] += 1;
                            }else{
                                $w_log_data[$original_code]['out_nums'] = 1;
                            }
                        } elseif ($p_detail['status'] != 2) {
                            $not_out_unique_need_update_pick_detail_ids[] = $p_detail['id'];

                            if (isset($out_store_detail[$p_detail['original_code']]) && isset($out_store_detail[$p_detail['original_code']][$p_detail['unique_code']]) && !empty($out_store_detail[$p_detail['original_code']][$p_detail['unique_code']])) {
                                foreach ($out_store_detail[$p_detail['original_code']][$p_detail['unique_code']] as $key => $o_info) {
                                    $out_unique_diff_update_store_detail_ids[] = $o_info['id'];
                                    unset($out_store_detail[$p_detail['original_code']][$p_detail['unique_code']][$key]);
                                }
                            }
                        }
                    }
                }
            }

            //组装数据
            $data = [
                'pick_order_data' => [
                    'where' => [
                        'serial_no' => $params['pick_order']
                    ],
                    'data' => [
                        'pick_admin_id' => $params['picker_id'],
                        'pick_admin_name' => $params['picker_name'],
                        'pick_time' => date('Y-m-d H:i:s'),
                        'out_admin_id' => $params['outer_id'],
                        'out_admin_name' => $params['outer_name'],
                        'out_time' => date('Y-m-d H:i:s'),
                        'status' => 3
                    ]
                ],
                'pick_order_detail' => $need_update_pick_detail_info,
                'out_store_detail' => $need_update_store_detail_info,
                'produce_area_detail' => $produce_area_detail_list,
                'out_unique_code_info' => $out_unique_code_info,
                'out_stok_info' => $out_stok_info,
                'abnormal_out_stock' => $abnormal_out_stock,
                'abnormal_out_stock_detail' => $abnormal_out_stock_detail,
                'out_unique_need_update_pick_detail_ids' => $out_unique_need_update_pick_detail_ids,
                'w_id' => $w_id,
                'admin_id' => $user_info['uid'],
                'admin_name' => $user_info['nickname'],
                'out_unique_need_update_store_detail_ids' => $out_unique_need_update_store_detail_ids,
                'not_out_unique_need_update_pick_detail_ids' => $not_out_unique_need_update_pick_detail_ids,
                'out_unique_diff_update_store_detail_ids' => $out_unique_diff_update_store_detail_ids
            ];
            $res = $this->PickOrderService->pickOrderOut($data);
            if ($res) {
                if (!empty($update_order_branch_detail)){
                    $this->OrderService->ModifyOrderBranchDetail($update_order_branch_detail);
                }
                $out_store_map = [];
                //记录log
                if (!empty($w_log_data)){
                    $out_store_codes = array_keys($w_log_data);
                    $out_store_list = $this->OutStoreService->list(1, count($out_store_codes), ['serial_no' => $out_store_codes]);
                    if (!empty($out_store_list['data'])){
                        $out_store_map = array_column($out_store_list['data'], 'id', 'serial_no');
                    }
                }
                foreach ($w_log_data as $out_store_serial_no => $w_log_item) {
                    $snow_id =  generateSnowId();
                    $w_data = [
                        'op_id' => $out_store_map[$out_store_serial_no]??0,
                        'req_router_name' => '货品出库',
                        'model_name' => 'OutStock',
                        'remark' => '出库数量:'.$w_log_item['out_nums'],
                        'snow_id' => strval($snow_id)
                    ];
                    wlog($snow_id, $w_data);
                }
                if (!empty($unique_code_log)){
                    foreach ($unique_code_log as $log_item){
                        addUniqueCodeLog(strval(generateSnowId()), $log_item);
                    }
                }
            }
        } else {#出库单
            $wait_redis_key = 'out_store_wait_' . $params['pick_order'];
            $checked_redis_key = 'out_store_checked' . $params['pick_order'];
            $error_redis_key = 'out_store_error' . $params['pick_order'];
            $out_redis_key = 'out_store_out' . $params['pick_order'];

            //获取出库单信息
            $out_store_info = $this->OutStoreService->getOutStoreOneBySerialNo(trim($params['pick_order']));
            if (empty($out_store_info)) {
                return $this->returnApi('501', '出库任务单不存在');
            }

            //获取出库错误的数据
            $error_list = array_values($this->HashService->getDataAllFromHash($error_redis_key, true));
            if (!empty($error_list)){
                foreach ($error_list as $e_item){
                    if (!$e_item['is_abnormal']) continue;//不需要加入错下架的跳过
                    $abnormal_out_stock_detail[] = [
                        'code_type' => $e_item['code_type'],
                        'code' => $e_item['code'],
                        'shelf_code' => $e_item['shelf_code'],
                        'out_num' => $e_item['out_num'],
                        'status' => 1
                    ];

                    $out_nums += $e_item['out_num'];
                }

                //获取仓库信息
                $warehouse = $this->WarehouseService->getWarehouseOne(intval($out_store_info['w_id']));
                if (!empty($out_nums)){
                    $abnormal_out_stock = [
                        'source_serial_no' => trim($params['pick_order']),
                        'source_type' => 1,
                        'w_id' => $out_store_info['w_id'],
                        'w_name' => !empty($warehouse) ? $warehouse['name'] : '',
                        'out_num' => $out_nums,
                        'status' => 1,
                        'admin_id' => $user_info['uid'],
                        'admin_name' => $user_info['nickname'],
                    ];
                }
            }

            //检测出库任务单状态
            if (!in_array($out_store_info['status'], [0, 1])) {
                return $this->returnApi('501', '出库任务单状态不符合出库要求');
            }

            //需要出库的商品
            $out_list = $this->HashService->getDataAllFromHash($out_redis_key, true);

            //需要出库的出库单详情信息
            $w_id = $out_store_info['w_id'];
            $type = $source_type = $out_store_info['type'];
            $source_order_no = $out_store_info['serial_no'];
            //出库任务单商品明细
            $out_store_detail_list = $out_store_info['details'];
            //获取sku对应的主条码
            $sku_ids = array_unique(array_column($out_store_detail_list, 'sku_id'));
            $sku_barcode_map = $this->SkuService->getSkuBarcodeMap(['sku_ids' => $sku_ids, 'id_asc' => true]);
            $sku_barcode_map_list = [];
            foreach ($sku_barcode_map as $map){
                if (!isset($sku_barcode_map_list[$map['sku_id']])){
                    $sku_barcode_map_list[$map['sku_id']] = $map['barcode'];
                }
            }
            $out_nums = 0;
            foreach ($out_store_detail_list as $item) {
                if (in_array($item['status'],[0,8,3])) {#只获取待导弹的数据
                    if (isset($item['unique_code']) && !empty($item['unique_code'])) {#店内码
                        if (isset($out_list['unique_code'][$item['unique_code']])) {
                            $out_unique_need_update_store_detail_ids[] = $item['id'];
                            $produce_area_detail_list[] = [
                                'w_id' => $w_id,
                                'source_type' => $source_type,
                                'source_order_no' => $source_order_no,
                                'sign_type' => 1,
                                'unique_code' => $item['unique_code'],
                                'barcode' => $sku_barcode_map_list[$item['sku_id']]??'',
                                'num' => 1,
                                'surplus_num' => 1,
                                'admin_id' => $user_info['uid'],
                                'admin_name' => $user_info['nickname'],
                                'sku_id' => $item['sku_id'],
                                'spu_id' => $item['spu_id'],
                                'brand_id' => $item['brand_id'],
                                'category_id' => $item['category_id']
                            ];

                            if (!isset($out_stok_info[$w_id.'-'.$item['sku_id'].'-'.$item['shelf_code']])){
                                $out_stok_info[$w_id.'-'.$item['sku_id'].'-'.$item['shelf_code']] = [
                                    'w_id' => $w_id,
                                    'sku_id' => $item['sku_id'],
                                    'shelf_code' => $item['shelf_code'],
                                    'out_num' => 0
                                ];
                            }
                            $out_stok_info[$w_id.'-'.$item['sku_id'].'-'.$item['shelf_code']]['out_num'] += 1;

                            if (!isset($out_unique_code_info[$item['sku_id']][$out_list['unique_code'][$item['unique_code']]['barcode']][$out_list['unique_code'][$item['unique_code']]['shelf_code']])){
                                $out_unique_code_info[$item['sku_id']][$out_list['unique_code'][$item['unique_code']]['barcode']][$out_list['unique_code'][$item['unique_code']]['shelf_code']] = [];
                            }
                            $out_unique_code_info[$item['sku_id']][$out_list['unique_code'][$item['unique_code']]['barcode']][$out_list['unique_code'][$item['unique_code']]['shelf_code']][] = $item['unique_code'];
                            $out_nums += 1;

                            //店内码出库log
                            $unique_code_log[$item['unique_code']] = [
                                'unique_code' => $item['unique_code'],//店内码
                                'operation_type' => PublicCode::UNIQUE_CODE_LOG_TYPE_MAP[$unique_code_log_type[$source_type]],//操作类型
                                'admin_id' => $user_info['uid'],//操作人id
                                'admin_name' => $user_info['nickname'],//操作人姓名
                                'operation_desc' => $operation_type_map[$source_type].'出库',//操作描述
                                'operation_time' => date( 'Y-m-d H:i:s' ),
                                'snow_id' => strval(generateSnowId())
                            ];
                            $achievement_unique_list[] = $item['unique_code'];
                        }else{
                            $out_unique_diff_update_store_detail_ids[] = $item['id'];
                        }
                    } else {#条形码
                        if (isset($out_list['barcode']) && !empty($out_list['barcode']) && isset($out_list['barcode'][$item['barcode']]) && !empty($out_list['barcode'][$item['barcode']]) && isset($out_list['barcode'][$item['barcode']][$item['shelf_code']]) && !empty($out_list['barcode'][$item['barcode']][$item['shelf_code']])) {
                            $o_status = 3;#部分出库
                            $already_pick_num = 0;#真实拣出数量
                            //计算真实出库量 拣货的出库量 > 剩余出库量 时，真实出库量 = 剩余出库量
                            if ($out_list['barcode'][$item['barcode']][$item['shelf_code']]['nums'] > ($item['num'] - $item['out_num'])) {
                                $already_pick_num = $item['num'] - $item['out_num'];
                                $out_list['barcode'][$item['barcode']][$item['shelf_code']]['nums'] = $out_list['barcode'][$item['barcode']][$item['shelf_code']]['nums'] - $already_pick_num;
                            } else {#真实出库量 = 拣货的出库量
                                $already_pick_num = $out_list['barcode'][$item['barcode']][$item['shelf_code']]['nums'];
                            }

                            if ($item['num'] == ($item['out_num'] + $already_pick_num)) {#总出库出 == 已出库数 则将状态改为4（已出库）
                                $o_status = 4;#已出库
                            }
                            $need_update_store_detail_info[] = [
                                'id' => $item['id'],
                                'out_num' => $item['out_num'] + $already_pick_num,
                                'export_num' => $item['export_num'] - $item['out_num'],
                                'status' => $o_status,
                                'exception_num' => $item['num'] - ($item['out_num'] + $already_pick_num),
                                'admin_id' => $user_info['uid'],
                                'admin_name' => $user_info['nickname'],
                            ];
                            $produce_area_detail_list[] = [
                                'w_id' => $w_id,
                                'source_type' => $source_type,
                                'source_order_no' => $source_order_no,
                                'sign_type' => 2,
                                'unique_code' => '',
                                'barcode' => $item['barcode'],
                                'num' => $already_pick_num,
                                'surplus_num' => $already_pick_num,
                                'admin_id' => $user_info['uid'],
                                'admin_name' => $user_info['nickname'],
                                'sku_id' => $item['sku_id'],
                                'spu_id' => $item['spu_id'],
                                'brand_id' => $item['brand_id'],
                                'category_id' => $item['category_id']
                            ];

                            $out_stok_info[] = [
                                'w_id' => $w_id,
                                'sku_id' => $item['sku_id'],
                                'shelf_code' => $item['shelf_code'],
                                'out_num' => $already_pick_num
                            ];
                            $out_nums += $already_pick_num;

                            $achievement_barcode_list[] = [
                                'code' => $item['barcode'],
                                'num' => $already_pick_num
                            ];
                        }else{
                            $need_update_store_detail_info[] = [
                                'id' => $item['id'],
                                'status' => 5,
                                'exception_num' => $item['num'] - $item['out_num'],
                                'export_num' => $item['export_num'] - $item['out_num'],
                                'admin_id' => $user_info['uid'],
                                'admin_name' => $user_info['nickname'],
                            ];
                        }
                    }
                }
            }

            $data = [
                'produce_area_detail_list' => $produce_area_detail_list,
                'update_store_detail_info' => $need_update_store_detail_info,
                'out_unique_code_info' => $out_unique_code_info,
                'out_stok_info' => array_values($out_stok_info),
                'abnormal_out_stock' => $abnormal_out_stock,
                'abnormal_out_stock_detail' => $abnormal_out_stock_detail,
                'out_unique_need_update_store_detail_ids' => $out_unique_need_update_store_detail_ids,
                'out_unique_diff_update_store_detail_ids' => $out_unique_diff_update_store_detail_ids,//异常出库数据
                'admin_id' => $user_info['uid'],
                'admin_name' => $user_info['nickname'],
                'w_id' => $w_id,
                'out_store_id' => $out_store_info['id'],
            ];

            //将出库任务单明细商品出库
//            if (!empty($need_update_store_detail_info) && !empty($abnormal_out_stock_detail)) {
            $res = $this->OutStoreService->updateOutStoreDetail($data);

            $w_data = [
                'op_id' => $out_store_info['id'],
                'req_router_name' => '货品出库',
                'model_name' => 'OutStock',
                'remark' => '出库数量:'.$out_nums,
                'snow_id' => strval($request->getAttribute('snow_id'))
            ];
            wlog($w_data['snow_id'], $w_data);

            if (!empty($unique_code_log)){
                $this->UniqueCodeLogService->batchOperationLogByDetailInfo($w_data['snow_id'], $unique_code_log);
            }
//            } else {
//                return $this->returnApi('502', '出库失败', false);
//            }
        }
        if ($res) {
            //业务绩效埋点
            $achievement_data = [];
            if (array_key_exists($type , PublicCode::achievement_pick_map) ){
                if (!empty($achievement_unique_list)){
                    $achievement_data[] = [
                        'w_id' => $w_id,// 仓库
                        'type' => PublicCode::achievement_pick_map[$type],// 单据类型
                        'source_no' => $params['pick_order'],// 单据号
                        'admin_id' => $params['picker_id'],// 操作人ID
                        'admin_name' => $params['picker_name'],// 操作人名称
                        'handle_at' => currentTime(), // 操作时间
                        'code_type' => 1,// 1店内码
                        'code_list' => $achievement_unique_list
                    ];
                }
                if (!empty($achievement_barcode_list)){
                    $achievement_data[] = [
                        'w_id' => $w_id,// 仓库
                        'type' => PublicCode::achievement_pick_map[$type],// 单据类型
                        'source_no' => $params['pick_order'],// 单据号
                        'admin_id' => $params['picker_id'],// 操作人ID
                        'admin_name' => $params['picker_name'],// 操作人名称
                        'handle_at' => currentTime(), // 操作时间
                        'code_type' => 2,// 2条形码
                        'code_list' => $achievement_barcode_list
                    ];
                }
                if (!empty($achievement_data)){
                    logger()->info('$achievement_data', [$achievement_data]);
                    AmqpProducer::produce(new AchievementProducer($achievement_data));
                }
            }

            //清空缓存
            if ($this->HashService->keyIsExist($wait_redis_key)) {
                $this->HashService->del($wait_redis_key);
            }
            if ($this->HashService->keyIsExist($checked_redis_key)) {
                $this->HashService->del($checked_redis_key);
            }
            if ($this->HashService->keyIsExist($error_redis_key)) {
                $this->HashService->del($error_redis_key);
            }
            if ($this->HashService->keyIsExist($out_redis_key)) {
                $this->HashService->del($out_redis_key);
            }
            RedisLock::unlock(CachePre::getKey(CachePre::OUT_STOCK_LOCK,$user_info['uid'], $params['pick_order']),$token);

            return $this->returnApi('200', '出库成功', $res);
        } else {
            RedisLock::unlock(CachePre::getKey(CachePre::OUT_STOCK_LOCK,$user_info['uid'], $params['pick_order']),$token);
            return $this->returnApi('501', '出库失败', false);
        }
    }

    /**
     * 检测上传条码数据
     * @RequestMapping(path="/outStock/check_upload_barcode", methods="get,post")
     * @param RequestInterface $request
     */
    public function check_upload_barcode(RequestInterface $request)
    {
        $params = $request->all();

        //处理上传上来的数据
        $file = $request->file('file');
        if (!$request->hasFile('file')) {
            return $this->returnApi(ErrorCode::REQUEST_FILE_ERROR, '未接收到file文件');
        }

        if (empty($file)) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, '上传文件不能为空', false);
        }
        $upload_excel_header = getExcelHeader($file); // 获取excel原表头数据
        $config_header = findTemplate('outstock_barcode', implode(',', $upload_excel_header));
        if (!$config_header) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, '数据表头于模板不一致');
        }
        $file_info = readFromExcel($file, explode(',', $config_header['key']));
        if (empty($file_info)) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, '未获取到上传的数据');
        }

        return $this->returnApi(200, '操作成功', $file_info);
    }

    /**
     *  设置异常原因
     * @RequestMapping(path="/outStock/setException", methods="post")
     * @param RequestInterface $request
     */
    public function setException(RequestInterface $request)
    {
        $params = $request->all();
        if(empty($params['detail_id']) || empty($params['exception_id']) || empty($params['out_store_id'])){
            return $this->returnApi(ErrorCode::REQUEST_ERROR, '参数错误，请刷新重试！');
        }
        $outStore = $this->OutStoreService->getOutStore(['id'=>$params['out_store_id']]);
        if($outStore['status'] == PublicCode::out_store_status_finish){
            return $this->returnApi(ErrorCode::REQUEST_ERROR, '出库单已完成，不允许操作!');
        }

        //检查类型
        if($outStore['type'] != 22){
            return $this->returnApi(ErrorCode::REQUEST_ERROR, '仅支持退返单据任务类型标注！');
        }

        $res = $this->OutStoreService->setException($params['detail_id'],['exception_id'=>$params['exception_id']]);
        if(!empty($res)){
            $snow_id = generateSnowId();
            $w_data = [
                'op_id' => $params['out_store_id'] ?? 0,
                'req_router_name' => '备注未出库原因',
                'model_name' => 'OutStock',
                'remark' => 'sku:' . $params['sku_id'] . '，原因:' . PublicCode::OUT_STORE_EXCEPTION_REMARK[$params['exception_id']] ?? '',
                'snow_id' => strval($snow_id)
            ];
            wlog($snow_id, $w_data);
        }
        return $this->returnApi(200, '设置成功');
    }

    /**
     * 数据验证
     * @param array $data
     * @return array
     */
    protected function validate(array $data, $secne = 'default')
    {

        $message = [
            'brand_ids.string' => '品牌参数有误!',
            'warehouse_ids.string' => '仓库参数有误!',
            'status.numeric' => '状态参数有误!',
            'out_store_ids.string' => '任务id不能为空!',
            'out_store_ids.array' => '任务id格式有误!',
            'purchase_no.string' => '采购单号有误!',
            'search_type.numeric' => '查询类型有误!',
            'pick_replace.numeric' => '查询类型有误!',
            'out_store_id.required' => '任务id不能为空!',
            'out_store_id.numeric' => '任务id必须为数值!',
            'search_value.string' => '查询值有误!',
            'date_range.string' => '时间格式有误!',
            'import_type.in' => '导入方式错误!',
            'import_type.required' => '请选择导入方式!',
            'serial_no.string' => '出库单号格式有误!',
            'order_no.string' => '类源单号格式有误!',
            'type.in' => '出库类型有误!',
            'out_type.in' => '出库类型有误!',
            'type.numeric' => '出库类型有误!',
            'out_type.numeric' => '出库方式有误!',
            'status_type.numeric' => '状态类型格式有误!',
            'status_type.in' => '状态类型格式有误!',
            'search_code.string' => '编码格式有误!',
        ];
        $rules = [
            'brand_ids' => 'string',
            'out_store_id' => 'required|numeric',
            'out_store_ids' => 'required|array',
            'type' => [
                'numeric',
                Rule::in(array_keys($this->taskTypeMap))
            ],
            'out_type' => [
                'numeric',
                Rule::in(array_keys($this->out_type))
            ],
            'status_type' => [
                'numeric',
                Rule::in([1, 2, 3])
            ],
            'serial_no' => 'string',
            'serial_nos' => 'array',
            'file' => 'required|file',
            'warehouse_ids' => 'string',
            'status' => 'numeric',
            'in_stock_no' => 'string',
            'purchase_no' => 'string',
            'search_type' => 'numeric',
            'pick_replace' => 'numeric',
            'search_value' => 'string',
            'order_no' => 'string',
            'export_num' => 'numeric',
            'exception_num' => 'numeric',
            'date_range' => 'string',
            'search_code' => 'string',
        ];

        $secnes = [
            'search' => ['warehouse_ids','type', 'status', 'serial_no', 'exception_num', 'export_num', 'order_no', 'search_type', 'search_value', 'date_range','pick_replace'],
            'detail_search' => ['out_store_id', 'out_type', 'status_type', 'search_code'],
            'blockInfo' => ['out_store_ids'],
            'add' => ['serial_no', 'type'],
            'batchAdd' => ['serial_nos', 'type'],
        ];
        $useRule = [];
        if (isset($secnes[$secne])) {
            foreach ($secnes[$secne] as $item) {
                $useRule[$item] = $rules[$item];
            }
        } else {
            throw new BusinessException('验证场景值有误');
        }

        $validator = validate()->make(
            $data,
            $useRule,
            $message
        );

        return $validator->validate(); //验证数据有效性
    }
}