<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>前置生产</title>
    <link href="/static/layui/css/layui.css" rel="stylesheet"/>
    <link href="/static/css/pearCommon.css" rel="stylesheet"/>
    <link href="/static/css/myui.css" rel="stylesheet"/>
</head>
<style>
    .clear {
        clear: both
    }

    .table_01 {
        text-align: center;
        margin: 10px;
        width: 90%;
    }

    .font_nums {
        font-size: 18px;
        font-weight: bold;
    }

    table, th, td {
        border: 1px solid rgba(0, 0, 0, .1);
    }

    .fail_info {
        color: red;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }

    .success_info {
        color: green;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }

    .layui-card-body {
        position: static !important;
    }
    .grid-demo{margin: 20px;}
    .grid-demo_1{margin: 10px;}
    .lable_form{font-size: 18px; font-weight: 600; width: 120px !important; color: #333;}
    .lable_font{font-size: 18px; font-weight: 600; width: 300px !important; color: #666; text-align:left !important;}
    .lable_nums{font-size: 18px; font-weight: 600; color: #5b5b5b;}

    #in_store_print{display: none;}
    #allot_print_rfid{display: none;}
    .input_font{font-weight: 600; font-size: 18px;}
    .layui-form-select{position: relative; font-size: 18px; font-weight: 600;}
    .layui-table th{font-size: 18px !important; font-weight: 600 !important; text-align: center;}
    .layui-table tr{font-size: 18px !important; font-weight: 600 !important; text-align: center;}
    .required_sign{color: red; padding-right: 5px;}
    #check_unique_info{display: none; font-size: 20px; font-weight: 600; width: 300px !important; color: rgb(0, 191, 160); text-align:left !important;}
    #print_tag_info{display: none}
    #allot_barcode_print{display: none;}
    #allot_barcode_print_rfid{display: none;}
    #allot_unique_code_print{display: none;}
    #allot_unique_code_print_rfid{display: none;}
    #in_store_barcode_print{display: none;}
    #in_store_barcode_print_rfid{display: none;}
    #in_store_barcode_print_front{display: none;}
    #epc_switch{position: absolute; margin-top:2.5%; right:13%; z-index: 1;}
    #epc_switch_rfid{position: absolute; margin-top:8.9%; right:13%; z-index: 1;}
    #epc_switch_barcode{position: absolute; margin-top:8.7%; right:13%; z-index: 1;}
    #epc_switch_barcode_rfid{position: absolute; margin-top:21.6%; right:13%; z-index: 1;}
    #print_price_nums{padding-top: 10px; display: none;}
    .wait_bind{font-size: 18px;font-weight: 800;color: rgb(64, 158, 255);}
    #read_rfid_div{display: none;}

    .layui-form-checkbox[lay-skin="primary"] span {
        /*color: #f00 !important;*/
        font-size: 15px;
        font-weight: bold;
    }


</style>
<body class="pear-container">
<div id="print_price_nums">
    <label class="layui-form-label lable_form">打印数量</label>
    <input style="font-size: 30px;margin-right: -15px;" type="button" class="fl rate_ul_r_jian layui-btn layui-btn-primary" value="-"></input>
    <input style="font-size: 20px; width: 100px;" class="rate_ul_r_input layui-btn layui-btn-primary" type="text" name="print_barcode_num" id="print_barcode_num" ><span class="rate_ul_r_icon"></span>
    <input style="font-size: 30px; margin-left: -5px;" type="button" class="fr rate_ul_r_jia layui-btn layui-btn-primary" value="+"></input>
</div>

<form class="layui-form" lay-filter='tag_form' action="">
    <div class="layui-bg-gray" style="padding: 10px;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md5">
                <div class="layui-card">
                    <div class="layui-card-header">一、生产配置</div>
                    <div class="layui-card-body">
                        <div class="layui-row" style="background-color: #e8e2e23b;padding: 20px 0px 20px 0px;">
                            <div class="grid-demo grid-demo-bg3">
                                <div class="layui-input-inline"><label class="layui-form-label lable_form"><span class="required_sign">*</span>吊牌模版</label></div>
                                <div class="layui-input-inline">
                                    <select name="temp_type" id="temp_type" lay-filter="temp_type" class="input_font" lay-verify="temp_type">
                                        <option value="" selected>请选择吊牌模版</option>
                                        @if ($template_type)
                                            @foreach ($template_type as $key => $value)
                                                <option selected = "{{$value['checked']}}" value="{{ $value['type'] }}">{{ $value["name"] }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                            <div class="grid-demo grid-demo-bg3">
                                <div class="layui-input-inline"><label class="layui-form-label lable_form">当前模板</label></div>
                                <div class="layui-input-inline" style="width:300px !important;">
                                    <label class="layui-form-label lable_form" style="width: 200px !important;" id="temp_name">
                                        @if ($template_type)
                                            @foreach ($template_type as $key => $value)
                                                @if($value['checked'])
                                                    {{ $value["name"] }}
                                                @endif
                                            @endforeach
                                        @endif
                                    </label>
                                </div>
                            </div>
                            <div class="grid-demo grid-demo-bg3">
                                <div class="layui-input-inline"><label class="layui-form-label lable_form"><span class="required_sign">*</span>生产类型</label></div>
                                <div class="layui-input-inline">
                                    <select name="produce_type" id="produce_type" lay-filter="produce_type" class="input_font" lay-verify="produce_type" disabled>
                                        <option value="" selected>请选择生产类型</option>
                                        @if ($production_type)
                                            @foreach ($production_type as $key => $value)
                                                <option value={{ $key }}>{{ $value }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                            <div class="grid-demo grid-demo-bg3" id="in_store_type_div">
                                <div class="layui-input-inline"><label class="layui-form-label lable_form"><span class="required_sign">*</span>入库方式</label></div>
                                <div class="layui-input-inline">
                                    <select name="in_store_type" id="in_store_type" lay-filter="in_store_type" class="input_font" lay-verify="in_store_type" disabled>
                                        @if ($in_store_type)
                                            @foreach ($in_store_type as $key => $value)
                                                <option value={{ $key }}>{{ $value }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-row">
                            <div class="layui-col-md3">
                                <div class="grid-demo_1 grid-demo-bg1">
                                    <label class="lable_nums">生产记录</label> <label class="lable_nums" id="print_nums">0</label>
                                </div>
                            </div>
                            <div class="layui-col-md4">
                                <div class="grid-demo_1 grid-demo-bg2 lable_nums"></div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="grid-demo_1 grid-demo-bg2 lable_nums" id="copybtn" data-clipboard-action="copy" data-clipboard-target=".print_log">一键复制编码</div>
                            </div>
                            <div class="layui-col-md2">
                                <div class="grid-demo_1 grid-demo-bg2 lable_nums" id="clear_table">清空</div>
                            </div>
                        </div>
                        <div class="layui-row">
                            <div class="print_log">
                                <table class="layui-table" id="print_log" name="print_log">
                                    <thead>
                                    <tr>
                                        <th>商品编码</th>
                                        <th>数量</th>
                                    </tr>
                                    </thead>
                                    <tbody>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md7">
                <div class="layui-card">
                    <div class="layui-card-header">二、吊牌生产</div>
                    {{--新品入库-RFID--}}
                    <div class="layui-card-body" id="in_store_print_rfid">
                        <div class="layui-tab">
                            <ul class="layui-tab-title">
                                <li class="layui-this">条形码打印</li>
                            </ul>
                            <div class="layui-tab-content">
                                <input type="hidden" name="in_store_unique_code" id="in_store_unique_code_rfid">
                                {{--条码打印吊牌--}}
                                <div class="layui-tab-item layui-show" id="in_store_barcode_print_show_rfid"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>


{{--打印吊牌时，需要显示的商品信息--}}
<div class="layui-col-md12" id="print_tag_info">
    <div class="layui-col-md2">
        <img id="spu_image" style="width: 100px;height: 100px" src="">
    </div>
    <div class="layui-col-md8">
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form" id="code_type"></label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_unique_code"></label>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form">销售价:</label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_sale_price"></label>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form">吊牌价:</label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_tag_price"></label>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form">品类:</label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_category"></label>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form">品牌:</label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_brand"></label>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form">SKU:</label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_sku"></label>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form">货号:</label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_spu_no"></label>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form" id="print_spec_key"></label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_spec_value"></label>
            </div>
        </div>
    </div>
</div>

{{--新品入库，条码打印吊牌-前置rfid--}}
<div id="in_store_barcode_print_front">
    <div class="layui-row">
        <input type="hidden" name="tag_print_log_id" id="tag_print_log_id">
        <input type="hidden" name="tag_unique_code" id="tag_unique_code">
        <div class="layui-col-md12">
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form">到货预约单</label>
                    </div>
                    <div class="layui-col-md7">
                        <input type="text" name="arrival_order_no" id="arrival_order_no" placeholder="到货预约单号" disabled value="" class="layui-input input_font">
                        <input type="hidden" name="quality_id" id="quality_id">
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form"><span class="required_sign">*</span>质检箱号</label>
                    </div>
                    <div class="layui-col-md7">
                        <input type="text" name="box_no" id="box_no" placeholder="请输入质检箱号" class="layui-input input_font"  lay-verify="box_no">
                    </div>
                    <div class="layui-col-md2" style="margin: 5px 0px 0px 20px;">
                        <input type="checkbox" lay-filter="imperfect" name="imperfect" id="imperfect" lay-skin="primary" title="原残">
                    </div>
                </div>

            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form"><span class="required_sign">*</span>扫描条形码</label>
                    </div>
                    <div class="layui-col-md7">
                        <input type="text" name="barcode" id="barcode" placeholder="请扫描条形码" class="layui-input input_font" onkeypress="javascript:if(event.keyCode == 32)event.returnValue = false;">
                    </div>
                    <div class="layui-col-md1" style="margin-left: 20px;">
                        <button type="button" class="layui-btn" lay-submit lay-filter="instore_print_barcode_rfid_front">打印</button>
                    </div>
                    <div class="layui-col-md1" style="margin: 5px 0px 0px 10px;">
                        <input type="checkbox" name="again_print" id="again_print" lay-skin="primary" title="补打">
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2"><label class="layui-form-label lable_form"><span class="required_sign">*</span>识别RFID</label></div>
                    <div class="layui-col-md7">
                        <input type="text" id="epc_code" name="epc_code" placeholder="请将RFID标签放到采集台上" class="layui-input input_font">
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px; margin-top: 3px;">
                        <input lay-filter="epc_switch_instore_barcode" type="checkbox" name="close" title="RFID开关" id="epc_switch_instore_barcode">
                    </div>
                </div>
            </div>

            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">&nbsp;</div>
                    <div class="layui-col-md8">
                        <button type="button" class="layui-btn" lay-submit lay-filter="instore_bind_rfid">绑定入库</button>
                        <label class="wait_bind" id="wait_bind"></label>
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px;">
                        <label class="layui-form-label" id="check_unique_info"></label>
                    </div>
                </div>
            </div>

            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">&nbsp;</div>
                    <div class="layui-col-md10">
                        <label class="layui-form-label lable_form" style="width: 160px !important;">货品数量/入库数量:</label>
                        <label class="layui-form-label lable_form" id="product_num" style="text-align: right !important;margin-right: 2px;width: auto !important;">0</label>
                        <label style="font-weight: 600;float: left;line-height: 31px;font-size: 29px;">|</label>
                        <label class="layui-form-label lable_form" id="in_product_num" style="text-align:left !important;margin-left: 2px;">0</label>
                    </div>
                </div>
            </div>

            <div class="grid-demo grid-demo-bg3" id="read_rfid_div">
                <div class="layui-row">
                    <div class="layui-col-md2">&nbsp;</div>
                    <div class="layui-col-md10">
                        <label class="layui-form-label lable_form" style="width: 160px !important;">已识别RFID数量:</label>
                        <label class="layui-form-label lable_form" id="read_num" style="text-align: right !important;margin-right: 2px;width: auto !important; color: green;">0</label>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-md2">&nbsp;</div>
                    <div class="layui-col-md10">
                        <label class="layui-form-label lable_form" style="width: 160px !important;">未识别RFID数量:</label>
                        <label class="layui-form-label lable_form" id="no_read_num" style="text-align: right !important;margin-right: 2px;width: auto !important; color: red;">0</label>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-md2">&nbsp;</div>
                    <div class="layui-col-md10">
                        <label class="layui-form-label lable_form" style="width: 160px !important;"></label>
                        <label class="layui-form-label lable_form" id="error_msg" style="text-align: right !important;margin-right: 2px;width: auto !important; color: red;">
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12" id="print_unique_info"></div>
    </div>
</div>

<script src="/static/layui/layui.js"></script>
<script src="/static/js/vue.js"></script>
<script src="http://127.0.0.1:8000/CLodopfuncs.js"></script>
{{--<script src="/static/js/jquery.min.js"></script>--}}
<script src="/static/js/clipboard.min.js"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/' //自定义模块
    }).use(['jquery', 'table', 'element', 'form', 'layer', 'iframeTools', 'laytpl', 'comm'], function () {
        var form = layui.form;
        var table = layui.table;
        var $ = layui.jquery;
        var element = layui.element;// Tab的切换功能，切换事件监听等，需要依赖element模块
        var layer = layui.layer;
        var iframeTools = layui.iframeTools;
        var laytpl = layui.laytpl;
        var comm =layui.comm;
        var goodsCodes = [];

        $(document).ready(function() {
            // 假设你的select的元素有一个ID为'mySelect'
            var selectedValue = 5;// 获取需要选中的值，可能来自于服务器端的数据或者本地存储
            $('#temp_type').val(selectedValue);

            $('#produce_type').val(2);
            form.render('select'); // 更新layui的select视图

            //获取选中的文本
            var text = $('#temp_type').find("option:selected").text();
            $('#temp_name').html(text);

            //展示前置rfid吊牌div
            $('#in_store_barcode_print_show_rfid').html($('#in_store_barcode_print_front').html());
            $('#epc_switch_instore_barcode').prop("checked", false);
            $('#in_store_print_rfid').show();
            $('#box_no').focus();

            // stopAndDisconnect();//关闭epc
            form.render();
        });

        $('#box_no').on("focus", function(){
            $('#box_no').val("");
            $("#product_num").text(0);
            $("#in_product_num").text(0);
            $("#arrival_order_no").val("");
        });

        var RFID_HOST = 'http://127.0.0.1:30010';//RFID IP
        var RFID_START_URL = RFID_HOST + '/api/Reader/connectAndStart';//开启RFID
        var RFID_STOP_URL = RFID_HOST + '/api/Reader/stopAndDisconnect';//暂停RFID
        var RFID_READ_URL = RFID_HOST + '/api/Reader/continueRead';//读取RFID数据地址
        var  TAG_IMG = [
            // 1== 买断  2== 代销   3==联营
            { id: "0", src: "" },
            {
                id: "1",
                src: "http://bigoffs-rep.oss-cn-beijing.aliyuncs.com/upload/2023-03-09/af393ea3-52e1-ce72-5a22-5f0a0503f2b1.jpg"
            },
            {
                id: "2",
                src: "http://bigoffs-rep.oss-cn-beijing.aliyuncs.com/upload/2023-03-09/f097ced4-5af9-510b-0fee-225a8feb6035.jpg"
            },
            {
                id: "3",
                src: "http://bigoffs-rep.oss-cn-beijing.aliyuncs.com/upload/2023-03-09/90a009db-9300-0b50-a752-cb8219429146.jpg"
            }
        ];

        $('#allot_unique_code_print_show').html($('#allot_unique_code_print').html());
        $('#print_uniqe_epc_switch').html();


        //一键复制
        var clipboard = new ClipboardJS('#copybtn');
        clipboard.on('success', function(e) {
            layer.msg('复制成功', {icon: 1,anim: 6});
        });
        clipboard.on('error', function(e) {
            layer.msg('复制失败', {icon: 5,anim: 6});
        });

        //一键清空
        $('#clear_table').click(function () {
            $("#print_log  tr:not(:first)").remove();
            $('#print_nums').html(0);
        });


        /*******************  开启关闭rfid  *********************/
        //新品入库 开启rfid
        form.on('checkbox(epc_switch_instore_barcode)', function (data) {
            if( data.elem.checked){　　　　　　//判断当前多选框是选中还是取消选中
                goodsCodes = []
                $('#read_rfid_div').hide();
                connectAndStart()
                continueRead()
            }else{
                goodsCodes = []
                $('#read_rfid_div').hide();
                stopAndDisconnect();
            }
            var value = data.value;   //获取选中的value值
        });
        /*******************  开启关闭rfid  *********************/

        /***************************  空格打开关闭rfid开关     ******************************/
        // 禁止按空格键时使用页面page down页面下翻滚动事件
        function PSDPD_KeyCheck(key) {
            if (key.target.nodeName == "INPUT" || key.target.nodeName == "TEXTAREA" || key.target.nodeName == "SELECT") return;
            if (key.target.hasAttribute("contenteditable") && key.target.getAttribute("contenteditable") == "true") return;
            if (key.ctrlKey || key.altKey || key.metaKey) return;
            if (key.key == ' ') {
                key.stopPropagation();
                key.preventDefault();
                return false;
            }
        }
        document.addEventListener('keydown', PSDPD_KeyCheck);
        $(document).keyup(function (event) {
            if (event.keyCode === 32) {
                 //新品入库
                if ($('#epc_switch_instore_barcode').is(':checked')) {
                    console.log('空格执行关闭-新品入库')
                    layui.use('form', function () {
                        $('#epc_switch_instore_barcode').prop("checked", false);
                        form.render();
                    });
                    goodsCodes = []
                    $('#read_rfid_div').hide();
                    stopAndDisconnect();
                } else {
                    console.log('空格执行开启-新品入库')

                    layui.use('form', function () {
                        $('#epc_switch_instore_barcode').prop("checked", true);
                        form.render();
                    });
                    goodsCodes = []
                    $('#read_rfid_div').hide();
                    connectAndStart()
                    continueRead()
                }
            }
        });
        /***************************  空格打开关闭rfid开关     ******************************/

        /***************************     RFID获取     ******************************/
        var timer;
        function stopAndDisconnect() {
            clearInterval(timer);
            // $('#read_rfid_div').hide();
            curlPost(RFID_STOP_URL)
        }

        function connectAndStart() {
            //清空商品展示信息
            $('#print_unique_info').html("");
            curlPost(RFID_START_URL);
        }

        function continueRead() {
            console.log('持续读取')
            timer=setInterval(Read,800);
        }

        function Read() {
            // console.log('读取')
            curlPost(RFID_READ_URL);
            // console.log(RFID_READ_URL)
        }

        // 将数组内容以换行形式显示在textarea
        function arrToBreakLine(arr) {
            return arr.join('\r\n')
        }

        function curlPost(url) {
            console.log("temp_type=======================",$('#temp_type').val());
            $.ajax({
                url: url,
                type: 'POST',
                data:{StartReadParam:{MemoryBank:'EPC',ExcludeDuplication:'true'}},
                dataType: 'JSON',
                success: function (result) {
                    console.log('epc请求success结果==', $('#temp_type').val());
                    if ('boolean' != typeof(result)) {
                        if ($('#temp_type').val() == 4 || $('#temp_type').val() == 5){//TSC打印机打印吊牌绑定epc并入库
                            var before_epc_info = $("#epc_code").val()
                            // 将返回结果提取，去重
                            var epcRes = comm.array_unique(comm.array_column(result.Data,'Epc'))
                            console.log('epc res========================', epcRes);
                            if (epcRes.length > 1){
                                layer.msg('不可放入多个RFID!', {icon: 5,anim: 6});
                                return false;
                            }

                            //检测板上的rfid是否在此次读取rfid中已被绑定
                            var ar = epcRes.filter(function(n) {
                                return goodsCodes.indexOf(n) != -1
                            });
                            if (ar != ''){
                                $("#epc_code").val('');
                                return false;
                            }

                            $("#epc_code").val('');
                            $("#epc_code").val(epcRes);
                            now_epc_code = $("#epc_code").val();
                            unique_code = $("#tag_unique_code").val();
                            tag_print_log_id = $('#tag_print_log_id').val();
                            checkEpcRes = true;
                            console.log('before now===========',before_epc_info, now_epc_code);
                            if (before_epc_info !== now_epc_code && now_epc_code != '') {
                                $('#print_unique_info').html('');
                                $.ajax({
                                    url: '/tagPrint/frontBindEpcInStore',
                                    type: 'post',
                                    data: JSON.stringify({
                                        epc_code : now_epc_code,
                                        unique_code: unique_code,
                                        tag_print_log_id : tag_print_log_id,
                                        barcode : $("#barcode").val(),
                                        arrival_order_no : $("#arrival_order_no").val()
                                    }),
                                    dataType: "json",
                                    contentType: 'application/json',
                                    processData: false,
                                    success: function (data) {
                                        if (data.code !== 200) {
                                            layer.msg(data.msg,{icon:2,time:1500})
                                        }else{
                                            this.enterConfirm = function(event){
                                                if(event.keyCode === 13){
                                                    $(".layui-layer-btn0").click();
                                                    return false; //阻止系统默认回车事件
                                                }
                                            };
                                            $("#epc_code").val('');
                                            $('#barcode').val("");
                                            $('#barcode').focus();
                                            $('#check_unique_info').hide();
                                            $('#wait_bind').html('绑定入库成功');
                                            goodsCodes.push(now_epc_code) // 合并数组

                                            //获取质检箱入库数量
                                            $.ajax({
                                                url: '/tagPrint/getBoxInfo',
                                                type: 'post',
                                                data: JSON.stringify({
                                                    box_no : $("#box_no").val(),
                                                    imperfect: $("#imperfect").is(':checked'),
                                                    temp_type: $('#temp_type').val(),
                                                    sign: 2
                                                }),
                                                dataType: "json",
                                                contentType: 'application/json',
                                                processData: false,
                                                success: function (data) {
                                                    if (data.code !== 200){
                                                        layer.msg(data.msg,{icon:2,time:1500});
                                                    }else{
                                                        $("#product_num").text(data.data.num);
                                                        $("#in_product_num").text(data.data.in_num);
                                                    }
                                                },
                                                error: function (e) {
                                                    layer.msg("检测磁扣是否在磁扣池中失败！",{icon:2,time:1500})
                                                },
                                            });
                                        }
                                    },
                                    error: function (e) {
                                        layer.msg("店内码绑定epc且入库失败！",{icon:2,time:1500})
                                    },
                                });
                                return false;
                            }
                        }else{
                            // 将返回结果提取，去重
                            var epcRes = comm.array_unique(comm.array_column(result.Data,'Epc'))
                            console.log('epc res========================', epcRes, goodsCodes);
                            var diff = $.grep(epcRes, function(element) {
                                return goodsCodes.indexOf(element) === -1;
                            });
                            //校验epc是否都已绑定店内码，且找出未识别到的epc的店内码
                            if(diff.length > 0) {//存在新rfid时再请求
                                $.ajax({
                                    url: '/tagPrint/checkFrontRfid',
                                    type: 'post',
                                    data: JSON.stringify({
                                        epc_codes: epcRes,
                                        quality_id: $('#quality_id').val(),
                                        imperfect: $("#imperfect").is(':checked')
                                    }),
                                    dataType: "json",
                                    contentType: 'application/json',
                                    processData: false,
                                    success: function (data) {
                                        if (data.code !== 200) {
                                            layer.msg(data.msg,{icon:2,time:1500})
                                            $.merge(goodsCodes, epcRes)
                                            $('#read_rfid_div').hide();
                                        } else {
                                            $('#read_num').text(data.data.read_num);
                                            $('#no_read_num').text(data.data.no_read_num);
                                            var error_info = "";
                                            $.each(data.data.error_msg, function (index, item) {
                                                error_info = error_info + item + "\n";
                                            })
                                            $('#error_msg').text(error_info);
                                            $('#read_rfid_div').show();
                                            if(data.data.error_num == 0){
                                                //入库
                                                $.ajax({
                                                url: '/tagPrint/frontInStore',
                                                type: 'post',
                                                data: JSON.stringify({
                                                    epc_codes : epcRes,
                                                    arrival_order_no : $("#arrival_order_no").val(),
                                                    quality_id: $('#quality_id').val(),
                                                    imperfect: $("#imperfect").is(':checked'),
                                                    barcode : $("#barcode").val(),

                                                }),
                                                dataType: "json",
                                                contentType: 'application/json',
                                                processData: false,
                                                success: function (data) {
                                                    if (data.code !== 200) {
                                                        layer.msg(data.msg,{icon:2,time:1500})
                                                    }else{
                                                        this.enterConfirm = function(event){
                                                            if(event.keyCode === 13){
                                                                $(".layui-layer-btn0").click();
                                                                return false; //阻止系统默认回车事件
                                                            }
                                                        };
                                                        $("#epc_code").val('');
                                                        $('#barcode').val("");
                                                        $('#barcode').focus();
                                                        $('#check_unique_info').hide();
                                                        $('#wait_bind').html('批量入库成功');
                                                        $.merge(goodsCodes, epcRes)
                                                        $('#read_rfid_div').hide();

                                                        //获取质检箱入库数量
                                                        $.ajax({
                                                            url: '/tagPrint/getBoxInfo',
                                                            type: 'post',
                                                            data: JSON.stringify({
                                                                box_no : $("#box_no").val(),
                                                                imperfect: $("#imperfect").is(':checked'),
                                                                temp_type: $('#temp_type').val(),
                                                                sign: 2
                                                            }),
                                                            dataType: "json",
                                                            contentType: 'application/json',
                                                            processData: false,
                                                            success: function (data) {
                                                                if (data.code !== 200){
                                                                    layer.msg(data.msg,{icon:2,time:1500});
                                                                }else{
                                                                    if(data.data.is_clear){//是否需要清空吊牌生产数据
                                                                        $('#box_no').val("");
                                                                        $("#product_num").text(0);
                                                                        $("#in_product_num").text(0);
                                                                        $("#arrival_order_no").val("");
                                                                        if($("#imperfect").is(':checked')){
                                                                            $('#imperfect').prop('checked', false);
                                                                        }
                                                                        if($("#again_print").is(':checked')){
                                                                            $('#again_print').prop('checked', false);
                                                                        }
                                                                        $("#box_no").focus();
                                                                    }else{
                                                                        $("#product_num").text(data.data.num);
                                                                        $("#in_product_num").text(data.data.in_num);
                                                                    }
                                                                }
                                                            },
                                                            error: function (e) {
                                                                layer.msg("检测磁扣是否在磁扣池中失败！",{icon:2,time:1500})
                                                            },
                                                        });
                                                    }
                                                },
                                                error: function (e) {
                                                    layer.msg("店内码绑定epc且入库失败！",{icon:2,time:1500})
                                                },
                                            });
                                            }
                                        }
                                    },
                                    error: function (e) {
                                        // layer.msg("检测磁扣是否在磁扣池中失败！")
                                    },
                                });
                            }
                        }

                    }
                },
                error: function (error) {
                    console.log(typeof (error));
                    console.log('epc请求结果fail==',error);
                    layer.msg('设备读取EPC码失败',{icon:2,time:1500})
                    //$("#goods_codes").text(JSON.stringify(error));
                }
            });
        }

        //显示店内码打印吊牌时的内容
        function show_print_tag_info(tag_info, sign = 1) {
            console.log('show print tag info=============',tag_info);
            // if (sign == 1){
            //     $('#wait_bind').html(tag_info.code+'绑定成功');
            // }else{
            //     if ($('#temp_type').val() == 3){
            //         $('#wait_bind').html(tag_info.code+'校验成功 等待绑定');
            //     }else{
            //         $('#wait_bind').html(tag_info.code+'校验成功');
            //     }
            // }
            $('#tag_unique_code').val(tag_info.code)
            $('#wait_bind').css('color', 'rgb(0, 191, 160)');
            var code_type = '';
            $("#spu_image").attr("src", tag_info.image);
            if (tag_info.code_type == 1){//店内码
                code_type = '店内码';
            }else{//条形码
                code_type = '条形码';
            }
            $('#wait_bind').html(tag_info.code+'校验成功');
            $('#wait_bind').show();


            var print_tag_info = '<div class="layui-col-md2">' +
                '        <img id="spu_image" style="width: 100px;height: 100px" src="">' +
                '    </div>' +
                '    <div class="layui-col-md8">' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form" id="code_type">'+code_type+'</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_unique_code">'+tag_info.code+'</label></div>' +
                '        </div>' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form">销售价:</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_sale_price">'+tag_info.sale_price+'</label></div>' +
                '        </div>' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form">吊牌价:</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_tag_price">'+tag_info.tag_price+'</label></div>' +
                '        </div>' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form">品类:</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_category">'+tag_info.category_name+'</label></div>' +
                '        </div>' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form">品牌:</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_brand">'+tag_info.brand_name+'</label></div>' +
                '        </div>' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form">SKU:</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_sku">'+tag_info.SKU+'</label></div>' +
                '        </div>' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form">货号:</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_spu_no">'+tag_info.spu_no+'</label></div>' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form" id="print_spec_key">'+tag_info.spec_key+':</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_spec_value">'+tag_info.spec_value+'</label></div>' +
                '        </div>' +
                '    </div>';
            $('#print_unique_info').html(print_tag_info);
        }
        //打印吊牌
        function print_tag(print_info, temp_type, number) {
            console.log("temp_type=================================="+temp_type);

            $.each(print_info, function(index, item) {
                var sale_price = item.sale_price.split(".")
                var priceLength = sale_price[0].length
                var priceOneWidth = ""
                var priceTwoLeft = ""
                var priceSignleft = "";

                if(temp_type == 4){
                    //累计打印数量
                    print_nums = $('#print_nums').html()*1;
                    print_nums += 1;
                    $('#print_nums').html(print_nums);
                    //追加生产记录
                    $("#print_log tbody").prepend('<tr><td>'+item.code+'</td><td>'+1+'</td></tr>');

                    // let LODOP = getLodop() //获得打印
                    LODOP.SET_LICENSES("","94D9E78FC9B721D1EBF7165B69114B39A35","","");
                    //4*9 TSC打印机打印
                    LODOP.PRINT_INITA("0mm", "0mm", "40", "90mm", "任务名")
                    LODOP.SET_PRINT_PAGESIZE(1, 400, 900, "")

                    LODOP.ADD_PRINT_TEXT("22mm", "3mm", "12mm", "3mm", "尺码")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    var specTop='19.5mm'
                    var specSize=13
                    if (item.spec_value.length >5 && item.spec_value.length <=7){
                        specTop='21mm'
                        specSize=10
                    }else if(item.spec_value.length >7){
                        specTop='21.5mm'
                        specSize=8
                    }
                    LODOP.ADD_PRINT_TEXT(specTop,"10mm","33mm","3mm",item.spec_value.substring(0, 12)) //11
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", specSize)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                    //横线 实线
                    LODOP.ADD_PRINT_LINE("28mm","2mm", "28mm", "RightMargin:2mm",0, 1)

                    if (priceLength == 1) {
                        priceSignleft = "12mm"
                        priceOneLeft = "12mm"
                        priceOneWidth = "8mm"
                        priceTwoLeft = "20mm"
                    } else if (priceLength == 2) {
                        priceSignleft = "10mm"
                        priceOneLeft = "10mm"
                        priceOneWidth = "13mm"
                        priceTwoLeft = "22mm"
                    } else if (priceLength == 3) {
                        priceSignleft = "7mm"
                        priceOneLeft = "5mm"
                        priceOneWidth = "20mm"
                        priceTwoLeft = "24mm"
                    } else if (priceLength == 4) {
                        priceSignleft = "4mm"
                        priceOneLeft = "2mm"
                        priceOneWidth = "26mm"
                        priceTwoLeft = "27mm"
                    }
                    console.log('priceSignleft================', priceSignleft);
                    console.log('priceOneLeft================', priceOneLeft);
                    console.log('priceOneWidth================', priceOneWidth);
                    console.log('priceTwoLeft================', priceTwoLeft);

                    LODOP.ADD_PRINT_TEXT("29.7mm", "3mm", "12mm", "3mm", "品牌：")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                    LODOP.ADD_PRINT_TEXT("29.7mm","1mm","RightMargin:3mm","3mm",item.brand_name.substring(0, 10))
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                    LODOP.ADD_PRINT_TEXT("32.6mm", "3mm", "12mm", "3mm", "货号：")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                    LODOP.ADD_PRINT_TEXT("32.6mm", "1mm", "RightMargin:3mm", "3mm", item.spu_no.substring(0, 14))
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                    LODOP.ADD_PRINT_TEXT("35.6mm", "3mm", "12mm", "3mm", "吊牌价：")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                    LODOP.ADD_PRINT_TEXT("35.6mm", "1mm", "RightMargin:3mm", "3mm", "￥"+item.tag_price)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                    //横线 实线
                    LODOP.ADD_PRINT_LINE("39.4mm","2mm", "39.4mm", "RightMargin:2mm",0, 1)

                    LODOP.ADD_PRINT_TEXT( "40.4mm", "3mm", "37mm", "3mm", "产地：")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                    LODOP.ADD_PRINT_TEXT( "40.4mm", "3mm", "RightMargin:3mm", "3mm", "见吊牌或水洗标")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                    LODOP.ADD_PRINT_TEXT( "42.7mm", "3mm", "37mm", "3mm", "经销商：")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                    LODOP.ADD_PRINT_TEXT( "42.7mm", "3mm", "RightMargin:3mm", "3mm", "好超值(天津)信息技术有限公司")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                    LODOP.ADD_PRINT_TEXT("45.1mm", "-2mm", "RightMargin:3mm", "3mm", "本品牌商品销售价以小程序扫码结果为准")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)


                    LODOP.ADD_PRINT_TEXT("51mm", priceSignleft, "3mm", "5mm", "¥")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 10)
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                    LODOP.ADD_PRINT_TEXT("49mm",priceOneLeft, priceOneWidth, "5mm", sale_price[0])
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 24)
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                    LODOP.ADD_PRINT_TEXT( "53.9mm",priceTwoLeft,"15mm","5mm","." + sale_price[1])
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 12)
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                    LODOP.ADD_PRINT_BARCODE("63mm","5mm","34mm","7mm","128Auto",item.code)

                    LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 0)

                    LODOP.ADD_PRINT_TEXT("71mm", "6mm", "40mm", "6mm", item.code)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 10)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 2)
                    LODOP.SET_PRINT_STYLEA(0, "Horient", 2)

                    if (item.co_model && TAG_IMG[item.co_model].src) {
                        LODOP.ADD_PRINT_IMAGE("77.5mm", "31mm","7mm","7mm",`<img  border='0' src='${TAG_IMG[item.co_model].src}' />`)
                        LODOP.SET_PRINT_STYLEA(0, "Stretch", 2) //(可变形)扩展缩放模式
                        LODOP.SET_PRINT_STYLEA(0, "Vorient", 1)
                    }

                    LODOP.SET_PRINT_COPIES(number) //指定份数
                    // LODOP.PRINT_DESIGN();
                    //LODOP.SET_PRINTER_INDEX('吊牌打印机');
                    // LODOP.PREVIEW();
                    LODOP.PRINT() //执行打印
                }
                else if(temp_type == 5){ // 6*4 TSC打印机打印
                    print_nums = $('#print_nums').html()*1;//累计打印数量
                    print_nums += 1;
                    $('#print_nums').html(print_nums);
                    //追加生产记录
                    $("#print_log tbody").prepend('<tr><td>'+item.code+'</td><td>'+1+'</td></tr>');

                    // let LODOP = getLodop() //获得打印
                    LODOP.SET_LICENSES("","94D9E78FC9B721D1EBF7165B69114B39A35","","");
                    //检测价格长度
                    if (priceLength == 1) {
                        priceSignleft = "13mm"
                        priceOneLeft = "16mm"
                        priceOneWidth = "12mm"
                        priceTwoLeft = "20mm"
                    } else if (priceLength == 2) {
                        priceSignleft = "11mm"
                        priceOneLeft = "14mm"
                        priceOneWidth = "15mm"
                        priceTwoLeft = "22mm"
                    } else if (priceLength == 3) {
                        priceSignleft = "9mm"
                        priceOneLeft = "12mm"
                        priceOneWidth = "18mm"
                        priceTwoLeft = "25mm"
                    } else if (priceLength == 4) {
                        priceSignleft = "6mm"
                        priceOneLeft = "8mm"
                        priceOneWidth = "21mm"
                        priceTwoLeft = "26mm"
                    }

                    LODOP.PRINT_INITA("0mm", "0mm", "60", "42.1mm", "任务名")
                    LODOP.SET_PRINT_PAGESIZE(2, 600, 421, "")

                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 16)
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                    if (item.co_model && TAG_IMG[item.co_model].src) {
                        LODOP.ADD_PRINT_IMAGE("2mm","6mm","2.5mm","2.5mm",`<img  border='0' src='${TAG_IMG[item.co_model].src}' />`)
                        LODOP.SET_PRINT_STYLEA(0, "Stretch", 2) //(可变形)扩展缩放模式
                        LODOP.SET_PRINT_STYLEA(0, "Vorient", 0)
                    }

                    //尺码
                    LODOP.ADD_PRINT_TEXT("12mm", "6mm", "14mm", "3mm", "尺码:")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                    //规格
                    var specTop="10.5mm"
                    var specSize=13
                    if (item.spec_value.length >5 && item.spec_value.length <=7){
                        specTop="11.5mm"
                        specSize=10
                    }else if(item.spec_value.length >7){
                        specTop="12.5mm"
                        specSize=8
                    }
                    LODOP.ADD_PRINT_TEXT(specTop,"14mm","27mm","3mm",item.spec_value.substring(0, 12))
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", specSize)
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                    //横线 实线
                    LODOP.ADD_PRINT_LINE("17mm","2mm", "17mm", "RightMargin:2mm",0, 1)

                    //品牌
                    LODOP.ADD_PRINT_TEXT("18mm", "6mm", "14mm", "3mm", "品牌:")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                    //品牌
                    LODOP.ADD_PRINT_TEXT("18mm","1mm","RightMargin:3mm","3mm",item.brand_name)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                    //货号
                    LODOP.ADD_PRINT_TEXT("20.5mm", "6mm", "14mm", "3mm", "货号:")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                    //货号名称
                    LODOP.ADD_PRINT_TEXT("20.5mm","1mm","RightMargin:3mm","3mm",item.spu_no)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                    //吊牌价
                    LODOP.ADD_PRINT_TEXT("23mm", "6mm", "14mm", "3mm", "吊牌价:")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                    //吊牌价
                    LODOP.ADD_PRINT_TEXT("23mm","1mm","RightMargin:3mm","3mm","￥"+item.tag_price)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                    //横线 实线
                    LODOP.ADD_PRINT_LINE("26mm","2mm", "26mm", "RightMargin:2mm",0, 1)

                    //产地
                    LODOP.ADD_PRINT_TEXT("27mm","6mm","8mm","2mm","产地:")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 4)
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                    LODOP.ADD_PRINT_TEXT("27mm","1mm","RightMargin:3mm","2mm","见吊牌或水洗标")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 4)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                    //经销商
                    LODOP.ADD_PRINT_TEXT("29mm","6mm","10mm","2mm","经销商:")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 4)
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                    LODOP.ADD_PRINT_TEXT("29mm","1mm","RightMargin:3mm","2mm","好超值(天津)信息技术有限公司")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 4)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                    LODOP.ADD_PRINT_TEXT("31mm","1mm","RightMargin:3mm","2mm","本品牌商品销售价以小程序扫码结果为准")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 4)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                    //黑色背景
                    // LODOP.ADD_PRINT_HTM("34mm","0mm","50mm","8mm","<div style='background-color: #0A0E11; width: 50mm;height: 8mm;'></div>");//一个矩形设置颜色为#FF0000
                    // LODOP.SET_PRINT_STYLE("FontColor", "#fff")
                    //销售价 ￥
                    LODOP.ADD_PRINT_TEXT("35mm", priceSignleft, "5mm", "4mm", "¥")
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 9)
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                    //元
                    LODOP.ADD_PRINT_TEXT("33.5mm", priceOneLeft, priceOneWidth, "4mm", sale_price[0])
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 20)
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                    // LODOP.SET_PRINT_STYLEA(0, "FontColor", "#FFF")

                    //角分
                    LODOP.ADD_PRINT_TEXT("37mm",priceTwoLeft,"15mm","4mm","." + sale_price[1])
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 9)
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                    LODOP.SET_PRINT_STYLE("FontColor", "#000")
                    //条形码
                    LODOP.ADD_PRINT_BARCODE("43.2mm","9mm","37.5mm","8mm","128Auto",item.code)
                    LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 0)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 2)

                    LODOP.ADD_PRINT_TEXT("51mm", "7mm", "34mm", "6mm", item.code)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 10)
                    LODOP.SET_PRINT_STYLEA(0, "Alignment", 2)
                    LODOP.SET_PRINT_STYLEA(0, "Horient", 2)

                    LODOP.SET_PRINT_COPIES(1) //指定份数
                    // LODOP.PRINT_DESIGN();
                    //LODOP.SET_PRINTER_INDEX('吊牌打印机');
                    if (temp_type == 5) {
                        LODOP.SET_PRINT_STYLEA(0,"AngleOfPageInside",180);
                    }
                    LODOP.SET_PRINT_MODE("POS_BASEON_PAPER",true);//设置以纸张边缘为基点
                    // LODOP.PREVIEW();
                    LODOP.PRINT() //执行打印
                }
                else if(temp_type == 1 || temp_type == 2){//艾利打印
                    $.ajax({
                        url: 'http://localhost:81/BigoffsLabelPrint',
                        type: 'GET',
                        data:{
                                size: item.spec_value,
                                brand_name: item.brand_name,
                                spu_no: item.spu_no,
                                tag_price: item.tag_price,
                                sale_price: item.sale_price,
                                code: item.code,
                                epc_code: item.epc_code,
                                icon_type: item.co_model,
                                temp_type: temp_type
                        },
                        dataType: 'JSON',
                        success: function (result) {
                            if (result.isCheckDataOk == true){//数据已发送打印机
                                //累计打印数量
                                print_nums = $('#print_nums').html()*1;
                                print_nums += 1;
                                $('#print_nums').html(print_nums);
                                //追加生产记录
                                $("#print_log tbody").prepend('<tr><td>'+item.code+'</td><td>'+1+'</td></tr>');
                                console.log('艾利打印机数据已发送, code'+item.code+"; result:", result);
                            }else{
                                layer.msg(result.checkMessage,{icon:2,time:1500});
                            }
                        },
                        error: function (error) {
                            console.log(typeof (error));
                            layer.msg("吊牌打印失败！" ,{icon:2,time:1500})
                        }
                    });
                }else if(temp_type == 9 || temp_type == 10){//艾利打印
                    $.ajax({
                        url: 'http://localhost:82/api/print',
                        type: 'GET',
                        data:{
                            size: item.spec_value,
                            brand_name: item.brand_name,
                            spu_no: item.spu_no,
                            tag_price: item.tag_price,
                            sale_price: item.sale_price,
                            code: item.code,
                            epc_code: item.epc_code,
                            icon_type: item.co_model,
                            temp_type: temp_type
                        },
                        dataType: 'JSON',
                        success: function (result) {
                            if (result.status == "Print success"){//数据已发送打印机
                                //累计打印数量
                                print_nums = $('#print_nums').html()*1;
                                print_nums += 1;
                                $('#print_nums').html(print_nums);
                                //追加生产记录
                                $("#print_log tbody").prepend('<tr><td>'+item.code+'</td><td>'+1+'</td></tr>');
                                console.log('艾利打印机数据已发送, code'+item.code+"; result:", result);
                            }else{
                                layer.msg(result.checkMessage,{icon:2,time:1500});
                            }
                        },
                        error: function (error) {
                            console.log(typeof (error));
                            layer.msg("吊牌打印失败！" ,{icon:2,time:1500})
                        }
                    });
                }
                else if(temp_type == 6){
                    LODOP.PRINT_INITA("0mm", "0mm", "60", "42.1mm", "任务名")
                    LODOP.SET_PRINT_PAGESIZE(0, 600, 421, "")
                    //横线 实线
                    LODOP.ADD_PRINT_LINE("1mm","0mm", "1mm", "RightMargin:0.1mm",0, 1)
                    LODOP.SET_PRINT_COPIES(20) //指定份数
                    // LODOP.PREVIEW();
                    LODOP.PRINT() //执行打印
                }
            })
        }

        //清空
        $().click(function () {
            $('#tableb_copy').html($('#tableb').html());
        });
        //条码打印时，加减打印数量
        $(function () {
            //加减
            $(".rate_ul_r_jian").click(function(){
                var values=$(this).siblings(".rate_ul_r_input");
                var valuesNumber=parseFloat(values.val());
                if(valuesNumber==NaN){
                    values.val(0);

                }else{
                    if(valuesNumber<1){
                        values.val(0);
                    }else{
                        values.val(valuesNumber-1);
                    }
                }
            });
            $(".rate_ul_r_jia").click(function(){
                var values=$(this).siblings(".rate_ul_r_input");
                var valuesNumber=parseFloat(values.val());
                if(valuesNumber==NaN){
                    values.val(0);

                }else{
                    if(valuesNumber>=1000000){
                        values.val(1000000);
                    }else{
                        values.val(valuesNumber+1);
                    }
                }
            });
        });
        //自定义验证规则
        form.verify({
            temp_type: function(value){
                if(value.length == 0){
                    return '请选择吊牌模版';
                }
            },
            produce_type: function (value) {
                if(value.length == 0){
                    return '请选择生产类型';
                }
            },
            in_store_type: function (value, data) {
                if(value.length == 0 && $('#produce_type').val() == 2){
                    return '请选择入库方式';
                }
            }
        });

        $('#barcode').blur(function () {
            $('#barcode').css('border-color', '');
        });

        //切换吊牌模版
        form.on('select(temp_type)', function(data){
            console.log('吊牌模版====', data.value, $('#produce_type').val());

            var e = data.elem;
            //获取选中的文本
            var text =e[e.selectedIndex].text;
            $('#temp_name').html(text);
            $('#epc_switch_instore_barcode').prop("checked", false);

            stopAndDisconnect();//关闭epc
            form.render();
        });
        //切换生产类型
        form.on('select(produce_type)', function(data){
            if (data.value == 1){//调拨生产
                console.log('切换生产类型===========， 模版类型：', $('#temp_type').val());
                //隐藏新品入库相关div
                $('#in_store_print').hide();
                $('#in_store_barcode_print_show').html('');
                $('#in_store_barcode_print_show_rfid').html('');
                $('#in_store_print_rfid').hide();
                $('#in_store_type_div').hide();

                // $('#allot_barcode_print_show').html('');

                //展示调拨相关div
                if ($('#temp_type').val() == 3){//rifd吊牌
                    //隐藏普通吊牌的div
                    $('#allot_unique_code_print_show').html('');
                    $('#allot_barcode_print_show').html('');
                    $('#allot_print').hide();
                    //展示rfid的吊牌div
                    element.tabChange('tab_info_rfid', 1);//切换到：店内码
                    $('#allot_unique_code_print_show_rfid').html($('#allot_unique_code_print_rfid').html());
                    $('#allot_barcode_print_show_rfid').html('');
                    $('#allot_print_rfid').show();
                    $('#epc_switch_unique_rfid').prop("checked", false);
                    $('#print_type_rfid').val(1);

                    $('#unique_code_rfid').focus();

                }else{//普通吊牌
                    //隐藏rfid吊牌的div
                    $('#allot_unique_code_print_show_rfid').html('');
                    $('#allot_barcode_print_show_rfid').html('');
                    $('#allot_print_rfid').hide();
                    //展示普通吊牌的div
                    element.tabChange('tab_info', 1);//切换到：店内码
                    $('#allot_unique_code_print_show').html($('#allot_unique_code_print').html());
                    $('#allot_barcode_print_show').html('');
                    $('#allot_print').show();
                    $('#epc_switch_unique').prop("checked", false);
                    $('#print_type').val(1);
                }
                $('#allot_no').show();
            }else if(data.value == 2){//新品入库
                //隐藏调拨相关div
                $('#allot_barcode_print_show').html('');
                $('#allot_unique_code_print_show').html('');
                $('#allot_print').hide();
                $('#allot_barcode_print_show_rfid').html('');
                $('#allot_unique_code_print_show_rfid').html('');
                $('#allot_print_rfid').hide();
                $('#allot_no').hide();
                //展示新品入库div
                $('#in_store_type_div').show();
                if ($('#temp_type').val() == 3){//rfid吊牌
                    //隐藏普通吊牌div
                    $('#in_store_barcode_print_show').html('');
                    $('#in_store_print').hide();
                    //展示rfid吊牌div
                    $('#in_store_barcode_print_show_rfid').html($('#in_store_barcode_print_rfid').html());
                    $('#epc_switch_instore_barcode').prop("checked", false);
                    $('#in_store_print_rfid').show();

                    $('#arrival_order_no_rfid').focus();
                }else{//普通吊牌
                    //隐藏rfid吊牌div
                    $('#in_store_barcode_print_show_rfid').html('');
                    $('#in_store_print_rfid').hide();
                    //展示普通吊牌div
                    $('#in_store_barcode_print_show').html($('#in_store_barcode_print').html());
                    $('#epc_switch_instore_barcode').prop("checked", false);
                    $('#in_store_print').show();
                }
            }
            stopAndDisconnect();//关闭epc
            form.render();
        });

        /*** 新品入库 前置rfid吊牌 条码打印吊牌*/
        //新品入库 rfid吊牌 到货预约单回车操作
        $("body").on("keydown", "#box_no", function (e) {
            if(e.keyCode == 13) {
                getBoxInfo(true);
                return false;
            }
        });
        //勾选 原残
        form.on('checkbox(imperfect)', function () {
            getBoxInfo(false);
            return false;
        });

        function getBoxInfo (printtag = true){
            if($("#box_no").val().length == 0){
                layer.msg('请输入质检箱号', {icon: 5,anim: 6});
                return false;
            }
            //通过箱号获取预约单信息
            $.ajax({
                url: '/tagPrint/getBoxInfo',
                type: 'post',
                data: JSON.stringify({
                    box_no : $("#box_no").val(),
                    imperfect: $("#imperfect").is(':checked'),
                    temp_type: $('#temp_type').val(),
                    sign: 1
                }),
                dataType: "json",
                contentType: 'application/json',
                processData: false,
                success: function (data) {
                    if (data.code !== 200){
                        layer.msg(data.msg,{icon:2,time:1500});
                        $('#box_no').val('');
                        $("#box_no").focus();
                    }else{
                        $("#arrival_order_no").val(data.data.arrival_no);
                        $("#quality_id").val(data.data.quality_id);
                        $("#product_num").text(data.data.num);
                        $("#in_product_num").text(data.data.in_num);
                        if(data.data.barcode != "" && printtag){
                            $("#barcode").val(data.data.barcode);

                            //打印吊牌
                            tagForm = layui.form.val(`tag_form`);
                            console.log("again_print====="+$("#again_print").is(':checked'));
                            //补打只打印一个吊牌
                            if($("#again_print").is(':checked')){
                                tagForm.barcode_num = 1;
                            }else{
                                tagForm.barcode_num = data.data.num;
                            }
                            print_instore_barcode_tag_rfid_front(tagForm);
                            return false;

                        }else{
                            $("#barcode").val("");
                            $("#barcode").focus();
                            return false;
                        }
                    }
                },
                error: function (e) {
                    layer.msg("检测磁扣是否在磁扣池中失败！",{icon:2,time:1500})
                },
            });
            return false;
        }

        //新品入库 前置rfid吊牌 打印按钮 打印吊牌
        form.on('submit(instore_print_barcode_rfid_front)', function(data){
            data.field.barcode_num = 1;
            print_instore_barcode_tag_rfid_front(data.field);
            return false;
        });

        //新品入库 前置-rfid吊牌 监听 监听条形码回车 获取吊牌信息
        $("body").on("keydown","#barcode", function (e) {
            if(e.keyCode == 13) {
                $('#check_unique_info').hide();
                data = layui.form.val(`tag_form`);
                if ($('#temp_type').val() == 4 || $('#temp_type').val() == 5){
                    data.barcode_num = 1;//按照条码打印时，只打印一张
                }
                //补打只打印一个吊牌
                if($("#again_print").is(':checked')){
                    data.barcode_num = 1;
                }
                print_instore_barcode_tag_rfid_front(data);
                return false;
            }
        });

        //新品入库 前置rfid吊牌 条码打印吊牌
        function print_instore_barcode_tag_rfid_front(info) {
            console.log("print_instore_barcode_tag_rfid_front==============");
            $('#print_unique_info').html('');
            $('#wait_bind').html('');

            if (info.box_no.length == 0){
                // $('#box_no').css('border-color', 'red');
                layer.msg('请输入质检箱号', {icon: 5,anim: 6});
                $('#box_no').focus();
                $('#barcode').val("");
                return false;
            }
            if (info.barcode.length == 0){
                $('#barcode').css('border-color', 'red');
                layer.msg('请扫描条形码', {icon: 5,anim: 6});
                $('#barcode').focus();
                return false;
            }
            info.print_type = 2;//新品入库
            //rfid开关 0关闭 1开启
            // if($('#epc_switch_instore_barcode').is(":checked")){
            //     info.epc_switch = 1;
            // }else{
            //     info.epc_switch = 0;
            // }
            //rfid开关 0关闭 1开启
            info.epc_switch = 0;//传递关闭值，先打印吊牌，后绑定epc

            console.log('新品入库 条码 前置rfid 打印吊牌', info);

            $.post('/tagPrint/printFrontTagRfid', info, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    $('#barcode').blur();
                    //赋值价签打印的log_id,绑定rfid时需要更新上epc码
                    $('#tag_print_log_id').val(r.data.tag_print_log_id);

                    if (info.barcode_num == 1){
                        //展示商品基础信息
                        show_print_tag_info(r.data.tag_info[0], info.epc_switch);
                    }
                    //货品数量
                    $("#product_num").text(r.data.num);
                    $("#in_product_num").text(r.data.in_num);
                    $("#print_barcode_num").val(r.data.num);

                    //打印吊牌
                    print_tag(r.data.tag_info, $('#temp_type').val(), r.data.log.num);
                    $('#barcode').focus();
                    $('#in_store_unique_code_rfid').val(r.data.tag_info.code);
                } else {
                    layer.msg(r.msg, {
                        icon: 2,
                        time: 3000
                    });
                    $('#barcode').val('');
                    $('#in_store_unique_code_rfid').val('');
                    $('#print_unique_info').html('');
                    $('#wait_bind').html('');
                }
                loading.hide();
            });
        }

        //新品入库 绑定入库按钮
        form.on('submit(instore_bind_rfid)', function(data){

            if (data.field.box_no.length == 0){
                // $('#box_no').css('border-color', 'red');
                layer.msg('请输入质检箱号', {icon: 5,anim: 6});
                $('#box_no').focus();
                $('#barcode').val("");
                return false;
            }

            if (data.field.barcode.length == 0){
                $('#barcode').css('border-color', 'red');
                layer.msg('请扫描条形码', {icon: 5,anim: 6});
                $('#barcode').focus();
                return false;
            }

            if (data.field.tag_unique_code.length == 0){
                layer.msg('请先打印吊牌', {icon: 5,anim: 6});
                $('#barcode').focus();
                return false;
            }

            $('#print_unique_info').html('');

            if ($('#temp_type').val() == 4 || $('#temp_type').val() == 5) {//TSC打印机打印吊牌绑定epc并入库
                now_epc_code = $("#epc_code").val();
                unique_code = $("#tag_unique_code").val();
                tag_print_log_id = $('#tag_print_log_id').val();
                $.ajax({
                    url: '/tagPrint/frontBindEpcInStore',
                    type: 'post',
                    data: JSON.stringify({
                        epc_code: now_epc_code,
                        unique_code: unique_code,
                        tag_print_log_id: tag_print_log_id,
                        barcode: $("#barcode").val(),
                        arrival_order_no: $("#arrival_order_no").val()
                    }),
                    dataType: "json",
                    contentType: 'application/json',
                    processData: false,
                    success: function (data) {
                        if (data.code !== 200) {
                            layer.msg(data.msg,{icon:2,time:1500})
                        } else {
                            this.enterConfirm = function (event) {
                                if (event.keyCode === 13) {
                                    $(".layui-layer-btn0").click();
                                    return false; //阻止系统默认回车事件
                                }
                            };
                            $("#epc_code").val('');
                            $('#barcode').val("");
                            $('#barcode').focus();
                            $('#check_unique_info').hide();
                            $('#wait_bind').html('绑定入库成功');
                            goodsCodes.push(now_epc_code) // 合并数组

                            //获取质检箱入库数量
                            $.ajax({
                                url: '/tagPrint/getBoxInfo',
                                type: 'post',
                                data: JSON.stringify({
                                    box_no: $("#box_no").val(),
                                    imperfect: $("#imperfect").is(':checked'),
                                    temp_type: $('#temp_type').val(),
                                    sign: 2
                                }),
                                dataType: "json",
                                contentType: 'application/json',
                                processData: false,
                                success: function (data) {
                                    if (data.code !== 200) {
                                        layer.msg(data.msg,{icon:2,time:1500});
                                    } else {
                                        $("#product_num").text(data.data.num);
                                        $("#in_product_num").text(data.data.in_num);
                                    }
                                },
                                error: function (e) {
                                    layer.msg("检测磁扣是否在磁扣池中失败！",{icon:2,time:1500})
                                },
                            });
                        }
                    },
                    error: function (e) {
                        layer.msg("店内码绑定epc且入库失败！",{icon:2,time:1500})
                    },
                });
            }
            return false;
        });
    })
</script>
</body>
</html>