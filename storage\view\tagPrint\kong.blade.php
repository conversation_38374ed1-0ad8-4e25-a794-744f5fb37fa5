<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <link href="/static/layui/css/layui.css" rel="stylesheet"/>
    <link href="/static/css/pearCommon.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/static/css/myui.css"/>
    <script src="/static/js/jquery.min.js"></script>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form layui-form-pane" action="">
            <div class="layui-row mt-7">
                <div class="layui-col-xs3">
                    <label class="layui-form-label">仓库</label>
                    <div class=" layui-input-inline" style="width: 60%">
                        <select name="warehouse" class="layui-select"  xm-select="warehouse" xm-select-search xm-select-skin="default" xm-select-show-count="4">
                            <option value=""></option>
                            @foreach($warehouse_list as $warehouse_info)
                                <option value="{{$warehouse_info['id']}}"}}>{{$warehouse_info['name']}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="layui-col-xs3">
                    <label class="layui-form-label" >差异处理</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <select name="diff" class="layui-select" id="diff">
                            <option value=""></option>
                            <option value="-1" selected>已处理</option>
                            <option value="1">未处理</option>
                        </select>
                    </div>
                </div>
                <div class="layui-col-xs3">
                    <label class="layui-form-label">吊牌打印时间</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <input type="text" name="print_time" autocomplete="off" class="layui-input" id="print_time">
                    </div>
                </div>
                <div class="layui-col-xs3">
                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="query" style="margin: 5px">
                        <i class="layui-icon layui-icon-search"></i>
                        查询
                    </button>
                    <button type="reset" class="pear-btn pear-btn-md" style="margin: 5px">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="export" style="margin: 5px">
                        <i class="layui-icon layui-icon-export"></i>
                        导出
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <table id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

<script src="/static/layui/layui.js"></script>
<script src="/static/js/clipboard/clipboard.min.js"></script>
<script>
    layui.use(['jquery', 'table', 'element', 'form', 'layer', 'laydate', 'iframeTools'], function () {
        var table = layui.table;
        var $ = layui.jquery;
        var form = layui.form;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var iframeTools = layui.iframeTools;
        //时间插件
        laydate.render({
            elem: '#print_time'
            , type: 'datetime'
            , range: true,
            trigger: 'click',
            value: '{{$date}}'
        });

        var diff = $('#diff').val() ? $('#diff').val() : 0;
        var print_time = $('#print_time').val() ? $('#print_time').val() : 0;
        //表格初始化
        var dataTable = table.render({
            elem: '#dataTable'
            , url: "/tagPrint/kongList"
            , page: true //开启分页
            , skin: 'line'
            , toolbar: '#lay-toolbar'
            ,limits: [10,50,80,100]
            ,where: {search: {diff:diff, print_time:print_time }, export: 0}
            ,page: {curr: 1}
            ,limit:10
            , cols: [[ //表头
                ,{field:'warehouse_name', title: '仓库', width: 180}
                ,{field:'supplier_name', title: '供应商', width:170}
                ,{field:'shelf_code', title: '货架号', width:120}
                ,{field:'spu_id', title: 'SPU', width:100}
                ,{field:'sku_id', title: 'SKU', width:100}
                ,{field:'barcode', title: '条形码', width:150}
                ,{field:'in_stock_no', title: '批次号', width:200}
                ,{field:'unique_code', title: '店内码', width:120}
                ,{field:'instore_at', title: '入库时间', width:185}
                ,{field:'created_at', title: '吊牌打印时间', width:185}
            ]]
        });

        //查询按钮
        var median;
        form.on('submit(query)', function (data) {
            if(data.field.print_time == ""){
                data.field.print_time = "{{$date}}";
            }
            table.reload('dataTable', {//重载
                where: {search: data.field, export: 0},
                page: {curr: 1},
                done: function (res) {
                    median = this;
                    if(res.code != 200){
                        layer.msg(res.msg, {icon: 2, time: 1500})
                        return false;
                    }
                }, page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
            return false;
        });
        //导出按钮
        form.on('submit(export)', function (data) {
            if(data.field.print_time == ""){
                data.field.print_time = "{{$date}}";
            }
            $.ajax({
                url: "/tagPrint/kongList",
                type: 'post',
                data: JSON.stringify({search: data.field, export: 1}),
                dataType: "json",
                contentType: 'application/json',
                processData: false,
                success: function (data) {
                    if (data.code == 200) {
                        window.open(data.data.url)
                    } else {
                        layer.msg(data.msg, {icon: 2, time: 1500})
                    }
                },
                error: function (e) {
                    layer.close(load);
                    layer.msg('提交失败！', {icon: 2})
                },
            });
            return false;
        });

    })
</script>
</body>
</html>