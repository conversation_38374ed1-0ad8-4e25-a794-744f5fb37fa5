init()
function init() {
    document.write("<script src=\"http://127.0.0.1:8000/CLodopfuncs.js\"></script>\n");
    document.write('<div id="boxCodePrint" style="display: none;">\n' +
        '    <style>\n' +
        '        .box-print-table{border-collapse: collapse;text-align: center;width: 77mm;height: 98mm;padding: 0;margin: 0;background-color: #ffffff;}.box-print-table tr td{padding: 0;margin: 0;}#boxCodePrint{padding: 0;margin: 0;}.box-print-table .box-print-main{height: 8mm;}.brand_names{font-size: 30px;font-weight: 700;}.box-main-info{font-size: 18px;width: 55mm;font-weight: 700;}.box-main-title{font-size: 14px;width: 24mm;}.goods_num{font-size: 46px;font-weight: bolder}\n' +
        '    </style>\n' +
        '    <table border=1 cellSpacing=0 cellPadding=1 class="box-print-table">\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="78mm" colspan="2" class="brand_names"></td>\n' +
        '        </tr>\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="20mm" class="box-main-title">供应商</td>\n' +
        '            <td width="58mm" class="box-main-info sup_name"></td>\n' +
        '        </tr>\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="20mm" class="box-main-title">退返单</td>\n' +
        '            <td width="58mm" class="box-main-info"><span class="source_no"></span></td>\n' +
        '        </tr>\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="20mm" class="box-main-title">件数</td>\n' +
        '            <td width="58mm" class="box-main-info "><span class="goods_num"></span></td>\n' +
        '        </tr>\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="20mm" class="box-main-title">退返备注</td>\n' +
        '            <td width="58mm" class="box-main-info remark"></td>\n' +
        '        </tr>\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="20mm" class="box-main-title">装箱人</td>\n' +
        '            <td width="58mm" class="box-main-info pk_admin_name"></td>\n' +
        '        </tr>\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="20mm" class="box-main-title">装箱时间</td>\n' +
        '            <td width="58mm" class="box-main-info pk_time"></td>\n' +
        '        </tr>\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="20mm" class="box-main-title">重量(kg)</td>\n' +
        '            <td width="58mm" class="box-main-info weight"></td>\n' +
        '        </tr>\n' +
        '        <tr style="height: 17mm">\n' +
        '            <td colspan="2">\n' +
        '            </td>\n' +
        '        </tr>\n' +
        '    </table>\n' +
        '</div>\n');
}

/**
 * 调拨箱号打印
 * @param data
 * [{
 *  source_no,
 *  w_id,
 *  in_w_name,
 *  brand_name_tips,
 *  pk_num,
 *  ask_date,
 *  pk_admin_name,
 *  pk_time,
 *  weight,
 *  box_no
 * }]
 */
function backBoxPrint(data) {
    console.log('打印箱码-data',JSON.stringify(data))
    $.each(data, function (k, v) {
        var brand_names_str = v.brand_names
        if (v.brand_names && v.brand_names.length > 8) {
            brand_names_str = v.brand_names.substring(0, 8) + "...";
        }
        var w_name = v.w_name
        if (v.w_name && v.w_name.length > 8) {
            w_name = v.w_name.substring(0, 8);
        }
        $(".brand_names").html(brand_names_str);
        $(".sup_name").html(v.sup_name);
        $(".source_no").html(v.source_no);
        $(".goods_num").html(v.goods_num);
        var remark_str = v.remark
        if (v.remark && v.remark.length > 10) {
            remark_str = v.remark.substring(0, 10) + "...";
        }
        $(".remark").html(remark_str);
        $(".pk_admin_name").html(v.pk_admin_name + "/" + w_name);
        $(".pk_time").html(v.pk_time);
        $(".weight").html(v.weight);

        LODOP.SET_LICENSES("","94D9E78FC9B721D1EBF7165B69114B39A35","","");
        LODOP.PRINT_INIT("");
        LODOP.SET_PRINT_PAGESIZE(1, '80mm', '100mm', "");
        LODOP.ADD_PRINT_TABLE("1mm", 0, 0, "100%", document.getElementById("boxCodePrint").innerHTML);
        LODOP.ADD_PRINT_BARCODE("81mm", "9mm", "60mm", "16mm", "128Auto", v.box_no);
        if (1 == v.allot_type) {
            LODOP.SET_PRINTER_INDEXA("箱唛打印");
        }
         //LODOP.PREVIEW();
        LODOP.PRINT();
    });
}