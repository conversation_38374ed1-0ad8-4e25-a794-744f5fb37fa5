<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/layui/css/layui.css" />
    <link rel="stylesheet" href="/static/css/pearTab.css" />
    <link rel="stylesheet" href="/static/css/pearTheme.css" />
    <link rel="stylesheet" href="/static/css/pearLoad.css" />
    <link rel="stylesheet" href="/static/css/pearFrame.css" />
    <link rel="stylesheet" href="/static/css/pearAdmin.css" />
    <link rel="stylesheet" href="/static/css/pearNotice.css" />
    <link rel="stylesheet" href="/static/css/pearMenu.css" />
    <link rel="stylesheet" href="/static/css/pearCommon.css"/>
    <link rel="stylesheet" href="/static/css/myui.css"/>
    <link rel="stylesheet" href="/static/layui/formSelects-v4.css"/>

    <script src="/static/js/jquery.min.js"></script>
    <style id="pearone-bg-color">
    </style>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form layui-form-pane" action="">
            <div class="layui-row mt-7">
                <div class="layui-col-xs6">
                    <label class="layui-form-label">仓库</label>
                    <div class="layui-input-inline" style="width: 60%;">
                        <select name="warehouse_ids" class="layui-select"  xm-select="warehouse" xm-select-search xm-select-skin="default" xm-select-show-count="4">
                            <option value=""></option>
                            @foreach($warehouse_list as $warehouse_info)
                                <option value="{{$warehouse_info['id']}}">{{$warehouse_info['name']}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="layui-col-xs6">
                    <label class="layui-form-label">出库类型</label>
                    <div class="layui-input-inline"  style="width: 60%;">
                        <select name="type" class="layui-select">
                            <option value=""></option>
                            @foreach($task_type as $key => $value)
                                <option value="{{$key}}">{{$value}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>

            <div class="layui-row mt-7">
                <div class="layui-col-xs6">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <select name="status" id="status">
                            <option value=""></option>
                            @foreach($status_list as $key => $name)
                                <option value="{{$key}}">{{$name}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="layui-col-xs6">
                    <label class="layui-form-label">出库单号</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <textarea name="serial_no" id="serial_no" class="layui-input" style="height: 100px"></textarea>
{{--                        <input type="text" name="serial_no" id="serial_no" placeholder="" class="layui-input">--}}
                    </div>
                </div>
            </div>

            <div class="layui-row mt-7">
                <div class="layui-col-xs6">
                    <label class="layui-form-label">货品</label>
                    <div class="layui-input-inline"  style="width: 30%;">
                        <select name="search_type" lay-filter="search_type" id="search_type" >
                            <option value="">请选择查询类型</option>
                            @foreach($search_type as $key => $value)
                                <option value="{{$key}}">{{$value}}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="layui-input-inline" style="width: 30%">
                        <input type="text" name="search_value" id="search_value"  class="layui-input">
                    </div>
                </div>


                <div class="layui-col-xs6">
                    <label class="layui-form-label">创建时间</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <input type="text" name="date_range" autocomplete="off" class="layui-input" id="pick-order-time">
                    </div>
                </div>
            </div>

            <div class="layui-row mt-7">
                <div class="layui-col-xs6">
                    <label class="layui-form-label">来源单号</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <textarea name="order_no" id="order_no" class="layui-input" style="height: 100px"></textarea>
                    </div>
                </div>
                <div class="layui-col-xs6">
                    <label class="layui-form-label">货品数量</label>
                    <div class="layui-input-block"  style="width: 60%;">
                        <input type="checkbox" name="export_num" value="1" lay-skin="primary" title="待导单数大于0" >
                        <input type="checkbox" name="exception_num" value="1" lay-skin="primary" title="出库失败大于0">
                    </div>
                </div>

                <div class="layui-col-xs6" style="margin-top: 5px;">
                    <label class="layui-form-label">拣货替码</label>
                    <div class="layui-input-inline"  style="width: 60%;">
                        <select name="pick_replace" class="layui-select">
                            <option value=""></option>
                            @foreach($pick_replace as $key => $value)
                                <option value="{{$key}}">{{$value}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>



            <div class="layui-form-item">
                <div  style="display: flex;justify-content: flex-end;">
                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="query" style="margin: 5px">
                        <i class="layui-icon layui-icon-search"></i>
                        查询
                    </button>
                    <button type="reset" class="pear-btn pear-btn-md" style="margin: 5px">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="export" style="margin: 5px">
                        <i class="layui-icon layui-icon-export"></i>
                        导出
                    </button>
                </div>

            </div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <table id="list" lay-filter="list"></table>
        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm" lay-event="exportTask">导出拣货单</button>
                <button class="layui-btn layui-btn-sm" lay-event="createSorting">创建分拣任务</button>
            </div>
        </script>
    </div>
</div>

<script type="text/html" id="detail_panel">
    <a class="pear-btn pear-btn-sm" lay-event="checkDetail">查看明细</a>
</script>
<script src="/static/layui/layui.js"></script>
<script type="text/javascript">
    layui.use(['pearAdmin', 'pearSocial', 'element', 'layer', 'form', 'util', 'pearTab', 'jquery', 'table', 'upload','formSelects', 'iframeTools', 'laydate'], function() {
        var formSelects = layui.formSelects;
        var table = layui.table;
        var pearTab = layui.pearTab;
        var iframeTools = layui.iframeTools;
        var supplier_ids = formSelects.value('supplier', 'val');
        var $ = layui.jquery;
        var laydate = layui.laydate;
        var form = layui.form;
        var block_list = []; //货品区数据
        var select_ids = []; //货品区数据

        //表格初始化
        var dataTable = table.render({
            elem: '#list',
            url:'/outStock/list',
            toolbar: '#toolbarDemo', //开启头部工具栏，并为其绑定左侧模板
            method: 'post',
            page: true ,//开启分页
            limits: [30,50,100,200],
            limit:30,
            skin: 'line',
            cols: [[
                {type: 'checkbox', fixed: 'left'},
                {field:'id', title:'ID', width:80, fixed: 'left'},
                {field:'serial_no', title: "出库单号"},
                {field:'order_no', title: "来源单号"},
                {field:'ware_name', title: '仓库'},
                {field:'type_name', title: '类型'},
                {field:'status_name', title: '状态'},
                {field:'total_num', title: '货品总数'},
                {field:'export_num', title: '待导单'},
                {field:'exception_num', title: '出库异常'},
                {field:'created_at', title: '创建时间'},
                {field:'right', title: '操作', templet:"#detail_panel"}
            ]]
            ,defaultToolbar: [{
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
        });
        form.on('select(search_type)', function(data){
            $('#search_value').focus();
            $('#search_value').val('');
        });
        //头工具栏事件
        table.on('toolbar(list)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            var data = checkStatus.data;
            var ids = [];
            if(data.length == 0){
                layer.msg('请选择要导出的任务单');
                return false;
            }
            var useType = '';
            for(i in data){
                console.log(useType)
                if(useType != '' && useType != data[i].type){
                    layer.msg('单据类型不支持导单或分拣操作');
                    return false;
                }
                useType = data[i].type;
                ids.push(data[i].id);
            }
            select_ids = ids;
            switch(obj.event){
                case 'exportTask':
                    console.log(data)
                    if(ids.length > 0){
                        $.post('/outStock/blockInfo', {out_store_ids:ids}, function (r) {
                            var loading = $(this).find('.layui-icon');
                            if (loading.is(":visible")) return;
                            loading.show();
                            if (r.code === 200) {
                                $('#task_count').text(r.data.task_count);
                                $('#total_num').text(r.data.total_num);
                                $('#export_num').text(r.data.export_num);
                                $('#block_export_num').text(r.data.export_num);
                                block_list = r.data.block_info;
                                if(r.data.block_info){
                                    $('#block-data').empty();
                                    //$('#block-data').append(new Option('请选择货品区域', 0));
                                    $.each(r.data.block_info,function(index,item){
                                        if(item.id ){
                                            $('#block-data').append(new Option(item.name+'/'+item.export_num, item.id));
                                        }
                                    });
                                    formSelects.render('block');
                                }
                                if(useType == 23){
                                    $(".show_auto_pick_people").css("display", "block");
                                    $("#auto_pick_people").val(r.data.auto_pick_people);
                                    $("#auto_pick_people").html(r.data.auto_pick_people);
                                }else{
                                    $(".show_auto_pick_people").css("display", "none");
                                    $("#auto_pick_people").val();
                                }
                                //调拨任务单展示
                                layer.open({
                                    type: 1,
                                    area: ['40%', '65%'],
                                    content: $('#export_panel') //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
                                });
                            } else {
                                layer.alert(r.msg);
                            }
                            loading.hide();
                        });
                    }

                    // layer.alert(JSON.stringify(data));
                    break;
                case 'createSorting':

                    layer.open({
                        type: 1,
                        content: $('#sorting_panel') //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
                    });
                    // var data = checkStatus.data;
                    // layer.msg('选中了：'+ data.length + ' 个');
                    break;
                case 'isAll':
                    layer.msg(checkStatus.isAll ? '全选': '未全选');
                    break;
                //自定义头工具栏右侧图标 - 提示
                case 'LAYTABLE_TIPS':
                    layer.alert('这是工具栏右侧自定义的一个图标按钮');
                    break;
            };
        });

        //监听行工具事件
        table.on('tool(list)', function(obj){
            var data = obj.data;
            //console.log(obj)
            if(obj.event === 'del'){
                layer.confirm('真的删除行么', function(index){
                    obj.del();
                    layer.close(index);
                });
            } else if(obj.event === 'edit'){
                layer.prompt({
                    formType: 2
                    ,value: data.email
                }, function(value, index){
                    obj.update({
                        email: value
                    });
                    layer.close(index);
                });
            }
        });

        formSelects.on('block',function(id, vals, val, isAdd, isDisabled){
            $("#block_export_num").text(0);
            //id:           点击select的id
            //vals:         当前select已选中的值
            //val:          当前select点击的值
            //isAdd:        当前操作选中or取消
            //isDisabled:   当前选项是否是disabled
            //如果return false, 那么将取消本次操作
            var export_num = 0;
            if(vals.length > 0){
                $.each(vals,function(index,item){
                    export_num +=  parseInt(block_list[item.value]["export_num"])
                });
            }

            if(val && isAdd){
                export_num += parseInt(block_list[val.value]["export_num"])
            }else{
                export_num -= parseInt(block_list[val.value]["export_num"])
            }

            if(export_num == 0){
                export_num = $('#export_num').text();
            }
            // console.log(id);
            // console.log(vals);
            // console.log(val);
            // console.log(isAdd);
            // console.log(isDisabled);
            $("#block_export_num").text(export_num);
            //return false;
        });

        form.on('select(block)', function(data){
            if(data.value == 0){
                $("#block_export_num").text($("#export_num").text());
            }else{
                $("#block_export_num").text(block_list[data.value]["export_num"]);
            }
        });
        //查询
        form.on('submit(query)', function (data) {
            if(!data.field.export_num){
                data.field.export_num = 0;
            }

            if(!data.field.exception_num){
                data.field.exception_num = 0;
            }
            table.reload('list', {
                where: data.field,
                page: {
                    curr: 1
                }
            })
            return false;
        });

        //导出
        form.on('submit(export)', function (data) {

            $.post('/outStock/export', data.field, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    layer.alert(r.msg, function(index){
                        layer.close(index);
                        window.open(r.data.url);
                    });
                } else {
                    layer.alert(r.msg);
                }
                loading.hide();
            });
            return false;
        });

        //导出
        form.on('submit(createRule)', function (data) {
            createRule(data);
            return false;
        });

        /**
         * 导出拣货单 强制替码操作
         * @param data
         * @param pick_replace
         */
        function createRule(data, pick_replace = 0) {
            data.field['pick_replace'] = pick_replace;
            data.field['out_ids'] = select_ids;
            data.field['stock_area'] = formSelects.value('block', 'val');
            data.field['auto_pick_people'] = $.trim(data.field.auto_pick_people).split(/[(\r\n)\r\n]+/);
            console.info(data.field)
            $('#create_rule_btn').addClass("layui-btn-disabled").attr("disabled", true);
            var timeOutId = setTimeout(function () {
                $('#create_rule_btn').removeClass("layui-btn-disabled").attr("disabled", false);
            }, 3000)
            $.post('/pickOrder/createPickOrder', data.field, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    var export_num = $('#export_num').text();
                    var block_export_num = $('#block_export_num').text();
                    $('#export_num').text(parseInt(export_num) - parseInt(data.field['out_number']));
                    $('#block_export_num').text(parseInt(block_export_num) - parseInt(data.field['out_number']));
                    layer.alert(r.msg, function (index) {
                        layer.close(index);
                        layer.closeAll();
                    });
                    console.log(r);
                } else if (r.code === 20001) {
                    //弹出询问框
                    layer.confirm(r.msg, {
                        btn: ['确定']
                        , btn1: function (index, layero) {
                            console.log(index, layero);
                            createRule(data, 1);
                        }
                    });
                } else {
                    layer.alert(r.msg, function (index) {
                        layer.close(index);
                    });
                }
                clearTimeout(timeOutId);
                $('#create_rule_btn').removeClass("layui-btn-disabled").attr("disabled", false);
                loading.hide();
            });
        }

        //导出
        form.on('submit(createSorting)', function (data) {
            data.field['out_ids'] = select_ids;
            $.post('/pickTask/createPickTask', data.field, function (r) {
                var loading = $(this).find('.layui-icon');

                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    layer.msg(r.msg, {icon: 1}, function (index) {
                        layer.close(index);
                        iframeTools.addTab('分拣任务', "/pickTask/lists");
                    });
                    console.log(r);
                } else {
                    layer.alert(r.msg);
                }
                loading.hide();

            });
            return false;
        });
        //监听工具条
        table.on('tool(list)', function (obj) { //注：tool是工具条事件名，dataTable是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            if (layEvent === 'checkDetail') {
                iframeTools.addTab('出库单明细', '/outStock/detail?id=' + data.id);
            }else if (layEvent === 'refresh') {
                refresh();
            }
        });

        //刷新
        refresh = function (param) {
            table.reload('list');
        }

        //时间插件
        laydate.render({
            elem: '#pick-order-time'
            , type: 'datetime'
            , range: true,
            trigger: 'click'
        });
    })
</script>

</body>
    <div class="layui-card" style="display: none" id="export_panel">
        <div class="layui-card-body">
            <form class="layui-form" action="">
                <div class="layui-form-item">
                    <div class="layui-row">
                        <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                            <label class="layui-form-label" >任务数:</label>
                            <div  style="line-height: 36px;">
                                <span id="task_count">0</span>
                            </div>
                        </div>
                        <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                            <label class="layui-form-label" >货品总数:</label>
                            <div style="line-height: 36px;">
                                <span id="total_num">0</span>
                            </div>
                        </div>
                        <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                            <label class="layui-form-label" >待导单数:</label>
                            <div style="line-height: 36px;">
                                <span id="export_num">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 100px !important;">货品区域：</label>
                        <div class="layui-input-inline" style="width: 300px;">
                            <select id="block-data" class="layui-select" xm-select="block" xm-select-search xm-select-skin="default" lay-filter="block">

                            </select>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 100px !important;">可导出数量：</label>
                        <div style="line-height: 36px;" class="layui-input-inline">
                            <span id="block_export_num">0</span>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 100px !important;">导出数量：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="out_number" lay-verify="title" autocomplete="off" class="layui-input">
                    </div>

                    <div class="layui-input-inline" style="width: 100px;">
                        ×
                        <input type="text" style="width: 80px;float: right;" name="out_frequency" value="1" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 100px !important;"></label>
                    <div class="layui-input-inline">
                        <input type="checkbox" name="out_merge" value="1" lay-skin="primary" title="合并拣货">
                    </div>
                </div>
                <div class="layui-form-item show_auto_pick_people">
                    <label class="layui-form-label" style="width: 100px !important;">自动分配拣货人：</label>
                    <div class="layui-input-inline">
                        <textarea name="auto_pick_people" id="auto_pick_people" class="layui-input"
                                  style="height: 90px;width: 300px;"></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn"  lay-submit lay-filter="createRule" id="create_rule_btn">确定</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="layui-card" style="display: none" id="sorting_panel">
        <div class="layui-card-body">
            <form class="layui-form" action="">
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 100px !important;">选择分拣规则:</label>
                    <div class="layui-input-block">
                        @foreach($pick_group as $key=>$value)
                            <input type="checkbox" name="group_type[]" lay-skin="primary" value="{{$key}}" title="{{$value}}" >
                        @endforeach
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 100px !important;">选择分拣方式:</label>
                    <div class="layui-input-block">
                        @foreach($pick_type as $key=>$value)
                            <input type="radio" name="pick_type" value="{{$key}}" title="{{$value}}" >
                        @endforeach
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="createSorting">确定</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

</div>


</html>