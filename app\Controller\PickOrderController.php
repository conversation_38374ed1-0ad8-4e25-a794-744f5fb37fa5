<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Exception\ValidateException;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\AttributeServiceInterface;
use App\JsonRpc\BlockServiceInterface;
use App\JsonRpc\BrandServiceInterface;
use App\JsonRpc\CategoryServiceInterface;
use App\JsonRpc\OrderServiceInterface;
use App\JsonRpc\OutStoreService;
use App\JsonRpc\PickOrderServiceInterface;
use App\JsonRpc\SerialNoServiceInterface;
use App\JsonRpc\SkuServiceInterface;
use App\JsonRpc\SpuServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Hyperf\Contract\SessionInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Picqer\Barcode\BarcodeGeneratorPNG;
use App\Library\Facades\AdminService;

/**
 * Class PickOrderController
 * @Controller
 * @package App\Controller
 */
class PickOrderController extends AbstractController
{

    /**
     * @Inject()
     * @var SerialNoServiceInterface
     */
    private $SerialNoService;

    /**
     * @Inject()
     * @var BrandServiceInterface
     */
    private $BrandService;

    /**
     * @Inject
     * @var PickOrderServiceInterface
     */
    private $PickOrderService;

    /**
     * @Inject
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject
     * @var CategoryServiceInterface
     */
    private $CategoryService;

    /**
     * @Inject
     * @var AttributeServiceInterface
     */
    private $AttributeService;

    /**
     * @Inject
     * @var BlockServiceInterface
     */
    private $BlockService;

    /**
     * @Inject()
     * @var SessionInterface
     */
    protected $session;

    /**
     * @Inject()
     * @var OrderServiceInterface
     */
    private $OrderService;

    /**
     * @Inject
     * @var SkuServiceInterface
     */
    private $SkuService;


    /**
     * 导出明细接口
     * @RequestMapping(path="/pickOrder/exportListDetails",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface|void
     */
    public function exportListDetails ()
    {
        $warehouse = [];
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        if (!empty( $wIds )) {
            $warehouse['ids'] = $wIds;
        }
        $where = $this->request->all();
        if (!empty( $where['date'] )) {
            $date = explode( " - ", $where['date'] );
            $where['start_date'] = $date[0];
            $where['end_date'] = $date[1];
        }

        if (!empty( $where['pick_date'] )) {
            $date = explode( " - ", $where['pick_date'] );
            $where['pick_start_date'] = $date[0];
            $where['pick_end_date'] = $date[1];
        }

        $where['w_ids'] = $wIds;
        $url = $this->PickOrderService->exportListDetails( $where );
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }

    /**
     * @RequestMapping(path="/pickOrder/lists",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface|void
     */
    public function lists ()
    {
        $warehouse = [];
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        if (!empty( $wIds )) {
            $warehouse['ids'] = $wIds;
        }
        $warehouse_info = collect( $this->WarehouseService->getWarehouses( $warehouse ) )->pluck( 'name', 'id' )->all();
        $pick_order_status = PublicCode::pick_order_status;
        $pick_order_type = PublicCode::PICK_ORDER_TYPE;
        if ($this->isAjax()) {
            $where = $this->request->all();
            $where['export'] = $where['export'] ?? 0;
            if (!empty( $where['date'] )) {
                $date = explode( " - ", $where['date'] );
                $where['start_date'] = $date[0];
                $where['end_date'] = $date[1];
            }

            if (!empty( $where['pick_date'] )) {
                $date = explode( " - ", $where['pick_date'] );
                $where['pick_start_date'] = $date[0];
                $where['pick_end_date'] = $date[1];
            }

            $where['w_ids'] = $wIds;
            $where['page_size'] = $where['limit'] ?? $this->pageLimit();
            if(!empty($where['export'])) unset($where['page_size'],$where['page']);
            $info = $this->PickOrderService->getPickOrderLists( $where );
            $result = [];
            if (!empty( $info['data'] )) {
                foreach ($info['data'] as $key => $item) {
                    if (empty($item['out_time'])) {
                        $item['out_time'] = '-';
                    }
                    if (empty($item['out_admin_name'])){
                        $item['out_admin_name'] = '-';
                    }
                    $result['data'][$key] = $item;
                    $result['data'][$key]['status_name'] = $pick_order_status[$item['status']] ?? '-';
                    $result['data'][$key]['type_name'] = $pick_order_type[$item['type']] ?? '-';
                    $result['data'][$key]['w_name'] = $warehouse_info[$item['w_id']] ?? '';
                }
            } else {
                $result['data'] = [];
            }
            $result['total'] = $info['total'] ?? count( $result['data'] );
            return $this->returnApi( ResponseCode::SUCCESS, '操作成功', $result['data'], ['count' => $result['total'], 'limit' => $where['page_size'], 'export' => $where['export']] );
        }

        return $this->show( 'pickOrder/index', [
            'warehouse_info' => $warehouse_info,
            'pick_order_status' => $pick_order_status,
            'pick_order_type' => $pick_order_type,
        ] );
    }

    /**
     * @RequestMapping(path="/pickOrder/detail/{id}",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function detail ()
    {
        $warehouse = [];
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        if (!empty( $wIds )) {
            $warehouse['ids'] = $wIds;
        }
        $warehouse_info = collect( $this->WarehouseService->getWarehouses( $warehouse ) )->pluck( 'name', 'id' )->all();
        $pick_order_status = PublicCode::pick_order_status;
        $pick_order_type = PublicCode::PICK_ORDER_TYPE;
        $pickId = (int)$this->request->route( 'id' );
        $detail = $this->PickOrderService->getPickOrderDetail( $pickId );
        if (empty($detail['pick_time'])) {
            $detail['pick_time'] = '-';
        }

        if (empty($detail['out_time'])) {
            $detail['out_time'] = '-';
        }
        $detail['type_name'] = $pick_order_type[$detail['type']] ?? '';
        $detail['status_name'] = $pick_order_status[$detail['status']] ?? '';
        $detail['w_name'] = $warehouse_info[$detail['w_id']] ?? '';
        return $this->show( 'pickOrder/detail', [
            'detail' => $detail,
        ] );
    }

    /**
     * @RequestMapping(path="/pickOrder/detailLists/{id}",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function detailLists ()
    {
        if ($this->isAjax()) {
            $pick_type = PublicCode::pick_type;
            $params = $this->request->all();
            $where['pick_order_id'] = (int)$this->request->route( 'id' );
            $where['page'] = $params['page'];
            $where['code'] = $params['code'] ?? '';
            $where['export'] = $params['export'] ?? 0;
            $where['page_size'] = $params['limit'] ?? $this->pageLimit();
            $info = $this->PickOrderService->getPickOrderDetailLists( $where );
            $result = [];
            if (!empty( $info['data'] )) {
                //获取品牌数据
                $brand_info = collect( $this->BrandService->getBrands( ['ids' => collect( $info['data'] )->pluck( 'brand_id' )->toArray()] ) )->pluck( 'name', 'id' )->all();
                $original_codes = collect($info['data'])->pluck('original_code')->unique()->toArray();
                $outStores = \App\Library\Facades\OutStoreService::list(1, count($original_codes), ['serial_no' => $original_codes]);
                $outStoreMaps = [];
                if(!empty($outStores['data'])){
                    $outStoreMaps = collect($outStores['data'])->pluck('order_no', 'serial_no');
                }

                foreach ($info['data'] as $key => $item) {
                    $result['data'][$key] = $item;
                    $result['data'][$key]['code'] = '';
                    if ($item['pick_type'] == PublicCode::pick_type_barcode) {
                        $result['data'][$key]['code'] = $item['barcode'];
                    } else {
                        $result['data'][$key]['code'] = $item['unique_code'];
                    }
                    $result['data'][$key]['pick_name'] = $pick_type[$item['pick_type']] ?? '';
                    $result['data'][$key]['brand_name'] = $brand_info[$item['brand_id']] ?? '';
                    $result['data'][$key]['category_name'] = $this->CategoryService->categoryList( $item['category_id'] ?? 0 );
                    $result['data'][$key]['order_no'] = $outStoreMaps[$item['original_code']]??'';
                }
            } else {
                $result['data'] = [];
            }
            $result['total'] = $info['total'] ?? count( $result['data'] );
            return $this->returnApi( ResponseCode::SUCCESS, '操作成功', $result['data'], ['count' => $result['total'], 'limit' => $where['page_size'], 'export' => $where['export']] );
        }
    }

    /**
     * @RequestMapping(path="/pickOrder/print/{id}",methods="get,post")
     */
    public function print ()
    {
        $pick_type = PublicCode::PICK_ORDER_TYPE;
        $pick_order_id = (int)$this->request->route( 'id' );
        $info = $this->PickOrderService->getPickOrderDetail( $pick_order_id );
        $info_list = $this->PickOrderService->getPickOrderDetailLists( ['pick_order_id' => $pick_order_id,'order_by_field'=>'shelf_code','order_by_type'=>'asc','sort'=>true] );
        $list = [];
        //处理生成条码数据
        $generator = new BarcodeGeneratorPNG();
        $barcode = $generator->getBarcode( $info['serial_no'], $generator::TYPE_CODE_128 );
        $print_item_page = [];
        if (!empty( $info_list['data'] )) {
            //查询品牌
            //$brand_info = collect( $this->BrandService->getBrands( ['ids' => collect( $info_list['data'] )->pluck( 'brand_id' )->toArray()] ) )->pluck( 'name', 'id' )->all();
            //查询品类
            //$category_info = $this->CategoryService->idNameMap( array_unique( array_column( $info_list['data'], 'category_id' ) ) );
            //查询规格
            $attr_list = $this->AttributeService->getSpecBySku( array_unique( array_column( $info_list['data'], 'sku_id' ) ) );
            $attr_k_v = [];
            foreach ($attr_list as $item) {
                if (!empty( $item['com'] )) {
                    $attrs = [];
                    foreach ($item['com'] as $attr){
                        $attrs[] = explode("：",$attr)[1]??'';
                    }
                    $attr_k_v[$item['sku']] = implode(",",$attrs);
                }
            }

            //排序
            $list = $info_list['data'];//collect($info_list['data'])->sortBy('shelf_code')->sortBy('brand_id')->sortBy('sku_id')->toArray();

            //查询spu货号
            $datas = array_chunk($list, 1024);
            $skuInfos = [];
            foreach ($datas as $data) {
                $skuIds = array_column($data, 'sku_id');
                $skuInfo = $this->SkuService->getSkuInfoById($skuIds, 1);
                foreach ($skuInfo as $item){
                    $skuInfos[$item['sku_id']] = $item;
                    unset($item);
                }
                unset($skuInfo);
            }

            foreach ($list as $key => $item) {
                $print_item_page[$key] = $item;
                $print_item_page[$key]['brand_name'] = $skuInfos[$item['sku_id']]['brand_name'] ?? '';
                $print_item_page[$key]['category_name'] = $skuInfos[$item['sku_id']]['category_name'] ?? '';
                $print_item_page[$key]['attr'] = $attr_k_v[$item['sku_id']] ?? '';
                $print_item_page[$key]['spu_no'] = $skuInfos[$item['sku_id']]['spu_no']??'';
            }
        }
        //查询仓库
        $warehouse_info = collect( $this->WarehouseService->getWarehouses( ['ids' => [$info['w_id']]] ) )->pluck( 'name', 'id' )->all();
        $detail = [
            'serial_no_barcode' => base64_encode( $barcode ),
            'serial_no' => $info['serial_no'],
            'pick_order_id' => $info['id'],
            'print_time' => date( "Y-m-d H:i:s" ),
            'w_name' => $warehouse_info[$info['w_id']] ?? '',
            'type_name' => $pick_type[$info['type']] ?? '',
            'company_name' => '好超值信息技术有限公司-拣货单',
        ];

        return $this->show( 'pickOrder/print', [
            'print_info' => $detail,
            'print_item_page' => array_chunk( $print_item_page, 50 ),
        ] );
    }

    /**
     * 打印成功需改拣货单状态为拣货中
     * 修改拣货单为拣货中
     * @RequestMapping(path="/pickOrder/outPick",methods="get,post")
     */
    public function outPick ()
    {
        $pick_order_id = $this->request->post( 'pick_order_id', 0 );
        if (empty( $pick_order_id )) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, '缺少拣货单ID' );
        }
        $userInfo = $this->getUserInfo();
        try {
            $result = $this->PickOrderService->getPickOrderDetail((int)$pick_order_id);
            if (in_array($result['status'], [1])) {//只允许待拣货打印 修改
                $data = [
                    'status' => 2,
                    'pick_admin_id' => $userInfo['uid'] ?? 0,
                    'pick_admin_name' => $userInfo['nickname'] ?? '',
                    'pick_time' => date("Y-m-d H:i:s"),
                ];
                $result = $this->PickOrderService->updatePickOrder((int)$pick_order_id, $data);
            }
        } catch (\Exception $exception) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage() );
        }
        return $this->returnApi( ResponseCode::SUCCESS, '操作成功', $result );
    }

    /**
     * @RequestMapping(path="/pickOrder/createPickOrder",methods="post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function createPickOrder ()
    {
        $out_ids = $this->request->post( 'out_ids', [] );//出库任务ids
        $stock_area = $this->request->post( 'stock_area', [] );//货品区域
        $out_number = $this->request->post( 'out_number', 0 );//导出数量
        $pick_replace = $this->request->post( 'pick_replace', 0 );//导出数量
        $out_frequency = $this->request->post( 'out_frequency', 1 );//拣货单个数
        $out_merge = $this->request->post( 'out_merge', 0 );//是否合并拣货
        $auto_pick_people = array_filter($this->request->post( 'auto_pick_people', [] ));//是否合并拣货
        if (empty( $out_ids )) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, '缺少出库任务单ID' );
        }

        if (empty( $out_number )) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, '导出拣货单数量不能0' );
        }

        if($out_number < 0){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '导出拣货单数量不能小于0' );
        }

        $userInfos = $this->getUserInfo();
        try {
            $extras = [
                'admin_id' => $userInfos['uid'] ?? 0,
                'admin_name' => $userInfos['nickname'] ?? '',
                'pick_replace' =>$pick_replace,
                'out_frequency' => $out_frequency,
                'out_merge' => $out_merge,
                'auto_pick_people' => $auto_pick_people ?? [],
            ];
            $pick_order = $this->PickOrderService->createPickOrder( (array)$out_ids, (array)$stock_area, (int)$out_number, $extras);
        } catch (\Exception $exception) {
            return $this->returnApi($exception->getCode(), $exception->getMessage());
        }
        return $this->returnApi( ResponseCode::SUCCESS, '创建成功', ['pick_order' => $pick_order] );
    }

    /**
     * @RequestMapping(path="/pickOrder/salePick",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function salePick ()
    {
        $data['title'] = '销售拣货';
        $where = [];
        $blockWhere = [];
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        if (!empty( $wIds )) {
            $where = ['ids' => $wIds];
            $blockWhere = ['w_ids' => $wIds];
        }
        $data['warehouse_info'] = collect( $this->WarehouseService->getWarehouses( $where ) )->pluck( 'name', 'id' )->toArray();
        $data['pick_type'] = PublicCode::pick_type;
        $data['pick_mode'] = PublicCode::pick_mode;
        $data['block_list'] = json_encode( collect( $this->BlockService->getBlockLists( array_merge( ['status' => 1], $blockWhere ), ['name', 'id', 'w_id'] ) )->toArray() );
        $data['order_source'] = PublicCode::order_source;
        $data['order_pick_type'] = PublicCode::order_pick_order_type;
        $data['pick_sort'] = PublicCode::pick_sort;
        return $this->show( 'pickOrder/salePick', $data );
    }

    /**
     * 查询可拣货条数以及数量
     * @RequestMapping(path="/pickOrder/checkSalePick",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function checkSalePick ()
    {
        $params = $this->request->post();

        //处理自订单号数据
        if (!empty( $params['order_branch_codes'] )) {
            $params['order_branch_codes'] = explode( "\n", $params['order_branch_codes'] );
        }

        //处理日期数据
        if (!empty( $params['date'] )) {
            $date = explode( " - ", $params['date'] );

            $params['start_date'] = $date[0];
            $params['end_date'] = $date[1];
        }

        //处理拣货方式
        if (!empty( $params['pick_type'] )) {
            if (count( $params['pick_type'] ) == count( PublicCode::pick_type )) {
                unset( $params['pick_type'] );
            } else {
                $params['pick_type'] = current( $params['pick_type'] );
            }
        }

        //默认值
        $params['number'] = $params['number'] ?? 0;//默认导出订单数量

        try {
            //查询所有需要拣货的数据
            $order_info = $this->OrderService->getOrderSalePickInfo( $params );
            if (empty( $order_info )) {
                throw new ValidateException( '暂无待拣货订单数据' );
            }

            //根据排序规则排序
            $order_branch_codes = [];
            if (!empty( $params['pick_sort'] ) && $params['pick_sort'] == 1) {
                //根据货架号过滤掉不符合的订单
                $sale_barcode_info = [];
                $shelf_code = array_filter(array_column($order_info, 'shelf_code'));
                if (!empty($shelf_code)) {
                    $sale_shelf_code_where = [
                        'w_id' => $params['w_id'] ?? 0,
                        'block_list' => $params['block_list'] ?? [],
                        'shelf_codes' => $shelf_code,
                    ];

                    $sale_barcode_info = $this->PickOrderService->getOrderSaleShelfCodeInfo($sale_shelf_code_where);
                }
                if (empty($sale_barcode_info)) {
                    throw new ValidateException('暂无符合拣货订单数据');
                }

                //获取所有符合拣货的条码信息
                $shelf_codes = collect( $sale_barcode_info )->pluck( 'shelf_code' )->toArray();

                //过滤掉不符合的条码信息
                foreach ($order_info as $k => $oi) {
                    if (!in_array( $oi['shelf_code'], $shelf_codes )) {
                        unset( $order_info[$k] );
                    }
                }

                //检测是否存在
                if (!empty( $order_info )) {
                    //筛选要处理的订单信息
                    $sort_sale_info = collect( $order_info )->sortByDesc( 'add_time' )->unique( 'branch_serial_no' )->chunk( (int)$params['number'] )->toArray();

                    //获取需要拣货的订单号信息
                    $order_branch_codes = array_column( $sort_sale_info[0], 'branch_serial_no' );
                }
            } elseif (!empty( $params['pick_sort'] ) && $params['pick_sort'] == 2) {
                //组织条件
                $sale_where = [
                    'w_id' => $params['w_id'] ?? 0,
                    'block_list' => $params['block_list'] ?? [],
                    'unique_codes' => array_filter( collect( $order_info )->pluck( 'unique_code' )->all() ),
                ];
                $sale_info = $this->PickOrderService->getOrderSaleUniqueInfo( $sale_where );
                $sort_sale_info = collect( $sale_info )->sortByDesc( ['area', 'line', 'seat'] )->all();
                $order_info_list = collect( $order_info )->pluck( 'branch_serial_no', 'unique_code' )->all();
                foreach ($sort_sale_info as $item) {
                    if (count( $order_branch_codes ) >= $params['number']) {
                        continue;
                    }

                    if (!empty( $item['unique_code'] ) && isset( $order_info_list[$item['unique_code']] )) {
                        $order_branch_codes[$order_info_list[$item['unique_code']]] = $order_info_list[$item['unique_code']];
                    }
                    unset( $item );
                }
                unset( $item, $sort_sale_info );
            }

            //组织所有数据
            $result = [
                'branch_serial_no' => array_values( $order_branch_codes ),
                'order_number' => count( $order_branch_codes ),
                'order_branch_number' => 0,
            ];
            foreach ($order_info as $item) {
                if (in_array( $item['branch_serial_no'], $order_branch_codes )) {
                    $result['order_branch_number']++;
                }
                unset( $item );
            }
            unset( $order_info );
        } catch (\Exception $exception) {
            return $this->returnApi( ErrorCode::DB_ERROR, $exception->getMessage() );
        }
        return $this->returnApi( ResponseCode::SUCCESS, '请求成功', $result );
    }

    /**
     * 生成销售拣货单
     * @RequestMapping(path="/pickOrder/makeSalePick",methods="get,post")
     */
    public function makeSalePick ()
    {
        $params = $this->request->post();
        try {
            if (empty( $params['w_id'] )) {
                throw new ValidateException( '仓库ID不存在！' );
            }

            if (empty( $params['branch_serial_no'] )) {
                throw new ValidateException( '无待导出订单数据' );
            }

            //处理拣货方式
            $where = [];
            if (!empty( $params['pick_type'] )) {
                if (count( $params['pick_type'] ) == count( PublicCode::pick_type )) {
                    unset( $params['pick_type'] );
                } else {
                    $where['pick_type'] = current( $params['pick_type'] );
                }
            }
            $where['order_branch_codes'] = $params['branch_serial_no'];
            $order_info = $this->OrderService->getOrderSalePickInfo( $where );
            if (!empty( $order_info )) {
                $unique_codes = collect( $order_info )->where( 'pick_type', PublicCode::pick_type_unique_code )->pluck( 'unique_code' )->toArray();
                //组织条件
                $sale_unique_where = [
                    'w_id' => $params['w_id'] ?? 0,
                    'block_list' => $params['block_list'] ?? [],
                    'unique_codes' => array_unique( array_filter( $unique_codes ) ),
                ];
                $sale_unique_info = $this->PickOrderService->getOrderSaleUniqueInfo( $sale_unique_where );
                $sale_unique_list = collect( $sale_unique_info )->pluck( 'shelf_code', 'unique_code' )->all();

                $userInfo = $this->getUserInfo();
                $data['pick_order'] = [
                    'serial_no' => $this->SerialNoService->generate( 30 ),
                    'w_id' => $params['w_id'],
                    'type' => 25,
                    'status' => 1,
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'remark' => '',
                ];

                $order_list = [];
                foreach ($order_info as $oi) {
                    if ($oi['pick_type'] == PublicCode::pick_type_unique_code) {
                        $order_list[$oi['branch_serial_no']][$oi['pick_type']][$oi['unique_code']] = $oi;
                    } else {
                        $order_list[$oi['branch_serial_no']][$oi['pick_type']][$oi['barcode']][$oi['sku_id']][$oi['shelf_code']][] = $oi;
                    }
                }

                $data['pick_order_detail'] = [];
                foreach ($order_list as $branch_serial_no => $pick) {
                    foreach ($pick as $pick_type => $infos) {
                        foreach ($infos as $code => $item) {
                            if ($pick_type == PublicCode::pick_type_unique_code) {
                                $it['pick_type'] = $pick_type;
                                $it['unique_code'] = $code;
                                $it['barcode'] = $item['barcode'];
                                $it['shelf_code'] = $sale_unique_list[$item['unique_code']] ?? '';
                                $it['pick_num'] = 1;
                                $it['original_code'] = $branch_serial_no;
                                $it['sku_id'] = $item['sku_id'];
                                $it['brand_id'] = $item['brand_id'] ?? 0;
                                $it['category_id'] = $item['category_id'] ?? 0;
                                $it['status'] = 1;
                                $data['pick_order_detail'][] = $it;
                                unset( $it );
                            } else if ($pick_type == PublicCode::pick_type_barcode) {
                                foreach ($item as $sku_id => $sku_info) {
                                    foreach ($sku_info as $shelf_code => $info) {
                                        $it['unique_code'] = '';
                                        $it['barcode'] = $code;
                                        $it['shelf_code'] = $shelf_code;
                                        $it['pick_num'] = count( $info );
                                        $it['original_code'] = $branch_serial_no;
                                        $it['sku_id'] = $sku_id;
                                        $it['brand_id'] = $info[0]['brand_id'] ?? 0;
                                        $it['category_id'] = $info[0]['category_id'] ?? 0;
                                        $it['status'] = 1;
                                        $data['pick_order_detail'][] = $it;
                                        unset( $it, $info, $shelf_code );
                                    }
                                    unset( $sku_info, $sku_id );
                                }
                            }
                            unset( $code, $item );
                        }
                        unset( $infos );
                    }
                }

                $serial_no = $this->PickOrderService->create( $data );

                //创建成功回写订单状态
                if (!empty( $serial_no )) {
                    $ids = array_column( $order_info, 'id' );
                    $res = $this->OrderService->updateOrderCurrentGeneration( ['ids' => $ids], ['current_generation' => 4] );
                    if (empty( $res )) {
                        throw new ValidateException( '订单状态回写失败！！' );
                    }
                }
            } else {
                throw new ValidateException( '暂无待导出订单数据' );
            }
            //旧逻辑 暂时不考虑
            /*if (!empty( $order_info )) {
                $barcodes = collect( $order_info )->where( 'pick_type', PublicCode::pick_type_barcode )->pluck( 'barcode' )->toArray();
                $unique_codes = collect( $order_info )->where( 'pick_type', PublicCode::pick_type_unique_code )->pluck( 'unique_code' )->toArray();
                if ((!empty( $barcodes ) && isset( $params['pick_type'] ) && $where['pick_type'] == PublicCode::pick_type_barcode) || !isset( $where['pick_type'] )) {
                    //组织条件
                    $sale_barcode_where = [
                        'w_id' => $params['w_id'] ?? 0,
                        'block_list' => $params['block_list'] ?? [],
                        'barcodes' => $barcodes,
                    ];
                    $sale_barcode_info = $this->PickOrderService->getOrderSaleBarcodeInfo( $sale_barcode_where );

                    //匹配订单货架号数据
                    foreach ($order_info as $k => $oi) {
                        foreach ($sale_barcode_info as $sk => $sab) {
                            //对比条码数据
                            if ($oi['barcode'] == $sab['barcode'] && !isset( $order_info[$k]['shelf_code'] )) {
                                if ($sab['num'] >= 1) {
                                    $order_info[$k]['shelf_code'] = $sab['shelf_code'];
                                    $sale_barcode_info[$sk]['num']--;
                                } else {
                                    unset( $sale_barcode_info[$sk] );
                                }
                            }
                            unset( $sab, $sk );
                        }
                        unset( $k, $oi );
                    }
                }

                if ((!empty( $unique_codes ) && isset( $where['pick_type'] ) && $where['pick_type'] == PublicCode::pick_type_unique_code) || !isset( $where['pick_type'] )) {
                    //组织条件
                    $sale_unique_where = [
                        'w_id' => $params['w_id'] ?? 0,
                        'block_list' => $params['block_list'] ?? [],
                        'unique_codes' => $unique_codes,
                    ];
                    $sale_unique_info = $this->PickOrderService->getOrderSaleUniqueInfo( $sale_unique_where );
                    $sale_unique_list = collect( $sale_unique_info )->pluck( 'shelf', 'unique_code' )->all();
                }
            }*/
        } catch
        (\Exception $exception) {
            return $this->returnApi( ErrorCode::DB_ERROR, $exception->getMessage() );
        }
        return $this->returnApi( ResponseCode::SUCCESS, '导出成功', ['serial_no' => $serial_no] );
    }
}