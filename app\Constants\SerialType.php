<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Constants;

use Hyperf\Constants\AbstractConstants;

/**
 * Class SerialType
 * @package App\Constants
 */
class SerialType extends AbstractConstants
{
    const C = 1;  //合同
    const AR = 2; //预约到货
    const SP = 3; //采购单
    const IB = 4; //入库单号/批次号
    const OB = 5; //出库单号/批次号
    const PD = 6; //差异调整单
    const RO = 7; //退返单
    const A = 8;  //调拨
    const I = 9;  //盘点
    const O = 10; //销售(父订单)
    const OBT = 11; //销售(子订单)
    const R = 12;//退款单
    const L = 13; //配送
    const DZ = 14; //对账
    const FK = 15; //财务-付款
    const JS = 16; //财务-结算
    const WL = 17; //往来费用
    const ISC = 18;//唯一码
    const DC = 20;//调拨差异
    const S = 21;//售后单
    const XSTJ = 22;//销售调价单
    const CBTJ = 23;//成本调价单
    const DP = 24; //店铺对账
    const ZF = 25; //支付方式对账
    const WIB = 26; //wms入库批次号
    const GP = 33; //装箱箱号
    const FG = 40; //寻货单号

    const WO_PROMOTION = 44;//批量导出任务单-活动商品

    const WO_UNIQUE = 50;//店内码导出
    const WO_IMPERFECT = 52;//残次导出
    const WO_TALLY = 53;//理货导出
    const THIRD = 56;//三方发货单号
    const WO_LOGISTICS = 57;//到货登记明细导出
    const BB = 58;//EPC
    const WO_ARDETAIL = 62;//到货任务明细
    const WO_PD = 63;//生产区货品导出
    const WO_PD_SM = 64;//生产区货品汇总
}
