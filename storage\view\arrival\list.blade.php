<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/layui/css/layui.css" />
    <link rel="stylesheet" href="/static/css/pearTab.css" />
    <link rel="stylesheet" href="/static/css/pearTheme.css" />
    <link rel="stylesheet" href="/static/css/pearLoad.css" />
    <link rel="stylesheet" href="/static/css/pearFrame.css" />
    <link rel="stylesheet" href="/static/css/pearAdmin.css" />
    <link rel="stylesheet" href="/static/css/pearNotice.css" />
    <link rel="stylesheet" href="/static/css/pearMenu.css" />
    <link rel="stylesheet" href="/static/css/pearCommon.css"/>
    <link rel="stylesheet" href="/static/css/myui.css"/>
    <link rel="stylesheet" href="/static/layui/formSelects-v4.css"/>
{{--    <script src="/static/js/jquery.min.js"></script>--}}
    <style id="pearone-bg-color">
        /*.layui-form-item .layui-input-inline {*/
        /*    width: 40%;*/
        /*}*/

        .layui-layer-content{text-align: center;}
    </style>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form layui-form-pane"  action="" style="width: 100%;margin-top: 20px;">
            <div class="layui-row mt-7">
                <div class="layui-col-xs4">
                    <label class="layui-form-label">仓库</label>
                    <div class=" layui-input-inline" style="width: 60%">
                        <select name="warehouse" class="layui-select"  xm-select="warehouse" xm-select-search xm-select-skin="default" xm-select-show-count="4">
                            <option value=""></option>
                            @foreach($warehouse_list as $warehouse_info)
                                <option value="{{$warehouse_info['id']}}" {{in_array($warehouse_info['id'],$w_ids)?'selected':''}}>{{$warehouse_info['name']}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="layui-col-xs4">
                    <label class="layui-form-label">状态</label>
                    <div class=" layui-input-inline" style="width: 60%">
                        <select name="status" id="status">
                            <option value=""></option>
                            @foreach($status_list as $key => $name)
                                <option value="{{$key}}">{{$name}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="layui-col-xs4">
                    <label class="layui-form-label">任务单号</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <input type="text" name="arrival_code" id="arrival_code" placeholder="任务单号" class="layui-input">
                    </div>
                </div>
            </div>

            <div class="layui-row mt-7">
                <div class="layui-col-xs4">
                    <label class="layui-form-label">采购单号</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <input type="text" name="purchase_code" id="purchase_code" placeholder="采购单号" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-xs4">
                    <label class="layui-form-label">供应商</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <select name="supplier" class="layui-select"  xm-select="supplier" xm-select-search xm-select-skin="default" xm-select-show-count="4">
                            <option value=""></option>
                            @foreach($supplier_list as $supplier_info)
                                <option value="{{$supplier_info['id']}}">{{$supplier_info['name']}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="layui-col-xs4">
                    <label class="layui-form-label">箱号</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <input type="text" id="box_no" name="box_no" lay-verify="box_no" class="layui-input" placeholder="箱号" onkeypress="javascript:if(event.keyCode == 32)event.returnValue = false;">
                    </div>
                </div>
            </div>

            <div class="layui-row mt-7">
                <div class="layui-col-xs4">
                    <label class="layui-form-label">货品</label>
                    <div class="layui-input-inline" style="width: 60%;">
                        <select name="brand_id" class="layui-select"  xm-select="brand_id" xm-select-search xm-select-skin="default" xm-select-show-count="4">
                            <option value="">请选择品牌</option>
                            @foreach($brand_list as $brand_info)
                                <option value="{{$brand_info['id']}}">{{$brand_info['name']}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="layui-col-xs4">
                    <label class="layui-form-label">条形码</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <input type="text" name="barcode" id="barcode" placeholder="条形码" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-xs4">
                    <label class="layui-form-label">三方发货单号</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <input type="text" name="third_party_no" id="third_party_no" placeholder="三方发货单号" class="layui-input">
                    </div>
                </div>
            </div>

            <div class="layui-row mt-7">
                <div class="layui-col-xs4">
                    <label class="layui-form-label" >预约信息</label>
                    <div class="layui-input-inline" style="width: 60%;">
                        <input type="text" name="delivery_sign" id="delivery_sign" placeholder="发货单号/车牌号" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-xs4">
                    <label class="layui-form-label" >联系人</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <input type="text" name="phone" id="phone" placeholder="联系人手机号" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-xs4">
                    <label class="layui-form-label">期望到货时间</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <input type="text" name="arrive_time" autocomplete="off" class="layui-input" id="arrive_time">
                    </div>
                </div>
            </div>

            <div class="layui-row mt-7">
                <div class="layui-col-xs4">
                    <label class="layui-form-label">到店时间</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <input type="text" name="arrival_shop_at" autocomplete="off" class="layui-input" id="arrival_shop_at">
                    </div>
                </div>

                <div class="layui-col-xs8">
                    <label class="layui-form-label" >中转预约单</label>
                    <div class="layui-input-inline" style="width: 30%;">
                        <select name="is_transfer" id="is_transfer" lay-filter="is_transfer" class="input_font" lay-verify="is_transfer">
                            <option value="" selected>请选择</option>
                            <option value="1">是</option>
                            <option value="0">否</option>
                        </select>
                    </div>
                    <div class="layui-input-inline" style="width: 30%; display: none;" id="allot_warehouse_id">
                        <select name="allot_warehouse" class="layui-select" id="allot_warehouse" xm-select="allot_warehouse" xm-select-search xm-select-skin="default" xm-select-show-count="4">
                            <option value="">预约到货门店</option>
                            @foreach($allot_warehouse_list as $awarehouse_info)
                                <option value="{{$awarehouse_info['id']}}">{{$awarehouse_info['name']}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-row mt-7">
                <div class="layui-col-xs4">
                    <div class=" layui-input-inline" style="width: 60%">
                        <input type="checkbox" name="produce_diff" id="produce_diff" value="0" title="生产转架差异" lay-filter="demo-checkbox-filter">
                    </div>
                </div>
                <div class="layui-col-xs4" style="float: right;">
                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="query" style="margin: 5px">
                        <i class="layui-icon layui-icon-search"></i>
                        筛选
                    </button>
                    <button class="pear-btn pear-btn-md pear-btn-primary export" type="button" onclick="export_content()" style="margin: 5px">
                        <i class="layui-icon layui-icon-export"></i>
                        导出
                    </button>
                    <button class="pear-btn pear-btn-md pear-btn-primary export" type="button" onclick="export_detail()" style="margin: 5px">
                        <i class="layui-icon layui-icon-export"></i>
                        导出明细
                    </button>
                    <button type="reset" class="pear-btn pear-btn-md" style="margin: 5px">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <table id="arrival_list" lay-filter="arrival_list"></table>
    </div>
</div>
<form style="display:none" id="down_list" action="/arrivalOrder/down_arrival_list" method="post">
</form>

<form style="display:none" id="down_detail" action="/arrivalOrder/down_arrival_detail" lay-filter='down_detail'>
</form>

<div id="barcode_arrival_code_div">
    <img id="barcode_arrival_code" style="padding:0;width:210px;height:120px;align-self: flex-end; display: none;"/>
</div>

<script type="text/html" id="arrival_id">
    <a class="pear-btn pear-btn-sm" lay-event="checkDetail">查看明细</a>
    <a class="pear-btn pear-btn-sm" lay-event="pintCode">预约条码打印</a>
</script>
<script src="/static/layui/layui.js"></script>
<script src="http://127.0.0.1:8000/CLodopfuncs.js"></script>
<script type="text/javascript" src="/static/js/JsBarcode.all.min.js"></script>
<script type="text/javascript">

    layui.use(['pearAdmin', 'pearSocial', 'element', 'layer', 'form', 'util', 'jquery', 'table', 'upload','formSelects', 'iframeTools','laydate'], function() {
        var formSelects = layui.formSelects;
        var table = layui.table;
        var iframeTools = layui.iframeTools;
        var laydate = layui.laydate;
        var $ = layui.jquery;
        var form = layui.form;
        $('#box_no').focus();
        var arrival_list;
        //时间插件
        laydate.render({
            elem: '#arrive_time'
            , type: 'datetime'
            , range: true,
            trigger: 'click',
            value: '{{$time}}'
        });
        laydate.render({
            elem: '#arrival_shop_at'
            , type: 'month'
            , range: true
            ,isPreview: false,
            trigger: 'click',
            value: ''
        });

        tableRender();

        //  表格换行显示的模板
        function getOrderItemInfo(objArry) {
            var strHtml=`<div>${objArry.arrival_code}<br/>${objArry.purchase_code}</div>`
            return strHtml;
        }

        function tableRender() {
            //表格初始化
            var brand_ids = formSelects.value('brand_id', 'val');
            var warehouse_ids = formSelects.value('warehouse', 'val');
            var supplier_ids = formSelects.value('supplier', 'val');
            var status = $("#status option:selected").val();
            var arrival_code = $("#arrival_code").val();
            var purchase_code = $("#purchase_code").val();
            var barcode = $("#barcode").val();
            var delivery_sign = $("#delivery_sign").val();
            var phone = $("#phone").val();
            var box_no = $("#box_no").val();
            var third_party_no = $('#third_party_no').val();
            var arrive_time = $('#arrive_time').val();
            var arrival_shop_at = $('#arrival_shop_at').val();
            var produce_diff = $('#produce_diff').is(':checked');
            var is_transfer = $('#is_transfer').val();
            var allot_warehouse = formSelects.value('allot_warehouse', 'val');
            layer.load(3);
            arrival_list = table.render({
                elem: '#arrival_list'
                ,url:'/arrivalOrder/ajax_list'
                ,method: 'post'
                ,page: true //开启分页
                ,skin: 'line'
                // ,toolbar: '#lay-toolbar'
                ,totalRow: true
                ,where: {
                    brand_ids:brand_ids,
                    warehouse_ids:warehouse_ids,
                    supplier_ids:supplier_ids,
                    status:status,
                    arrival_code:arrival_code,
                    purchase_code:purchase_code,
                    barcode:barcode,
                    delivery_sign:delivery_sign,
                    phone:phone,
                    box_no:box_no,
                    third_party_no:third_party_no,
                    arrive_time:arrive_time,
                    arrival_shop_at:arrival_shop_at,
                    produce_diff:produce_diff,
                    is_transfer:is_transfer,
                    allot_warehouse:allot_warehouse
                }
                ,cols: [[
                    {field:'arrival_code', title: "任务单号 <br> 采购单号", width: 180,"templet": function (d) { return getOrderItemInfo(d); }, totalRowText:"总计"}
                    ,{field:'supplier_name', title: '供应商'}
                    ,{field:'warehouse_name', title: '仓库'}
                    ,{field:'brand_names', title: '品牌'}
                    ,{field:'status', title: '状态', width: 70}
                    ,{field:'arrive_num', title: '预约数', width: 70, totalRow:true}
                    ,{field:'in_num', title: '入库数', width: 70, totalRow:true}
                    ,{field:'diff_num', title: '差异数', width: 70, totalRow:true}
                    ,{field:'defective_nums', title: '残次数', width: 70, totalRow:true}
                    ,{field:'print_num', title: '生产数', width: 70, totalRow:true}
                    ,{field:'tally_num', title: '转架数', width: 70, totalRow:true}
                    ,{field:'arrive_time', title: '预约时间', width: 120}
                    ,{field:'arrival_shop_at', title: '到店时间', width: 120}
                    ,{field:'arrival_id', title: '操作', templet:"#arrival_id", width: 220}
                ]]
                ,done: function(res, curr, count) {
                    if(res.code != 200){
                        layer.msg(res.msg, {icon:0, time: 2000})
                    }
                    layer.closeAll('loading'); // 隐藏加载层
                }

                // ,defaultToolbar: [{
                //     layEvent: 'refresh',
                //     icon: 'layui-icon-refresh',
                // }, 'filter', 'print']
            });
        }

        //查询按钮
        var median;
        form.on('submit(query)', function (data) {
            tableRender();
            return false;
        });

        //选择中转预约单标志
        form.on('select(is_transfer)', function(data){
            if (data.value == 1){//是
                $('#allot_warehouse_id').show();
            }else{
                $('#allot_warehouse_id').hide();
            }
            formSelects.value('allot_warehouse', []);
            form.render('select');
        });

        //监听工具条
        table.on('tool(arrival_list)', function (obj) { //注：tool是工具条事件名，dataTable是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            if (layEvent === 'checkDetail') {
                iframeTools.editTab('任务单明细', '/arrivalOrder/detail/' + data.arrival_id);
            }else if (layEvent === 'refresh') {
                refresh();
            }else if (layEvent === 'pintCode') {
                JsBarcode("#barcode_arrival_code", data.arrival_code);
                let test = document.getElementById('barcode_arrival_code').getAttribute('src')
                var barcodeImage = $('#barcode_arrival_code').attr('src', test);
                // 弹出层
                layer.open({
                    type: 1,
                    title: '预约条码',
                    content: barcodeImage, // 条形码图片作为内容
                    area: ['300px', '300px'], // 弹框大小
                    shadeClose: true, // 点击遮罩关闭弹窗
                    closeBtn: 0, // 不显示关闭按钮
                    btn: ['打印', '关闭'],
                    btnAlign: 'c',
                    btn1: function(index, layero){
                        print(data.arrival_code);
                    },
                    cancel: function(layero,index){
                        // 关闭弹窗
                        layer.close(index);
                    }
                });
            }
        });

        // //监听回车键
        // $("body").on("keydown", "#box_no", function (e) {
        //     if(e.keyCode == 13) {
        //         tableRender();
        //         return false;
        //     }
        // });

        //刷新
        refresh = function (param) {
            table.reload('list');
        }
    })

    //导出数据
    function export_content() {
        layui.use(['pearAdmin', 'pearSocial', 'element', 'layer', 'form', 'util', 'jquery', 'table', 'upload','formSelects'], function () {
            var $ = layui.jquery;
            var formSelects_s = layui.formSelects;
            var table = layui.table;
            var brand_ids = formSelects_s.value('brand_id', 'val');
            var warehouse_ids = formSelects_s.value('warehouse', 'val');
            var supplier_ids = formSelects_s.value('supplier', 'val');
            var status = $("#status option:selected").val();
            var arrival_code = $("#arrival_code").val();
            var purchase_code = $("#purchase_code").val();
            var barcode = $("#barcode").val();
            var delivery_sign = $("#delivery_sign").val();
            var phone = $("#phone").val();
            var box_no = $("#box_no").val();
            var third_party_no = $('#third_party_no').val();
            var produce_diff = $('#produce_diff').is(':checked');
            var arrive_time = $('#arrive_time').val();
            var is_transfer = $('#is_transfer').val();
            var allot_warehouse = formSelects_s.value('allot_warehouse', 'val');
            var arrival_shop_at = $('#arrival_shop_at').val();

            if(produce_diff == true && brand_ids == '' && warehouse_ids == "" && supplier_ids == "" && status == "" && arrival_code == "" && purchase_code == "" && barcode == "" && delivery_sign == "" && phone == "" && box_no == "" && third_party_no == "" && arrive_time == ""){
                alert("请设置期望到店时间");
            }else{
                var $formArrival = $("#down_list");
                $formArrival.append($('<input name = "brand_ids" value = "' + brand_ids + '" />'));
                $formArrival.append($('<input name = "warehouse_ids" value = "' + warehouse_ids + '" />'));
                $formArrival.append($('<input name = "supplier_ids" value = "' + supplier_ids + '" />'));
                $formArrival.append($('<input name = "status" value = "' + status + '" />'));
                $formArrival.append($('<input name = "arrival_code" value = "' + arrival_code + '" />'));
                $formArrival.append($('<input name = "purchase_code" value = "' + purchase_code + '" />'));
                $formArrival.append($('<input name = "barcode" value = "' + barcode + '" />'));
                $formArrival.append($('<input name = "delivery_sign" value = "' + delivery_sign + '" />'));
                $formArrival.append($('<input name = "phone" value = "' + phone + '" />'));
                $formArrival.append($('<input name = "box_no" value = "' + box_no + '" />'));
                $formArrival.append($('<input name = "third_party_no" value = "' + third_party_no + '" />'));
                $formArrival.append($('<input name = "produce_diff" value = "' + produce_diff + '" />'));
                $formArrival.append($('<input name = "arrive_time" value = "' + arrive_time + '" />'));
                $formArrival.append($('<input name = "is_transfer" value = "' + is_transfer + '" />'));
                $formArrival.append($('<input name = "allot_warehouse" value = "' + allot_warehouse + '" />'));
                $formArrival.append($('<input name = "arrival_shop_at" value = "' + arrival_shop_at + '" />'));
                $formArrival.submit();
                layer.msg('导出已提交，请稍等！', {icon: 1, time: 2000})
            }

        })
    }

    //导出数据
    function export_detail() {
        layui.use(['pearAdmin', 'pearSocial', 'element', 'layer', 'form', 'util', 'jquery', 'table', 'upload','formSelects'], function () {
            var $ = layui.jquery;
            var formSelects_s = layui.formSelects;
            var table = layui.table;
            var brand_ids = formSelects_s.value('brand_id', 'val');
            var warehouse_ids = formSelects_s.value('warehouse', 'val');
            var supplier_ids = formSelects_s.value('supplier', 'val');
            var status = $("#status option:selected").val();
            var arrival_code = $("#arrival_code").val();
            var purchase_code = $("#purchase_code").val();
            var barcode = $("#barcode").val();
            var delivery_sign = $("#delivery_sign").val();
            var phone = $("#phone").val();
            var box_no = $("#box_no").val();
            var third_party_no = $('#third_party_no').val();
            var produce_diff = $('#produce_diff').is(':checked');
            var arrive_time = $('#arrive_time').val();
            var is_transfer = $('#is_transfer').val();
            var allot_warehouse = formSelects_s.value('allot_warehouse', 'val');
            var arrival_shop_at = $('#arrival_shop_at').val();
            if(produce_diff == true && brand_ids == '' && warehouse_ids == "" && supplier_ids == "" && status == "" && arrival_code == "" && purchase_code == "" && barcode == "" && delivery_sign == "" && phone == "" && box_no == "" && third_party_no == "" && arrive_time == ""){
                alert("请设置期望到店时间");
            }else {
                $.ajax({
                    url: '/arrivalOrder/down_arrival_detail',
                    type: 'post',
                    data: {
                        brand_ids:brand_ids,
                        warehouse_ids:warehouse_ids,
                        supplier_ids:supplier_ids,
                        status:status,
                        arrival_code:arrival_code,
                        purchase_code:purchase_code,
                        barcode:barcode,
                        delivery_sign:delivery_sign,
                        phone:phone,
                        box_no:box_no,
                        third_party_no:third_party_no,
                        produce_diff:produce_diff,
                        arrive_time:arrive_time,
                        is_transfer:is_transfer,
                        allot_warehouse:allot_warehouse,
                        arrival_shop_at:arrival_shop_at,
                    },
                    dataType: 'JSON',
                    async: false,//这是重要的一步，防止重复提交的
                    success: function (result) {
                        if (result.code == 200) {
                            layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                                layer.close(index);
                            })
                        }else{
                            layer.alert(result.msg);
                        }
                    },
                    error: function () {
                        layer.alert(JSON.stringify('服务器相应错误'));
                    }
                });

                // $formArrival.submit();
                // layer.msg('导出已提交，请稍等！', {icon: 1, time: 2000})
            }
        })
    }

    function print(arrivalCode){
        LODOP.SET_LICENSES("","94D9E78FC9B721D1EBF7165B69114B39A35","","");
        LODOP.PRINT_INITA(0,0,188,113,"");
        LODOP.SET_PRINT_MODE("PRINT_NOCOLLATE",1);
        LODOP.ADD_PRINT_BARCODE(10,10,168,89,"128Auto",arrivalCode);
        LODOP.SET_PRINT_STYLEA(0,"FontSize",9);
        LODOP.SET_PRINT_STYLEA(0,"Horient",2);
        LODOP.SET_PRINT_STYLEA(0,"Vorient",2);
        // LODOP.PREVIEW();
        LODOP.PRINT();
    }

</script>

</body>
</html>
