<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>{{ $title }}</title>
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<link rel="stylesheet" href="/static/layui/css/layui.css" />
		<link rel="stylesheet" href="/static/css/pearTab.css" />
		<link rel="stylesheet" href="/static/css/pearTheme.css" />
		<link rel="stylesheet" href="/static/css/pearLoad.css" />
		<link rel="stylesheet" href="/static/css/pearFrame.css" />
		<link rel="stylesheet" href="/static/css/pearAdmin.css" />
		<link rel="stylesheet" href="/static/css/pearNotice.css" />
		<link rel="stylesheet" href="/static/css/pearMenu.css" />
		<link rel="stylesheet" href="/static/css/pearCommon.css"/>
        <link rel="stylesheet" href="/static/css/myui.css"/>
		<style id="pearone-bg-color"></style>
        <style>

        .float_right {
            float: right;
            position: absolute;
            right: 38%;
            top: 35px;
            height: 500px; /* 设置你需要的高度 */
            overflow: auto; /* 添加滚动条 */
        }

        .table-wrap {
            display: none;
            /*position: absolute;*/
        }

        .mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 98;
            /*让鼠标事件透过遮罩层到达下面的表格*/
            pointer-events: none;
        }
        .downloadTable {

        }

        </style>
	</head>
	<body class="layui-layout-body pear-admin">
		<!-- 布局框架 -->
		<div class="layui-layout layui-layout-admin">
			<div class="layui-header">
				<ul class="layui-nav layui-layout-left">
					<li class="collaspe layui-nav-item layui-hide-xs"><a href="#" class="layui-icon layui-icon-shrink-right"></a></li>
					<li class="refresh layui-nav-item"><a href="#" class="layui-icon layui-icon-refresh-1"></a></li>
				</ul>
				<ul class="layui-nav layui-layout-right row h-hvc">
                    <!--统一下载按钮-->
                    <div id="down_icon" class="icon-wrap" style="position: relative;">
                        <i style="cursor:pointer;font-weight:bolder;font-size: 25px; color: #1E9FFF;" class="layui-icon layui-icon-download-circle">
                            <span class="layui-badge" id="downNum"></span>
                        </i>
                    </div>
                    <div id="table-container" class="table-wrap float_right">
                        <table class="layui-hide downloadTable" id="downloadTable" lay-filter="tableBar"></table>
                        <div class="mask"></div>
                    </div>
					<li style="margin-left: 150px;" class="layui-nav-item layui-hide-xs"><a href="#" class="fullScreen layui-icon layui-icon-screen-full"></a></li>
					<li class="layui-nav-item" lay-unselect="">
						<a href="javascript:;"><img src="/static/images/avatar.jpg" class="layui-nav-img">{{$app_name}}</a>
						<dl class="layui-nav-child">
							<dd><a href="/cache">清理缓存</a></dd>
							<dd><a href="/logout">注销登陆</a></dd>
						</dl>
					</li>
					<li class="setting layui-nav-item"><a href="#" class="layui-icon layui-icon-more-vertical"></a></li>
				</ul>
			</div>
			<div class="layui-side layui-bg-black">
				<div class="layui-logo">
					<img class="logo" src="/static/images/logo.png" />
					<span class="title"></span>
				</div>
				<div class="layui-side-scroll">
					<div id="sideMenu"></div>
				</div>
			</div>
			<div class="layui-body">
				<div id="content">{{ $token }}</div>
			</div>
		</div>

		<!-- 移动端 遮盖层 -->
		<div class="pear-cover"></div>


		<!-- 移动端 的 收缩适配 -->
		<div class="collaspe pe-collaspe layui-hide-sm">
			<i class="layui-icon layui-icon-shrink-right"></i>
		</div>

        <input type="hidden" id="ws_host" name="ws_host" value="{{env('WSS_HOST','wss://d.bigoffs.com')}}" />
        <input type="hidden" id="oss_base_url" name="ws_host" value="{{env('OSS_BASE_URL','')}}" />

        <script id="optbar" type="text/html">
            @verbatim
            {{# if (d.status == 3) { }}
                {{# if (d.file_path.length > 0) { }}
                    <a lay-event="download" style="color: #0a67fb;cursor: pointer;" >下载</a>
                {{# } }}
            {{# } else if (d.status == 4) { }}
                <a lay-event="retry" style="color: #0a67fb;cursor: pointer;" >重试</a>
            {{# } else { }}
                <span>{{d.progress}}%</span>
            {{# } }}
            @endverbatim
        </script>

        <script type="text/html"></script>
		<script src="/static/layui/layui.js"></script>
		<script>
			layui.config({
                base: '/static/js/' //自定义模块
            }).use(['pearAdmin', 'jquery', 'pearSocial', 'layer','table'], function() {
				var pearAdmin = layui.pearAdmin;
				var $ = layui.jquery;
				var layer = layui.layer;
				var table = layui.table;

				var config = {
					muiltTab: true, // 是 否 开 启 多 标 签 页 true 开启 false 关闭
					control: false, // 是 否 开 启 多 系 统 菜 单 true 开启 false 关闭
					theme: "dark-theme", // 默 认 主 题 样 式 dark-theme 默认主题 light-theme 亮主题
					index: '/main', // 默 认 加 载 主 页
					data: '/menu', // 菜 单 数 据 加 载 地 址
					select: '0',                 // 默 认 选 中 菜 单 项
				};
				pearAdmin.render(config);

                var uid = localStorage.getItem('uid')
                var oss_base_url = $('#oss_base_url').val()
                var ws_host = $('#ws_host').val()
                var reconnectInterval = 1000 // 初始重连间隔，单位毫秒
                var maxReconnectInterval = 60000 // 最大重连间隔，单位毫秒
                var reconnectTimeout = ''
                var downData = []
                var curUserInfo = {}

                getCurUserInfo()
                function getCurUserInfo() {
                    $.ajax({
                        type: 'post',
                        url: "/common/currentUserInfo",
                        async: false,
                        data: {},
                        success: function (res) {
                            if (res.code === 200) {
                                curUserInfo = res.data
                            } else {
                                layer.msg(res.msg);
                            }
                        }
                    })

                }

                console.log('uid',uid)
                console.log('curUserInfo',curUserInfo)
                console.log('oss_base_url1=',oss_base_url)
                console.log('ws_host1=',ws_host)

                function rendDownNum() {
                    let num = 0
                    // 取downData中process<100的数量
                    for (let i = 0;i < downData.length;i++) {
                        if (downData[i].progress < 100 && downData[i].status != 4) num++
                    }

                    if (num > 0 && num < 99) {
                        $('#downNum').text(num)
                    } else if (num > 99) {
                        $('#downNum').text('99+')
                    } else {
                        $('#downNum').text('0')
                    }
                }

                var socket;
                // websocket
                connectWebSocket()
                function connectWebSocket() {
                    // socket = new WebSocket(`ws://*************:12306/wms/${uid}`); // 生产
                    // socket = new WebSocket(`ws://**********:12306/wms/${uid}`); // 测试
                    // socket = new WebSocket(`ws://**********:23432/wms/${uid}`); //开发

                    socket = new WebSocket(`${ws_host}/wms/${curUserInfo.uid}`);

                    socket.onmessage = ( event => {
                        var data = JSON.parse(event.data);
                        console.log("接收到服务端的值：", data);
                        // 处理数据并更新页面
                        if (data != null) {
                            if (data.data!=null){
                                if (data.type=='1'){
                                    downData=data.data;
                                    renderTable();
                                    if (downData.length>0 && [3,4].includes(downData[0].status)){
                                        // 导出任务已完成，调用后端接口，允许下一次请求
                                        makeCanExport()
                                    }
                                }else if(data.type=='3'){
                                    layer.alert('<pre>'+data.data.content+'</pre>', {title:data.data.title,skin:"layui-layer-molv",area:["550px","350px"]},function (index) {
                                        var message = {"type":"4","msg_id":data.data.msg_id};
                                        socket.send(JSON.stringify(message));
                                        layer.close(index);
                                    });

                                }
                            }
                            if(data.type=='4'){
                                console.log('-------->',data)
                                parent.layer.closeAll()
                            }
                            
                        }else{
                            console.log("没有数据");
                        }
                    })

                    socket.onclose = (event) => {
                        console.log(event,"关闭")
                        if (event.code === 1000 || event.code === 1001) {
                            // 正常关闭或者由服务器关闭，不需要重连
                        } else {
                            // 连接异常关闭，尝试自动重连
                            clearTimeout(reconnectTimeout);
                            reconnectTimeout= setTimeout(connectWebSocket, reconnectInterval);
                            reconnectInterval = Math.min(reconnectInterval * 2, maxReconnectInterval); // 指数退避算法
                        }
                    };

                }

                function makeCanExport() {
                    let loading =  layer.load(1, {
                        shade: [0.1,'#fff'] //0.1透明度的白色背景
                    });
                    $.ajax({
                        type: 'post',
                        url: "/stockView/makeCanExport",
                        async: false,
                        data: {},
                        success: function (res) {
                            layer.close(loading)
                            console.log('恢复导出任务可下载',res)
                        }
                    })
                }

                renderTable()
                function renderTable() {
                    rendDownNum()
                    table.render({
                        elem: '#downloadTable',
                        cols: [[
                            {field: 'name', title: '标题', width: 260},
                            {field: 'remark', title: '备注', width: 150},
                            {fixed: 'right', title: '操作',width: 100, toolbar:  '#optbar',align:'center'}
                        ]],
                        data: downData,
                        page: false, // 是否显示分页
                        limit: Number.MAX_VALUE,
                    });
                }

                table.on("tool(tableBar)", function (obj) {
                    console.log('tableBar=',obj)
                    let event = obj.event;
                    let data = obj.data;
                    if (event == 'download') {
                        console.log('download-url=',data.file_path)
                        location.href = `${oss_base_url}${obj.data.file_path}`
                    }
                    if (event == 'retry') {
                        retry(data.serial_no)
                    }
                })

                function retry(serial_no) {
                    let loading =  layer.load(1, {
                        shade: [0.1,'#fff'] //0.1透明度的白色背景
                    });
                    $.ajax({
                        type: 'post',
                        url: "/stockView/retry",
                        async: false,
                        data: {serial_no},
                        success: function (res) {
                            layer.close(loading)
                            console.log('retry-res=',res)
                            if (res.code === 200) {
                                layer.msg(res.msg,{icon: 1});
                            } else {
                                layer.msg(res.msg,{icon: 2});
                            }
                        }
                    })
                }

                $(".table-wrap").hide();

                $(".icon-wrap").on("mouseenter","i", function(){
                    console.log('鼠标移入下载图标')
                    $(".table-wrap").show();
                });

                // 在表格上直接监听 mouseleave 事件
                $(".table-wrap").on("mouseleave", function() {
                    console.log('鼠标移出表格')
                    $(".table-wrap").hide();
                });

                function createSimpleWatermark() {
                    const div = document.createElement('div');
                    div.style.position = 'fixed';
                    div.style.top = '0';
                    div.style.left = '0';
                    div.style.width = '100%';
                    div.style.height = '100%';
                    div.style.pointerEvents = 'none';
                    div.style.zIndex = '999999999';

                    const canvas = document.createElement('canvas');
                    canvas.width = 300;
                    canvas.height = 200;
                    const ctx = canvas.getContext('2d');
                    ctx.rotate(-15 * Math.PI / 180);
                    ctx.font = '16px Arial';
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
                    
                    // 格式化当前时间为 'YYYY-mm-dd H:i:s'
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = String(now.getMonth() + 1).padStart(2, '0');
                    const day = String(now.getDate()).padStart(2, '0');
                    const hours = String(now.getHours()).padStart(2, '0');
                    const minutes = String(now.getMinutes()).padStart(2, '0');
                    const seconds = String(now.getSeconds()).padStart(2, '0');
                    const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                    
                    ctx.fillText(curUserInfo.username + ' ' + formattedTime, 0, 100);

                    div.style.background = 'url(' + canvas.toDataURL() + ') repeat';
                    document.body.appendChild(div);
                }
                createSimpleWatermark()
			})
		</script>
	</body>
</html>
