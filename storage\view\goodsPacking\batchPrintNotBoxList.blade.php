<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>{{$title}}</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/layui/css/layui.css" rel="stylesheet"/>
    <link href="/static/css/pearCommon.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/static/css/myui.css"/>
    <script src="/static/layui/layui.js" charset="utf-8"></script>
    <script src="http://127.0.0.1:8000/CLodopfuncs.js"></script>
    <style>

        text-c{
            text-align: center;
        }
        mlr27{
            margin: 0 27px 0 27px;
        }
        plr27{
            padding: 0 27px 0 27px;
        }
        .bold_tb {
            border-top: 1px dashed #d9d9d9;
            border-bottom: 1px dashed #d9d9d9;
        }
        .bold_t {
            border-top: 1px dashed #d9d9d9;
        }
        .bold_b {
            border-bottom: 1px dashed #d9d9d9;
        }
        .dash {
            border-bottom: 1px dashed #d9d9d9;
        }

        .line_dash {
            border-bottom: 1px dashed black;
        }
    </style>
</head>

<body class="bg_gray" style="background-color: #fff;">
<input type="hidden" name="export" value="{{$export}}" />
<input type="hidden" name="source_no" value="{{$source_no}}" />
<input type="hidden" name="pick_order_detail_ids" value="{{$pick_order_detail_ids}}" />
<!--startprint-->
<div style="width: 100%;text-align: center" id="js_print_content"><h2 id="print_process"></h2></div>
<!--endprint-->

</body>
<script src="/static/layui/layui.js"></script>
<script src="/static/js/jquery.min.js"></script>
<script src="/static/js/jQuery.print.js"></script>
<script type="text/javascript" src="/static/js/JsBarcode.all.min.js"></script>
<script>

    layui.config({
        base: '/static/js/' //自定义模块
    }).use(['form','element','layer', 'iframeTools','jquery','table' ,'laydate', 'comm'], function () {
        // Tab的切换功能，切换事件监听等，需要依赖element模块
        var $ = layui.jquery
        var layer = layui.layer
        var table = layui.table
        var form = layui.form
        var laydate = layui.laydate
        var iframeTools = layui.iframeTools
        // var comm = layui.comm
        var state  = {
            source_no: $('input[name=source_no]').val(),
            export: $('input[name=export]').val(),
            pick_order_detail_ids: $('input[name=pick_order_detail_ids]').val(),
            htmlStr: ''
        }

        // $("#jsBarcode").JsBarcode("GP15498498!");
        // $("#jsBarcode").JsBarcode(state.box_no,{
        // 	width:2,
        // 	height:50,
        // });

        initJsBarcode()
        function initJsBarcode() {
            JsBarcode(".jsBarcode").init();
        }

        /********************************************自定义方法***************************************************/

        $(document).ready(function (){
            // 获取批量打印数据
            let data = getPrintList(state.ids)
            if (data.length > 0) {
                lodopPrint(data)
                window.parent.location.reload();
            } else {
                layer.msg('无打印数据', {icon: 2, time: 2000})
            }
        })


        function  Print(){
            let bdhtml = window.document.body.innerHTML;
            let sprnstr = "<!--startprint-->";  //开始打印标识字符串有17个字符
            let eprnstr = "<!--endprint-->";    //结束打印标识字符串
            let prnhtml = bdhtml.substr(bdhtml.indexOf(sprnstr) + 17);  //从开始打印标识之后的内容
            prnhtml = prnhtml.substring(0, prnhtml.indexOf(eprnstr));  //截取开始标识和结束标识之间的内容
            window.document.body.innerHTML = prnhtml;  //把需要打印的指定内容赋给body.innerHTML
            var style = document.createElement('style');
            style.innerHTML = "@page{size: portrait;margin: 0 0 0 0;}";
            window.document.head.appendChild(style);
            window.print();  //调用浏览器的打印功能打印指定区域

            return false
        }

        function getPrintList() {
            let data = []
            $.ajax({
                type: 'post',
                url: "/goodsPacking/notBoxList",
                data: {
                    export: state.export,
                    source_no: state.source_no,
                    pick_order_detail_ids: state.pick_order_detail_ids
                },
                async: false,
                success: function (res) {
                    console.log('获取打印数据: ', res)
                    data = res.data
                }
            })

            return data
        }

        function lodopPrint(data) {
            var de_hight=0.2;
            var left = "0.7cm";
            var right = "3.7cm";
            var right_content = "2.3cm";
            var img_middle = "2.5cm";
            var info_right_label = 3.2;
            var info_right_txt = 1.3;

            LODOP.SET_LICENSES("","94D9E78FC9B721D1EBF7165B69114B39A35","","");

            //整体页面设置
            //LODOP=getLodop(document.getElementById('LODOP'),document.getElementById('LODOP_EM'));
            LODOP.PRINT_INITA(de_hight+"cm","0.00cm","8.00cm","15.00cm","好超值漏装箱清单打印任务");
            LODOP.SET_PRINT_PAGESIZE(3,'8.00cm','0.45cm',"");
            LODOP.SET_PRINT_STYLE("FontSize",8);
            LODOP.SET_PRINT_STYLE("FontName", "微软雅黑");
            LODOP.SET_PRINT_COPIES(1);

            //页面元素
            LODOP.ADD_PRINT_IMAGE(de_hight+"cm",img_middle,"3cm","2.1cm","<img src='/static/images/bigoffs-logo.jpg'>");
            LODOP.SET_PRINT_STYLEA(0,"Stretch",2);
            LODOP.SET_PRINT_STYLEA(0,"Alignment",2);
            LODOP.SET_PRINT_STYLEA(0,"FontSize",15);

            de_hight+=2.2;
            LODOP.ADD_PRINT_TEXT(de_hight+"cm","0.3cm","7.5cm","0.70cm","好超值（天津）信息技术有限公司");
            LODOP.SET_PRINT_STYLEA(0,"FontSize",11);
            LODOP.SET_PRINT_STYLEA(0,"Alignment",2);

            //横线-实线
            de_hight+=0.8;
            LODOP.ADD_PRINT_LINE(de_hight+"cm","0cm",de_hight+"cm","9cm",2,0);

            $.each(data,function(key,val) {
                de_hight += 0.2;
                LODOP.ADD_PRINT_TEXT(de_hight + "cm", left, "8cm", "0.30cm", "品牌/货号");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                LODOP.SET_PRINT_STYLEA(0,"FontName", "宋体");

                LODOP.ADD_PRINT_TEXT(de_hight + "cm", right, "8cm", "0.30cm", "店内码/货位/拣货人");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                LODOP.SET_PRINT_STYLEA(0,"FontName", "宋体");

                de_hight += 0.4;
                LODOP.ADD_PRINT_TEXT(de_hight + "cm", left, "10cm", "0.30cm", val.brand_name);
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                LODOP.SET_PRINT_STYLEA(0,"FontName", "宋体");

                LODOP.ADD_PRINT_TEXT(de_hight + "cm", right, "8cm", "0.30cm", val.unique_code);
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                LODOP.SET_PRINT_STYLEA(0,"FontName", "宋体");

                de_hight += 0.4;
                LODOP.ADD_PRINT_TEXT(de_hight + "cm", left, "10cm", "0.30cm", val.spu_no);
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                LODOP.SET_PRINT_STYLEA(0,"FontName", "宋体");

                LODOP.ADD_PRINT_TEXT(de_hight + "cm", right, "10cm", "0.30cm", val.shelf_code + " " + val.pick_admin_name);
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                LODOP.SET_PRINT_STYLEA(0,"FontName", "宋体");

                //横线-虚线
                de_hight+=0.5;
                LODOP.ADD_PRINT_LINE(de_hight+"cm","0cm",de_hight+"cm","9cm",2,0);

            });

            // 打印下边距留白
            de_hight+=0.7;
            LODOP.ADD_PRINT_TEXT(de_hight+"cm",left,"2.00cm","0.30cm"," ");
            LODOP.SET_PRINT_STYLEA(0,"FontSize",9);

            // 设置默认打印机名称
            LODOP.SET_PRINTER_INDEXA("bigoffs_xiaopiao");

            return LODOP.PRINT();
            // LODOP.PREVIEW();
        }

    })
</script>
</body>

</html>
