<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Constants\CachePre;
use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Constants\SerialType;
use App\Exception\BusinessException;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\AllotServiceInterface;
use App\JsonRpc\BackOrderServiceInterface;
use App\JsonRpc\EpcPoolServiceInterface;
use App\JsonRpc\GoodsPackingServiceInterface;
use App\JsonRpc\OutStoreServiceInterface;
use App\JsonRpc\SerialNoServiceInterface;
use App\JsonRpc\ShelfGoodsCodeMapServiceInterface;
use App\JsonRpc\SkuBarcodeServiceInterface;
use App\JsonRpc\SkuServiceInterface;
use App\JsonRpc\TemplateSetServiceInterface;
use App\Library\Facades\AdminService;
use App\Library\Facades\TemplateSetService;
use App\Library\Facades\AllotService;
use App\Library\Facades\BackOrderService;
use App\JsonRpc\UniqueCodeLogServiceInterface;
use App\Library\GoodsPacking\CacheData;
use App\Library\GoodsPacking\GoodsPackingCommon;
use App\Library\RedisLock;
use App\Service\CommonService;
use App\Service\HashService;
use App\Service\RedisService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Validation\Rule;
use phpDocumentor\Reflection\PseudoTypes\False_;
use PhpParser\Node\Stmt\Throw_;

/**
 * @Controller()
 */
class GoodsPackingController extends AbstractController
{
    // 装箱页面数据缓存6个小时
    const GP_CACHE_EXPIRE = 60*60*24;

    /**
     * @Inject ()
     * @var SerialNoServiceInterface
     */
    private $serialNoService;

    /**
     * @Inject ()
     * @var UniqueCodeLogServiceInterface
     */
    private $UniqueCodeLogService;

    /**
     * @Inject ()
     * @var OutStoreServiceInterface
     */
    private $OutStoreService;

    /**
     * @Inject ()
     * @var SkuBarcodeServiceInterface
     */
    private $SkuBarcodeService;

    /**
     * @Inject ()
     * @var AllotServiceInterface
     */
    private $AllotService;

    /**
     * @Inject ()
     * @var ShelfGoodsCodeMapServiceInterface
     */
    private $ShelfGoodsCodeMapService;

    /**
     * @Inject ()
     * @var BackOrderServiceInterface
     */
    private $BackOrderService;

    /**
     * @Inject ()
     * @var RedisService
     */
    private $RedisService;

    /**
     * @Inject ()
     * @var SkuServiceInterface
     */
    private $SkuService;

    /**
     * @Inject ()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject ()
     * @var GoodsPackingServiceInterface
     */
    private $GoodsPackingService;

    /**
     * @Inject ()
     * @var EpcPoolServiceInterface
     */
    private $EpcPoolService;

    /**
     * @Inject ()
     * @var HashService
     */
    private $HashService;

    const E_DATA_SUFFIX = '_e_data';
    const NORMAL_DATA_SUFFIX = '_normal_data';

    /**
     * 根据任务类型、单号，获取单据信息
     * @param $sourceNo
     * @param $taskType
     */
    private function taskInfo($sourceNo,$taskType){
        $result = [];
        switch ($taskType){ // 1=调拨任务,2=退返任务
            case 1:
                $result = AllotService::infoByNo($sourceNo);
                break;
            case 2:
                $result = BackOrderService::getOrder(['serial_no'=>$sourceNo]);
                break;
        }
        return $result;
    }

    /**
     * 新建装箱
     * @RequestMapping(path="/goodsPacking/add", methods="get,post")
     */
    public function add(RequestInterface $request)
    {
        $params = $request -> all();

        if ($request -> isMethod('POST')) {
            $validator = validate()->make($params, [
                'upload_type' => 'required|integer',
//                'box_no' => 'required|integer',
                'task_type' => 'required|integer',
                'source_no' => 'required|string',
                'gather_way' => 'required|integer',
            ],[
                'task_type.required'=> '任务类型必填',
                'task_type.integer'=> '任务类型参数必须是正整数',
                'source_no.required'=> '来源单号必填',
                'source_no.string'=> '来源单号必须是字符串',
                'gather_way.required'=> '采集方式必选',
                'gather_way.integer'=> '采集方式必须是正整数',
            ]);

            if ($validator->fails()) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
            }

            if ($params['gather_way'] != 3 && !isset($params['box_no'])) { // 批量上传店内码数据从redis获取，不必校验此项
                if ($validator->fails()) {
                    return $this->returnApi( ErrorCode::REQUEST_ERROR, '箱号必填~' );
                }
            }

            $taskInfo = $this->taskInfo($params['source_no'],$params['task_type']);
            if (empty($taskInfo)) { // 批量上传店内码数据从redis获取，不必校验此项
                return $this->returnApi( ErrorCode::REQUEST_ERROR, '无法获取来源单号信息' );
            }

            $userInfo = $this -> session -> get('userInfo');
            $adminInfo = [
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
            ];

            $pkData = [
                'pk_id' => $params['pk_id'],
                'task_type' => $params['task_type'],
                'source_no' => $params['source_no'],
                'gather_way' => $params['gather_way'],
                'box_no' => $params['box_no'],
                'lead_seal_no' => $params['lead_seal_no'],
                'admin_id' => $adminInfo['admin_id'],
                'admin_name' => $adminInfo['admin_name'],
                'pk_admin_id' => 0,
                'pk_admin_name' => '',
                'pk_time' => currentTime(),
                'goods_codes' => [],
            ];

            try {
                $pkIds = [];
                $redisKey = $this -> _makeRedisKey($userInfo['uid'],$params['gather_way'],$params['task_type'],$params['source_no'],$params['upload_type']).self::NORMAL_DATA_SUFFIX;
                $boxNoList = [];
                $batchDataBoxGroup = [];
                $boxIdMap = [];
                $redisResult = $this -> HashService -> getDataAllFromHash($redisKey);
                // 获取采集数据内的admin_id数据
                $adminIdNameMap = $this -> AdminService -> idsToNameList(array_unique(array_column($redisResult,'admin_id')));
                if ($params['gather_way'] != 23) {
                    if ($redisResult) {
                        //$redisResult = json_decode($redisResult,true);
                        if (empty($redisResult)) {
                            throw new BusinessException('有效数据为空，无法装箱');
                        }
                        $pkData['pk_admin_id'] = $adminInfo['admin_id'];
                        $pkData['pk_admin_name'] = $adminInfo['admin_name'];
                        $pkData['goods_codes'] = $redisResult;
                        logger() ->debug('装箱数据：',[$params,$pkData]);
                        $pkId = $this -> GoodsPackingService -> addGoodsPacking($pkData);
                        $batchDataBoxGroup[$pkData['box_no']] = $pkData['goods_codes'];
                        array_push($pkIds,$pkId);
                        array_push($boxNoList,$pkData['box_no']);
                        $boxIdMap[$pkData['box_no']] = $pkId;
                    } else {
                        throw new BusinessException('上传数据为空，无法装箱');
                    }
                } else {
                    // 批量上传店内码
                    if ($params['upload_type'] == 1) {
                        if (!$redisResult) {
                            throw new BusinessException('上传数据为空，无法装箱~~');
                        }
                        // $redisResult = json_decode($redisResult,true);
                        // 批量装箱
                        $c = collect($redisResult);
                        $batchDataBoxGroup = $c -> groupBy('box_no') -> toArray();
                        $adminIds = $c -> unique('admin_id') -> pluck('admin_id');
                        $adminInfoMap = $this -> AdminService -> idsToNameList($adminIds -> toArray());
                        $gatherWay = 3; // 店内码
                        logger() ->debug('装箱数据-批量店内码：',[$params,$pkData,$redisResult,$batchDataBoxGroup,$adminInfoMap,$gatherWay]);
                        $pkResult = $this -> _multiPacking($pkData,$redisResult,$batchDataBoxGroup,$adminInfoMap,$gatherWay);
                        $pkIds = $pkResult['pk_ids'];
                        $boxNoList = $pkResult['box_list'];
                        $boxIdMap = $pkResult['box_id_map'];
                    }
                    // 批量上传条码装箱
                    if ($params['upload_type'] == 2) {
                        $redisResult = $this -> HashService -> getDataAllFromHash($redisKey);
                        if (!$redisResult){
                            throw new BusinessException('上传数据为空，无法装箱~~~');
                        }
                        // $redisResult = json_decode($redisResult,true);
                        // 批量装箱
                        $c = collect($redisResult);
                        $batchDataBoxGroup = $c -> groupBy('box_no') -> toArray();
                        $adminIds = $c -> unique('admin_id') -> pluck('admin_id');
                        $adminInfoMap = $this -> AdminService -> idsToNameList($adminIds -> toArray());
                        $gatherWay = 2; // 条码
                        logger() ->debug('装箱数据-批量条码：',[$params,$pkData,$redisResult,$batchDataBoxGroup,$adminInfoMap,$gatherWay]);
                        $pkResult = $this -> _multiPacking($pkData,$redisResult,$batchDataBoxGroup,$adminInfoMap,$gatherWay);
                        $pkIds = $pkResult['pk_ids'];
                        $boxNoList = $pkResult['box_list'];
                        $boxIdMap = $pkResult['box_id_map'];
                    }
                }

                if($pkIds){
                    /********************************************记录操作日志****************************************************/
                    if($params['task_type'] == 1){
                        // 记录日志数据
                        $logData = [
                            'snow_id' => $this->request->getAttribute('snow_id'),
                            'op_id' => $taskInfo['id'],
                            'req_router_name' => '货品装箱', // 操作类型
                            'model_name' => 'allot', // 操作模块
                            'remark' => '箱号：'.implode(',',$boxNoList), // 操作内容
                        ];
                    }else{
                        $logData = [
                            'snow_id' => $this->request->getAttribute('snow_id'),
                            'op_id' => $taskInfo['id'],
                            'model_name' => 'BackOrder', // 操作模块
                            'handle_type' => '货品装箱',
                            'remark' => '箱号：'.implode(',',$boxNoList), // 操作内容

                            'admin_id' => $userInfo['uid'],
                            'admin_name' => $userInfo['nickname'],
                            'handle_time' => date('Y-m-d H:i:s'),
                        ];
                    }
                    wlog((string)$logData['snow_id'], $logData);
                    logger() -> debug('记录装箱操作日志',[$params,$batchDataBoxGroup,$logData]);
                    /********************************************记录店内码日志****************************************************/
                    // RFID装箱
                    $isRfid = 1 == (int)$params['gather_way'] && 0 === (int)$params['upload_type'];
                    // 店内码装箱
                    $isUnique = 3 == (int)$params['gather_way'] && 0 === (int)$params['upload_type'];
                    // 店内码上传
                    $isBatchUnique = 23 == (int)$params['gather_way'] && 1 === (int)$params['upload_type'];
                    if ($isRfid || $isUnique || $isBatchUnique) { // 上传店内码txt // 采集方式：1=RFID,2=条形码,3=店内码
                        logger() -> debug('记录店内码日志',[$params,$batchDataBoxGroup]);
                        foreach ($batchDataBoxGroup as $boxNo => $boxItems) {
                            // 如果一个箱号下有多个操作人id，则只记录第一个作为装箱操作人
                            $pkAdminId = array_column($boxItems,'admin_id')[0];
                            $uLogData = [
                                'unique_code' => array_column($boxItems,'unique_code'),//批量添加操作日志
                                'operation_type' => PublicCode::UNIQUE_CODE_LOG_TYPE_MAP['goods_packing'],//操作类型
                                'operation_desc' => PublicCode::PK_TASK_TYPE_NAME[$params['task_type']]."装箱，装箱ID：".$boxIdMap[$boxNo].',箱号：'.$boxNo,//操作描述
                                'operation_time' => currentTime(),//操作时间
                                'admin_id' => $pkAdminId, // 装箱人id
                                'admin_name' => $adminIdNameMap[$pkAdminId],// 装箱人姓名
                            ];
                            logger() -> debug('店内码日志参数',[$uLogData]);
                            $res = $this -> UniqueCodeLogService -> batchOperationLog((string)$this->request->getAttribute('snow_id'),$uLogData);
                            logger() -> debug('店内码日志结果',[$res]);
                        }
                    }
                }


                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['pk_ids' => $pkIds]);
            } catch (\Exception $exception) {
                $ret = $exception -> getMessage();
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
            }
        }

        // 自动生成箱号
        // 追加明细，箱号还是原来的
        $leadSealNo = '';
        if (isset($params['flag']) && 'append' == $params['flag']) {
            $boxNo = $params['box_no'];
        }
        else {
            if(isset($params['no']) && !empty($params['no'])) {
                // 如果扫描过来，则箱号，铅封号值相同
                $boxNo = $params['no'];
                $leadSealNo = $params['no'];
            } else {
                // 自动生成
                $boxNo = $this -> serialNoService -> generate(SerialType::GP);
                // 箱号是自动生成时，铅封号要默认为空，等待后期称重绑定
            }
        }
        logger() -> debug('请求参数: ',[$params]);
        return $this -> show("/goodsPacking/add",['params'=>$params,'boxNo' => $boxNo,'leadSealNo' => $leadSealNo]);
    }

    // 多个打包
    private function _multiPacking($pkData,$redisResult,$batchDataBoxGroup,$adminInfoMap,$gatherWay) {
        $pkIds = [];
        $boxNoList = [];
        $boxIdMap = [];
        //$redisResult = json_decode($redisResult,true);
        if (empty($redisResult)) {
            throw new BusinessException('有效数据为空，无法装箱~');
        }

        // 按箱号将数据分组，分别装箱
        foreach ($batchDataBoxGroup as $boxNo => $boxItems) {
            // 如果一个箱号下有多个操作人id，则只记录第一个作为装箱操作人
            $pkAdminId = array_column($boxItems,'admin_id')[0];
            $pkData['gather_way'] = $gatherWay;
            $pkData['box_no'] = $boxNo;
            $pkData['pk_admin_id'] = $pkAdminId;
            $pkData['pk_admin_name'] = $adminInfoMap[$pkAdminId];
            $pkData['goods_codes'] = $boxItems;


            $pkId = $this -> GoodsPackingService -> addGoodsPacking($pkData);
            array_push($pkIds,$pkId);
            array_push($boxNoList,$boxNo);
            $boxIdMap[$boxNo] = $pkId;
        }
        return [
            'pk_ids' => $pkIds,
            'box_list' => $boxNoList,
            'box_id_map' => $boxIdMap,
        ];
    }

    /**
     * 校验EPC
     * @RequestMapping(path="/goodsPacking/checkEpcCode", methods="get,post")
     */
    public function checkEpcCode(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'task_type' => 'required|integer',
            'source_no' => 'required|string',
            'box_no' => 'required|string',
            'gather_way' => 'required|integer',
            'goods_codes' => 'required',
        ],[
            'task_type.required'=> '任务类型必填',
            'task_type.integer'=> '任务类型参数必须是正整数',
            'source_no.required'=> '来源单号必填',
            'source_no.string'=> '来源单号必须是字符串',
            'box_no.required'=> '箱号必填',
            'box_no.string'=> '箱号必须是字符串',
            'gather_way.required'=> '采集方式必选',
            'gather_way.integer'=> '采集方式必须是正整数',
            'goods_codes.required'=> '商品码（epc码或条码）必填',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        $params['goods_codes'] = json_decode($params['goods_codes'],true);
        // 登录用户
        $userInfo = $this->session->get('userInfo');
        $goodsCodes = [];
        foreach ($params['goods_codes'] as $goods_code) {
            array_push($goodsCodes,[
                'box_no' => $params['box_no'],
                'epc_code' => $goods_code,
                'admin_id' => $userInfo['uid'],
            ]);
        }

        $newData = [
            'pk_id' => $params['pk_id'] ?? 0,
            'task_type' => $params['task_type'],
            'gather_way' => $params['gather_way'],
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname'],
            'source_no' => $params['source_no'],
            'goods_codes' => $goodsCodes,
        ];

        $redisKey = $this -> _getRedisKey($userInfo['uid'],$params['task_type'],$params['gather_way'],$params['source_no'],'epc_code');

        // 先删除
        $redis = redis();
        $redis -> del($redisKey);

        try {
            // 取出历史数据，合并，然后再一起校验
            $t1 = microtime(true);
            $finalData = $this -> _mergeHistoryData($redisKey,'epc_code',$goodsCodes);
            $t2 = microtime(true);
            $diff = number_format($t2-$t1, 3, '.', '');
            $consume_total = $diff." s";
            var_dump('取出历史数据并合并处理-代码耗时：'.$consume_total);
            $newData['goods_codes'] = $finalData;
//            $checkRes = $this -> checkData($params);
            $t1 = microtime(true);
            $newData['redis_key'] = $redisKey;
            $checkResult = $this -> _bulkCheckEpc($newData);
            $t2 = microtime(true);
            $diff = number_format($t2-$t1, 3, '.', '');
            $consume_total = $diff." s";
            var_dump('校验epc总耗时-代码耗时：'.$consume_total);
            // $redis -> set($redisKey,json_encode($checkRes),60 * 30);
            $t1 = microtime(true);
            $this -> _saveCheckResultToHash($redisKey,'epc_code',$checkResult,self::GP_CACHE_EXPIRE);
            $t2 = microtime(true);
            $diff = number_format($t2-$t1, 3, '.', '');
            $consume_total = $diff." s";
            var_dump('保存校验结果-代码耗时：'.$consume_total);

            $ret = [
                'e_data' => array_values($checkResult['e_data']),
                'e_num' => count($checkResult['e_data']),
                'normal_data' => array_values($checkResult['normal_data']),
                'normal_num' => count($checkResult['normal_data']),
                'total' => count($checkResult['e_data'])+count($checkResult['normal_data']),
            ];

            // 获取校验结果并返回
            //$ret = $this -> _getBulkCheckResult($userInfo['uid'],$params['gather_way'],$params['task_type'],$params['source_no'],$params['upload_type'],$params['export']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    // 校验数据
    public function checkData($data)
    {
        $exceptionList = [];
        //$exceptionResult = []; // 异常数据集合
        //$normalResult = []; // 正常数据集合
        $invalidEpc = []; // 无效epc
        $exceptionNum = 0; // 异常数
        // 校验来源单号
        if (isset($data['task_type']) && 1 == $data['task_type']) { // 调拨任务
            $checkRes = $this -> AllotService -> checkSerialNos([$data['source_no']]);
            if (!empty($checkRes['not_exists'])) {
                throw new BusinessException('来源单号【调拨单号】不存在');
            }
        }

        if (isset($data['task_type']) && 2 == $data['task_type']) { // 退返任务
            $checkRes = $this -> BackOrderService -> checkSerialNos([$data['source_no']]);
            if (!empty($checkRes['not_exists'])) {
                throw new BusinessException('来源单号【退返单号】不存在');
            }

            // 校验退返单是否已发货
            $checkRes = $this -> BackOrderService -> getOrder(['serial_no'=>$data['source_no']]);
            if (5 != $checkRes['status']) { // 退返状态 0待部门审核 1部门驳回 2待财务审核 3财务驳回 4待接单 5待出库 6待发货 7已发货 8已签收 -1已作废
                throw new BusinessException('该退返单无法装箱：原因（'.PublicCode::BACK_ORDER_STATUS[$checkRes['status']].'）');
            }

        }

        // 校验箱号
        if (isset($data['box_no']) && !empty($data['box_no'])) {
            $checkRes = $this -> GoodsPackingService -> checkBoxNos([$data['box_no']]);
            if (!empty($checkRes['exists'])) {
                throw new BusinessException('箱号已存在');
            }
        }

        // 采集数据
        if (isset($data['goods_codes']) && !empty($data['goods_codes'])) {

            // 校验EPC码
            if ( 1== $data['gather_way'] ) { // 按RFID采集
                // 校验epc码是否存在
                $checkRes = $this -> ShelfGoodsCodeMapService ->checkEpcExistsAndGetEpcInfo($data['goods_codes']);
                var_dump('EPC_RES==',$checkRes);

                // 不存在的epc码
                //$checkEResArr = [];
                if (!empty($checkRes['not_exists'])) {
                    foreach ($checkRes['not_exists'] as $k => $v) {
//                        array_push($invalidEpc,$v); // 无效epc
                        removeArrValue($v,$data['goods_codes']); // 剔除数组中对应epc码
                        //$exceptionNum++;
                        $checkERes['code_type'] = 1; // 1=RFID 2=条形码 3=店内码
                        $checkERes['code'] = '';
                        $checkERes['epc_code'] = $v;
                        $checkERes['num'] = 1;
                        $checkERes['exception'] = '<span style="color:red">epc码未绑定</span>';
                        array_push($exceptionList,$checkERes);
                    }
                    //return $checkEResArr;
                }

                // 存在的epc码，继续下一步校验
                $existsEpcInfo = array_column($checkRes['exists'],NULL,'epc_code');
                $existsEpc = array_keys($existsEpcInfo);
                // 存在的epc码，继续下一步校验
                if (!empty($existsEpc)) {
                    // 根据传入的EPC码按绑定店内码和条码归类
                    $checkEPCRes = $this -> ShelfGoodsCodeMapService -> getGoodsCodeMapByEpcCodes($existsEpc);
                    var_dump('check_EPC_res==',$checkEPCRes);
                    // 绑定店内码的EPC的店内码集合
                    $uniqueCodeArr = $checkEPCRes['unique_code'];
                    // 绑定条码的EPC的条码集合
                    // 注意：不同EPC绑定的条码可能是相同的
                    $barCodeArr = $checkEPCRes['barcode'];
                    //$checkResArr = [];
                    if (!empty($checkEPCRes['unique_code'])) {// 校验店内码
                        /**
                         * 店内码调拨-校验顺序：2022-06-30 02:35:00 测试，产品确定
                            0. 货品未绑定
                            1. 货品不属于此任务
                            2. 货品已打包，箱号：XHHXHXHXH
                            3. 货品未出库
                            4. 货品已锁定
                            5. 货品不存在
                         */
                            // 校验：店内码是否属于此任务
                            $uniqueCodes = array_column($checkEPCRes['unique_code'],'unique_code');
                            if (isset($data['task_type']) && 1 == $data['task_type']) { // 调拨任务，去查是否属于此次调拨任务
                                //$allotInfo = $this -> AllotService -> getAllotBySerialNo($data['source_no']);
                                $checkAllotRes = $this -> AllotService -> checkIfUniqueCodeInAllotBySerialNo($data['source_no'],$uniqueCodes);
                                var_dump('$checkAllotRes=============',$checkAllotRes);
                                if (!empty($checkAllotRes['not_exists'])) { // 店内码不属于此次任务的，异常信息写入
                                    foreach ($checkAllotRes['not_exists'] as $k => $v) {
                                        // 根据店内码找到对应epc码并从采集数据里剔除
                                        $uniqueKey = array_search2($uniqueCodeArr,'unique_code',$v); // 店内码对应键名
                                        var_dump('aaaaaaaaaaaaaaaaaaaaaaaaaaaa',$uniqueKey);
                                        if ($uniqueKey === false) {
                                            continue;
                                        }
                                        $epcCode = $uniqueCodeArr[$uniqueKey]['epc_code'];// 根据店内码找到对应epc码

                                        $Res3['code_type'] = 3; // 1=RFID 2=条形码 3=店内码
                                        $Res3['code'] = $v;
                                        $Res3['epc_code'] = $epcCode;
                                        $Res3['num'] = 1;
                                        $Res3['exception'] = '<span style="color:red">货品不属于此任务</span>';
                                        array_push($exceptionList,$Res3);

                                        $exceptionNum++;
                                        removeArrValue($epcCode,$data['goods_codes']); // 剔除数组中对应epc码
                                    }
                                }
                            }

                            if (isset($data['task_type']) && 2 == $data['task_type']) { // 退返任务，去查是否属于此次退返任务
                                $checkAllotRes = $this -> BackOrderService -> checkIfUniqueCodeInBackOrderBySerialNo($data['source_no'],$uniqueCodes);
                                if (!empty($checkAllotRes['not_exists'])) { // 店内码不属于此次任务的，异常信息写入
                                    foreach ($checkAllotRes['not_exists'] as $k => $v) {
                                        // 根据店内码找到对应epc码并从采集数据里剔除
                                        $uniqueKey = array_search2($uniqueCodeArr,'unique_code',$v); // 店内码对应键名
                                        if ($uniqueKey === false) {
                                            continue;
                                        }
                                        $epcCode = $uniqueCodeArr[$uniqueKey]['epc_code'];// 根据店内码找到对应epc码
                                        $Res3['code_type'] = 3; // 1=RFID 2=条形码 3=店内码
                                        $Res3['code'] = $v;
                                        $Res3['epc_code'] = $epcCode;
                                        $Res3['num'] = 1;
                                        $Res3['exception'] = '<span style="color:red">货品不属于此任务</span>';
                                        array_push($exceptionList,$Res3);

                                        $exceptionNum++;
                                        removeArrValue($epcCode,$data['goods_codes']); // 剔除数组中对应epc码
                                    }
                                }
                            }

                            if (!empty($checkAllotRes['exists'])) { // 店内码属于此次任务的。继续下一步校验：是否货品已打包
//                              // $checkPackedRes = $this -> GoodsPackingService ->checkIfUniquePacked($checkAllotRes['exists']);
                                $checkPackedRes = $this -> GoodsPackingService ->checkIfUniquePackedByTask((int)$data['task_type'], $data['source_no'], $checkAllotRes['exists']);
                                var_dump('$checkPackedRes=============',$checkPackedRes);
                                if (!empty($checkPackedRes['packed'])) {
                                    foreach ($checkPackedRes['packed'] as $k => $v) {
                                        // 根据店内码找到对应epc码并从采集数据里剔除
                                        $uniqueKey = array_search2($uniqueCodeArr,'unique_code',$v['unique_code']); // 店内码对应键名
                                        if ($uniqueKey === false) {
                                            continue;
                                        }
                                        $epcCode = $uniqueCodeArr[$uniqueKey]['epc_code'];// 根据店内码找到对应epc码
                                        $Res4['code_type'] = 3; // 1=RFID 2=条形码 3=店内码
                                        $Res4['code'] = $v['unique_code'];
                                        $Res4['epc_code'] = $epcCode;
                                        $Res4['num'] = 1;
                                        $Res4['exception'] = '<span style="color:red">货品已打包，箱号：'.$v['box_no'].'</span>';
                                        array_push($exceptionList,$Res4);

                                        $exceptionNum++;
                                        removeArrValue($epcCode,$data['goods_codes']); // 剔除数组中对应epc码
                                    }
                                }

                                $checkURes = $this -> ShelfGoodsCodeMapService -> checkUniqueCodes($checkAllotRes['exists']);
                                if (!empty($checkURes['not_exists'])) {
                                    foreach ($checkURes['not_exists'] as $k => $v) {
                                        // 根据店内码找到对应epc码并从采集数据里剔除
                                        $uniqueKey = array_search2($uniqueCodeArr,'unique_code',$v); // 店内码对应键名
                                        if ($uniqueKey === false) {
                                            continue;
                                        }
                                        $epcCode = $uniqueCodeArr[$uniqueKey]['epc_code'];// 根据店内码找到对应epc码
                                        $Res1['code_type'] = 3; // 1=RFID 2=条形码 3=店内码
                                        $Res1['code'] = $v;
                                        $Res1['epc_code'] = $epcCode;
                                        $Res1['num'] = 1;
                                        $Res1['exception'] = '<span style="color:red">货品不存在</span>';
                                        array_push($exceptionList,$Res1);

                                        $exceptionNum++;
                                        removeArrValue($epcCode,$data['goods_codes']); // 剔除数组中对应epc码
                                    }
                                }

                                // 存在的店内码,继续校验是否已出库
                                if (!empty($checkURes['exists'])) {
                                    // 校验货品未出库
                                    $checkInStoreRes = $this -> ShelfGoodsCodeMapService -> checkIfUniqueCodesOutStore($checkURes['exists']);
                                    if (!empty($checkInStoreRes['in_store'])) { // 如果有还在库的，则写入异常信息
                                        foreach ($checkInStoreRes['in_store'] as $k => $v) {
                                            // 根据店内码找到对应epc码并从采集数据里剔除
                                            $uniqueKey = array_search2($uniqueCodeArr,'unique_code',$v); // 店内码对应键名
                                            if ($uniqueKey === false) {
                                                continue;
                                            }
                                            $epcCode = $uniqueCodeArr[$uniqueKey]['epc_code'];// 根据店内码找到对应epc码
                                            $Res2['code_type'] = 3; // 1=RFID 2=条形码 3=店内码
                                            $Res2['code'] = $v;
                                            $Res2['epc_code'] = $epcCode;
                                            $Res2['num'] = 1;
                                            $Res2['exception'] = '<span style="color:red">货品未出库</span>';
                                            array_push($exceptionList,$Res2);

                                            $exceptionNum++;
                                            removeArrValue($epcCode,$data['goods_codes']); // 剔除数组中对应epc码
                                        }
                                    }

                                    if (!empty($checkInStoreRes['locked'])) { //锁定，则写入异常信息
                                        foreach ($checkInStoreRes['locked'] as $k => $v) {
                                            // 根据店内码找到对应epc码并从采集数据里剔除
                                            $uniqueKey = array_search2($uniqueCodeArr,'unique_code',$v); // 店内码对应键名
                                            if ($uniqueKey === false) {
                                                continue;
                                            }
                                            $epcCode = $uniqueCodeArr[$uniqueKey]['epc_code'];// 根据店内码找到对应epc码
                                            var_dump('bbbbbbbbbbbbbbbbbbbbbbbbb',$uniqueKey);
                                            var_dump('$v===============================================',$v);
                                            var_dump('$uniqueKey===============================================',$uniqueKey);
                                            var_dump('$epcCode=====================================================',$epcCode);

                                            $Res2['code_type'] = 3; // 1=RFID 2=条形码 3=店内码
                                            $Res2['code'] = $v;
                                            $Res2['epc_code'] = $epcCode;
                                            $Res2['num'] = 1;
                                            //$Res2['exception'] = '<span style="color:red">货品已锁定</span>';
                                            $Res2['exception'] = '<span style="color:red">货品未出库(已锁定)</span>';
                                            array_push($exceptionList,$Res2);

                                            $exceptionNum++;
                                            removeArrValue($epcCode,$data['goods_codes']); // 剔除数组中对应epc码
                                        }
                                    }

                                }
                            }
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

                    }

                    if (!empty($checkEPCRes['barcode'])) { // 校验条码
                        // 条形码不存在
                        // 注意：此处 不同EPC绑定的条码可能是下重复的，需要去重
                        $barcodes = array_unique(array_column($checkEPCRes['barcode'],'bar_code'));
                        $checkBRes = $this -> SkuBarcodeService -> checkBarcodes($barcodes);
                        $barCodeArrCollection = collect($barCodeArr);
                        if (!empty($checkBRes['not_exists'])) {
                            foreach ($checkBRes['not_exists'] as $k => $v) {
                                // 根据店内码找到对应epc码并从采集数据里剔除
                                //$barcodeKey = array_search2($barCodeArr,'epc_code',$v); // 店内码对应键名
                                $epcs = $barCodeArrCollection -> where('bar_code',$v) -> all();
                                if (empty($epcs)) {
                                    continue;
                                }
                                foreach ($epcs as $epc) {
                                    $epcCode = $epc['epc_code'];

                                    $uRes['code_type'] = 2; // 1=RFID 2=条形码 3=店内码
                                    $uRes['code'] = $v['bar_code'];
                                    $uRes['epc_code'] = $epcCode;
                                    $uRes['num'] = 1;
                                    $uRes['exception'] = '<span style="color:red">货品信息不存在</span>'; // 条码不存在
                                    array_push($exceptionList,$uRes);

                                    $exceptionNum++;
                                    removeArrValue($epcCode,$data['goods_codes']); // 剔除数组中对应epc码
                                }

                            }
                        }

                        // 存在货品信息的条码继续下一步：条形码不属于此批次
                        if (!empty($checkBRes['exists'])) {
                            if (isset($data['task_type']) && 1 == $data['task_type']) { // 调拨任务，去查是否属于此次调拨任务
                                $checkAllotRes = $this -> AllotService -> checkIfBarcodeInAllotBySerialNo($data['source_no'],$barcodes);
                                var_dump('$checkAllotRes-条码==============',$checkAllotRes);
                                if (!empty($checkAllotRes['not_exists'])) {
                                    foreach ($checkAllotRes['not_exists'] as $k => $v) {
                                        // 根据店内码找到对应epc码并从采集数据里剔除
                                        $epcs = $barCodeArrCollection -> where('bar_code',$v) -> all();
                                        if (empty($epcs)) {
                                            continue;
                                        }
                                        foreach ($epcs as $epc) {
                                            $epcCode = $epc['epc_code'];

                                            $uRes['code_type'] = 2; // 1=RFID 2=条形码 3=店内码
                                            $uRes['code'] = $v;
                                            $uRes['epc_code'] = $epcCode;
                                            $uRes['num'] = 1;
                                            $uRes['exception'] = '<span style="color:red">货品不属于此批次</span>';
                                            array_push($exceptionList,$uRes);

                                            $exceptionNum++;
                                            removeArrValue($epcCode,$data['goods_codes']); // 剔除数组中对应epc码

                                        }
                                    }
                                }

                                var_dump('调拨单剩余的条码==',$checkAllotRes);
                                // 下一步校验：条码数量是否超出调拨单任务数
                                if (!empty($checkAllotRes['exists'])) {
                                    foreach ($checkAllotRes['exists'] as $k => $v) {
                                        // 将有效条码数量与 可出库数量比较
                                        // 单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
                                        // 商品码类型：1=店内码，2=条码
                                        var_dump('check_data-allot==',$data['task_type']);
                                        $OutStockNum = $this -> OutStoreService -> getNotOutStoreNum($data['source_no'],(int)$data['task_type'],$v,2); // 单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
                                        $barcodeNum = 1;// epc绑定的条码，数量是一个

                                        if ($barcodeNum > $OutStockNum) {
                                            // 根据店内码找到对应epc码并从采集数据里剔除
                                            $epcs = $barCodeArrCollection -> where('bar_code',$v) -> all();
                                            if (empty($epcs)) {
                                                continue;
                                            }
                                            foreach ($epcs as $epc) {
                                                $epcCode = $epc['epc_code'];

                                                $uRes['code_type'] = 2; // 1=RFID 2=条形码 3=店内码
                                                $uRes['code'] = $v;
                                                $uRes['epc_code'] = $epcCode;
                                                $uRes['num'] = 1;
                                                $uRes['exception'] = '<span style="color:red">货品数量超出任务数量</span>';
                                                array_push($exceptionList,$uRes);

                                                $exceptionNum++;
                                                removeArrValue($epcCode,$data['goods_codes']); // 剔除数组中对应epc码

                                            }
                                        }
                                    }
//                                    // 调拨单剩余的有效条码数量
//                                    $validBarcodeNum = count($checkAllotRes['exists']);
//                                    // 将有效条码数量与 未出库数量(调拨单任务数-已出库任务数)比较，不能超出未出库数量
//                                    $NotOutStockNum = $this -> OutStoreService -> getNotOutStoreNum($data['source_no'],23); // 单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
//                                    var_dump('有效条码总数1==',$validBarcodeNum);
//                                    var_dump('未出库总数1==',$NotOutStockNum);
//                                    if ($validBarcodeNum > $NotOutStockNum) { // 有效条码超出任务数
//                                        $uRes['code_type'] = 2; // 1=RFID 2=条形码 3=店内码
//                                        $uRes['code'] = $checkAllotRes['exists'];
//                                        $uRes['num'] = $validBarcodeNum;
//                                        $uRes['exception'] = '<span style="color:red">条码数量超出任务数量</span>';
//                                        array_push($exceptionList,$uRes);
//
//                                        // 根据店内码找到对应epc码并从采集数据里剔除
//                                        foreach ($checkAllotRes['exists'] as $k => $v) {
//                                            $barcodeKey = array_search2($barCodeArr,'bar_code',$v); // 条码对应键名
//                                            $epcCode = $barCodeArr[$barcodeKey]['epc_code'];// 根据条码找到对应epc码
//
//                                            $exceptionNum++;
//                                            removeArrValue($epcCode,$data['goods_codes']); // 剔除数组中对应epc码
//                                        }
//                                    }
                                }

                            }

                            if (isset($data['task_type']) && 2 == $data['task_type']) { // 退返任务，去查是否属于此次退返任务
                                $checkBackOrderRes = $this -> BackOrderService -> checkIfBarcodeInBackOrderBySerialNo($data['source_no'],array_column($checkEPCRes['barcode'],'bar_code'));
                                if (!empty($checkBackOrderRes['not_exists'])) { // 条形码不属于此次任务的，异常信息写入
                                    foreach ($checkBackOrderRes['not_exists'] as $k => $v) {
                                        // 根据店内码找到对应epc码并从采集数据里剔除
                                        $epcs = $barCodeArrCollection -> where('bar_code',$v) -> all();
                                        if (empty($epcs)) {
                                            continue;
                                        }
                                        foreach ($epcs as $epc) {
                                            $epcCode = $epc['epc_code'];

                                            $uRes['code_type'] = 2; // 1=RFID 2=条形码 3=店内码
                                            $uRes['code'] = $v;
                                            $uRes['epc_code'] = $epcCode;
                                            $uRes['num'] = 1;
                                            $uRes['exception'] = '<span style="color:red">货品不属于此批次</span>';
                                            array_push($exceptionList,$uRes);

                                            $exceptionNum++;
                                            removeArrValue($epcCode,$data['goods_codes']); // 剔除数组中对应epc码

                                        }
                                    }
                                }
                                // 下一步校验：条码数量是否超出退返单任务数
                                if (!empty($checkBackOrderRes['exists'])) {

                                    foreach ($checkBackOrderRes['exists'] as $k => $v) {
                                        // 将有效条码数量与 可出库数量比较
                                        // 单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
                                        // 商品码类型：1=店内码，2=条码
                                        var_dump('check_data-back==',$data['task_type']);
                                        $OutStockNum = $this -> OutStoreService -> getNotOutStoreNum($data['source_no'],(int)$data['task_type'],$v,2); // 单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
                                        $barcodeNum = 1;// epc绑定的条码，数量是一个

                                        if ($barcodeNum > $OutStockNum) {

                                            // 根据店内码找到对应epc码并从采集数据里剔除
                                            $epcs = $barCodeArrCollection -> where('bar_code',$v) -> all();
                                            if (empty($epcs)) {
                                                continue;
                                            }
                                            foreach ($epcs as $epc) {
                                                $epcCode = $epc['epc_code'];
                                                $uRes['code_type'] = 2; // 1=RFID 2=条形码 3=店内码
                                                $uRes['code'] = $v;
                                                $uRes['epc_code'] = $epcCode;
                                                $uRes['num'] = 1;
                                                $uRes['exception'] = '<span style="color:red">货品数量超出任务数量</span>';
                                                array_push($exceptionList,$uRes);
                                                $exceptionNum++;
                                                removeArrValue($epcCode,$data['goods_codes']); // 剔除数组中对应epc码

                                            }
                                        }
                                    }
//                                    // 调拨单剩余的有效条码数量
//                                    $validBarcodeNum = count($checkBackOrderRes['exists']);
//                                    // 将有效条码数量与 未出库数量(调拨单任务数-已出库任务数)比较，不能超出未出库数量
//                                    $NotOutStockNum = $this -> OutStoreService -> getNotOutStoreNum($data['source_no'],23); // 单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
//
//
//                                    if ($validBarcodeNum > $NotOutStockNum) { // 有效条码超出任务数
//                                        $uRes['code_type'] = 2; // 1=RFID 2=条形码 3=店内码
//                                        $uRes['code'] = $checkBackOrderRes['exists'];
//                                        $uRes['num'] = $validBarcodeNum;
//                                        $uRes['exception'] = '<span style="color:red">条码数量超出任务数量</span>';
//                                        array_push($exceptionList,$uRes);
//
//                                        // 根据店内码找到对应epc码并从采集数据里剔除
//                                        foreach ($checkBackOrderRes['exists'] as $k => $v) {
//                                            $barcodeKey = array_search2($barCodeArr,'bar_code',$v); // 条码对应键名
//                                            $epcCode = $barCodeArr[$barcodeKey]['epc_code'];// 根据条码找到对应epc码
//
//                                            $exceptionNum++;
//                                            removeArrValue($epcCode,$data['goods_codes']); // 剔除数组中对应epc码
//                                        }
//                                    }
                                }
                            }
                        }
                    }
                }
            }

            if ( 2== $data['gather_way'] ) { // 条码采集
                /*$barcodes = [];
                foreach ($data['goods_codes'] as $k => $v) {
                    if (0 == $v['e_status']) { // 0异常，1正常
                        array_push($barcodes,$v['barcode']);
                    }
                }*/
                $barcodes = array_column($data['goods_codes'],'barcode');
                $checkBRes = $this -> SkuBarcodeService -> checkBarcodes($barcodes,false);

                if (!empty($checkBRes['not_exists'])) {
                    foreach ($checkBRes['not_exists'] as $k => $v) {
                        $uRes['code_type'] = 2; // 1=RFID 2=条形码 3=店内码
                        $uRes['code'] = $v['bar_code'];
                        $uRes['num'] = 1;
                        $uRes['exception'] = '<span style="color:red">条码不存在</span>';
                        array_push($exceptionList,$uRes);

                        $exceptionNum++;
                        removeArrValue($v,$data['goods_codes']); // 剔除正常数据集合里的异常条码
                    }
                }

                if (!empty($checkBRes['exists'])) {

                    // 存在的条码继续下一步：条形码是否属于此批次
                    if (isset($data['task_type']) && 1 == $data['task_type']) { // 调拨任务，去查是否属于此次调拨任务
                        $checkAllotRes = $this -> AllotService -> checkIfBarcodeInAllotBySerialNo($data['source_no'],$checkBRes['exists']);
                        if (!empty($checkAllotRes['not_exists'])) {
                            foreach ($checkAllotRes['not_exists'] as $k => $v) {
                                $uRes['code_type'] = 2; // 1=RFID 2=条形码 3=店内码
                                $uRes['code'] = $v;
                                $uRes['num'] = 1;
                                $uRes['exception'] = '<span style="color:red">条形码不属于此批次</span>';
                                array_push($exceptionList,$uRes);

                                $exceptionNum++;
                                removeArrValue($v,$data['goods_codes']); // 剔除正常数据集合里的异常条码
                            }
                        }

                        // 下一步校验：条码数量是否超出调拨单任务数
                        if (!empty($checkAllotRes['exists'])) {
                            $submitBarcodeInfo = array_column($data['goods_codes'],NULL,'barcode'); // 提交的条码

                            foreach ($checkAllotRes['exists'] as $v) {
                                // 根据条码获取其提交的数量
                                $barcodeNum = $submitBarcodeInfo[$v]['num'];
                                $barcode = $submitBarcodeInfo[$v]['barcode'];

                                // 将有效条码数量与 可出库数量比较
                                // 单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
                                // 商品码类型：1=店内码，2=条码
                                var_dump('check_data-allot22222==',$data['task_type']);
                                $OutStockNum = $this -> OutStoreService -> getNotOutStoreNum($data['source_no'],(int)$data['task_type'],$barcode,2);

                                if ($barcodeNum > $OutStockNum) { // 有效条码超出任务数
                                    $uRes['code_type'] = 2; // 1=RFID 2=条形码 3=店内码
                                    $uRes['code'] = $v;
                                    $uRes['num'] = $barcodeNum;
                                    $uRes['exception'] = '<span style="color:red">条码数量超出任务数量</span>';
                                    array_push($exceptionList,$uRes);

                                    $exceptionNum++;
                                    removeArrValue($v,$data['goods_codes']); // 剔除正常数据集合里的异常条码
                                }
                            }

                        }

                    }

                    if (isset($data['task_type']) && 2 == $data['task_type']) { // 退返任务，去查是否属于此次退返任务
                        $checkBackOrderRes = $this -> BackOrderService -> checkIfBarcodeInBackOrderBySerialNo($data['source_no'],$checkBRes['exists']);
                        if (!empty($checkBackOrderRes['not_exists'])) { // 条形码不属于此次任务的，异常信息写入
                            foreach ($checkBackOrderRes['not_exists'] as $k => $v) {
                                $uRes['code_type'] = 2; // 1=RFID 2=条形码 3=店内码
                                $uRes['code'] = $v;
                                $uRes['num'] = 1;
                                $uRes['exception'] = '<span style="color:red">条形码不属于此批次</span>';
                                array_push($exceptionList,$uRes);

                                $exceptionNum++;
                                removeArrValue($v,$data['goods_codes']); // 剔除正常数据集合里的异常条码
                            }
                        }
                        // 下一步校验：条码数量是否超出退返单任务数
                        if (!empty($checkBackOrderRes['exists'])) {
                            // 将有效条码数量与 未出库数量(退返单任务数-已出库任务数)比较，不能超出未出库数量
                            $submitBarcodeInfo = array_column($data['goods_codes'],NULL,'barcode'); // 提交的条码
                            foreach ($checkBackOrderRes['exists'] as $v) {
                                // 根据条码获取其提交的数量
                                $barcode = $submitBarcodeInfo[$v]['barcode']; // 提交的条码
                                $barcodeNum = $submitBarcodeInfo[$v]['num']; // 提交的条码数
                                // 将有效条码数量与 可出库数量比较
                                // 单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
                                // 商品码类型：1=店内码，2=条码
                                var_dump('check_data==',$data['task_type']);
                                $OutStockNum = $this -> OutStoreService -> getNotOutStoreNum($data['source_no'],(int)$data['task_type'],$barcode,2);

                                if ($barcodeNum > $OutStockNum) { // 有效条码超出任务数
                                    $uRes['code_type'] = 2; // 1=RFID 2=条形码 3=店内码
                                    $uRes['code'] = $v;
                                    $uRes['num'] = $barcodeNum;
                                    $uRes['exception'] = '<span style="color:red">条码数量超出任务数量</span>';
                                    array_push($exceptionList,$uRes);

                                    $exceptionNum++;
                                    removeArrValue($v,$data['goods_codes']); // 剔除正常数据集合里的异常条码
                                }
                            }

                        }
                    }
                }
            }
        }

        // 将异常条形码数据聚合
//        $eList = [];
//        foreach ($exceptionList as $k => $v) {
//            $searArrRes = $this -> searchArr($eList,$v['epc_code']);
//            if (is_array($searArrRes)) {// 搜索到了结果，将对应值的num累加1
//                $element = $eList[$searArrRes['key']];
//                $num = $element['num'] + 1;
//                $eList[$searArrRes['key']]['num'] = $num;
//            } else {
//                array_push($eList,$v);
//            }
//        }

        // 正常数据集合（每次检测到异常数据后剔除epc之后剩余的正常epc）
        $normalList = $data['goods_codes'];

        $return = [
            'exception_data' => $exceptionList,
            'exception_num' => array_sum(array_column($exceptionList,'num')),
            'normal_data' => $normalList,
            'normal_num' => count($data['goods_codes']),
//            'invalid_epc' => $invalidEpc,
//            'invalid_epc_num' => count($invalidEpc),
        ];

        return $return;
    }

    /**
     * 通过条码找出对应数据的key和nun
     * @param $normalResult
     * @param $code
     * @param $normalItem
     * @return array|false
     */
    private function searchArr($normalResult,$code)
    {
        if (empty($normalResult)) {
            return false;
        } else {
            foreach ($normalResult as $k2 => $v2) {
                if ($v2['code_type'] == 2 && $v2['code'] == $code) {
                    return ['key'=> $k2,'num'=>$v2['num']];
                }
            }
            // 未找到，返回false
            return false;
        }
    }

    /**
     * 装箱详情
     * @RequestMapping(path="/goodsPacking/detail", methods="get,post")
     */
    public function detail(RequestInterface $request)
    {
        $params = $request -> all();
        $id = $params['id'] ?? 0;
        $detailData = $this -> GoodsPackingService -> getGoodsPackingOne($id);
        return $this -> show("/goodsPacking/detail",['detail' => $detailData]);
    }

    /**
     * 打印装箱清单
     * @RequestMapping(path="/goodsPacking/reportList", methods="get,post")
     */
    public function reportList(RequestInterface $request)
    {
        var_dump('进入打印报表===');
        $params = $request -> all();
        var_dump('params11==',$params);
        $id = $params['id'];
        $detailData = $this -> GoodsPackingService -> getGoodsPackingOne($id);

        return $this -> show("/goodsPacking/reportList",['detail' => $detailData]);
    }

    /**
     * 装箱清单
     * @RequestMapping(path="/goodsPacking/billList", methods="get,post")
     */
    public function billList(RequestInterface $request)
    {
        var_dump('进入报表表格==');
        $params = $request -> all();
        var_dump('params22==',$params);
        $pkId = $params['id'];
        try {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 10;
            $export = $params['export'] ?? 0;
            $print = $params['print'] ?? 0;
            $where = $params['where'] ?? [];
            $ret = $this -> GoodsPackingService -> getGoodsPackingBillList($pkId,$export,$page,$limit,$where);
            if (1 == $print) {
                $ret = $this -> GoodsPackingService -> getGoodsPackingBillList($pkId,$export,$page,99999,[]);
            }
            $billListData = isset($ret['data']) && $ret['data'] ? array_column($ret['data'],NULL,'id') : [];
            if ($billListData) {
                $combinations = join(',',array_column($billListData,'combination'));

                // 追加规格信息
                //$commService = new CommonService;
                //$commService -> appendSpec($billListData,array_column($billListData,'combination'),'size','尺码');
                $billListData = $this -> _appendSpecNames($billListData,$combinations);
            }
            $billListData = array_values($billListData);
            if (1 == $export) {
                $exportUrl= $this -> export($billListData);
                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['url'=>$exportUrl]);
            }
            if (1 == $print) {

                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),array_values($billListData));
            }
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['total' => $ret['total'],'data' => array_values($billListData)]);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    // 追加规格信息
    private function _appendSpecNames($billListData,$combinations)
    {
        // 追加规格信息
        $specRes = [];
        if ($combinations) {
            $specRes = $this->SkuService->getSpecNameBatch(explode(',', $combinations));
        }
        $specRes = $specRes ? array_column($specRes, NULL, 'spec_kv_id') : [];
        foreach ($billListData as &$v) {
            $combinationArr = $v['combination'] ? explode(',', $v['combination']) : [];
            foreach ($combinationArr as $v2) {
                $v['spec_names'][] = $specRes[$v2]['spec_name'];
            }
        }
        return $billListData;
    }

    /**
     * 装箱信息
     * @RequestMapping(path="/goodsPacking/getPackList", methods="get,post")
     */
    public function getPackList()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['task_type'] = $params['task_type'] ?? 2;// 默认退返任务
            $search['source_no'] = $params['serial_no'];// 单号
            $export = intval($params['export']?? 0) ;// 0列表 1导出
            $search['status'] = [1, 2, 3];
            isset($params['is_quality']) && $search['is_quality'] = $params['is_quality'];

            //处理status条件
            if(isset($params['status']) && $params['status'] != '') $search['status'] = $params['status'];
            $list = $this->GoodsPackingService->packList($export, (int)$page, (int)$pageLimit, $search);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit, 'already_out_quality' => $list['already_out_quality'], 'no_quality' => $list['no_quality'], 'already_num' => $list['already_num']]);
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 装箱信息
     * @RequestMapping(path="/goodsPacking/signLogList", methods="get,post")
     */
    public function signLogList()
    {
        $source_no = $this->request->input('serial_no');

        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['source_no'] = $params['serial_no'];// 单号
            $export = intval($params['export']?? 0) ;// 0列表 1导出

            $list = $this->GoodsPackingService->signLogList($export, (int)$page, (int)$pageLimit, $search);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }
        return $this->show('/goodsPacking/signlist', [
            'source_no' => $source_no
        ]);
    }

    /**
     * 货品签收信息
     * @RequestMapping(path="/goodsPacking/signList", methods="get,post")
     */
    public function signList()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['source_no'] = $params['serial_no'];// 单号
            $search['un_sign'] = $params['un_sign'] ?? 0;// 单号
            $export = intval($params['export']?? 0) ;// 0列表 1导出

            $list = $this->GoodsPackingService->signList($export, (int)$page, (int)$pageLimit, $search);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit, 'send_num' => $list['send_num'], 'sign_num' => $list['sign_num']]);
        }
        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 箱号质检
     * @RequestMapping(path="/goodsPacking/boxQualitys", methods="get,post")
     */
    public function boxQualitys(RequestInterface $request)
    {
        $params = $this->validate($request->all(),'quality');
        $sourceNo = $params['sourceNo'];
        $boxNos = $params['boxNos'];

        $data = $this->GoodsPackingService->qualityBox($sourceNo,$boxNos);
        return $this->returnApi(ResponseCode::SUCCESS, '质检成功', $data);

    }

    /**
     * 货品编码校验
     * @RequestMapping(path="/goodsPacking/checkSignCode", methods="get,post")
     */
    public function checkSignCode(RequestInterface $request)
    {
        $params = $this->validate($request->all(),'checkSignCode');
        $params['check_status'] = 1;
        if(isset($params['box_no']) && !empty($params['box_no'])){
            $params['box_nos'] = [$params['box_no']];
            unset($params['box_no']);
        }
        $data = $this->GoodsPackingService->checkCode($params);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $data);
    }

    /**
     * 货品编码校验
     * @RequestMapping(path="/goodsPacking/checkCodeByWarehouse", methods="get,post")
     */
    public function checkCodeByWarehouse(RequestInterface $request)
    {
        $params = $this->validate($request->all(),'checkCodeByWarehouse');

        $userInfo = $this->getUserInfo();
        $cacheKey = CachePre::getKey(CachePre::BATCH_SIGN_LIST,$userInfo['uid'],$params['w_id']);
        $cacheUnSignKey = CachePre::getKey(CachePre::BATCH_UNSIGN_LIST,$userInfo['uid'],$params['w_id']);
        if(!redis()->exists($cacheKey)){
            redis()->expireAt($cacheKey,strtotime(date('Y-m-d 23:59:59')));
            redis()->expireAt($cacheKey.':uni',strtotime(date('Y-m-d 23:59:59')));
            redis()->expireAt($cacheKey.':currentBox',strtotime(date('Y-m-d 23:59:59')));
            redis()->expireAt($cacheUnSignKey,strtotime(date('Y-m-d 23:59:59')));

        }
        if(redis()->sIsMember($cacheKey.':uni',$params['unique_code'])){
            throw new BusinessException(sprintf('店内码：%s 重复扫描',$params['unique_code']));
        }

        $currentBox = redis()->get($cacheKey.':currentBox');
        $unSignCount = redis()->hLen($cacheUnSignKey);
        if (empty($currentBox) || $unSignCount <= 0){
            $result = $this->GoodsPackingService->codeGetUnSignListByWarehouse($params['w_id'],$params['unique_code']);
            $data = $result['code_info'] ?? [];
            if(!empty($data)){

//                if(redis()->sIsMember($cacheKey.':uni',$data['unique_code'])){
//                    throw new BusinessException(sprintf('店内码：%s 重复扫描',$data['unique_code']));
//                }
                foreach ($result['un_sign_list'] as $item){
                    if ($data['unique_code'] == $item['unique_code']){
                        continue;
                    }
                    redis()->hset($cacheUnSignKey,$item['unique_code'],json_encode($item,JSON_UNESCAPED_UNICODE));
                }

                redis()->set($cacheKey.':currentBox',$data['box_no']);
                redis()->sAdd($cacheKey.':uni',$data['unique_code']);
                redis()->lpush($cacheKey,json_encode($data,JSON_UNESCAPED_UNICODE));
            }
        }else{

            $data = redis()->hGet($cacheUnSignKey,$params['unique_code']);

            if (empty($data)){
                throw new BusinessException(sprintf('店内码：%s 非当前箱货品',$data['unique_code']));
            }
            $data = json_decode($data,true);
            redis()->hDel($cacheUnSignKey,$params['unique_code']);
            redis()->sAdd($cacheKey.':uni',$data['unique_code']);
            redis()->lpush($cacheKey,json_encode($data,JSON_UNESCAPED_UNICODE));
        }

        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $data);
    }

    /**
     * 坚持箱号是否可以登记
     * @RequestMapping(path="/goodsPacking/checkRegBoxNo", methods="get,post")
     */
    public function checkRegBoxNo(RequestInterface $request)
    {
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $params = $this->validate($request->all(),'checkRegBoxNo');
        $data = $this->GoodsPackingService->checkRegBoxNo($wIds,$params['boxNos']);
        $userInfo = $this->getUserInfo();
        $cacheKey = CachePre::getKey(CachePre::BATCH_REG_LIST,$userInfo['uid']);
        if(!redis()->exists($cacheKey)){
            redis()->expireAt($cacheKey,strtotime(date('Y-m-d 23:59:59')));
            redis()->expireAt($cacheKey.':box',strtotime(date('Y-m-d 23:59:59')));
        }

        $errorList = [
            'repeat_code' => [
                'list' => [],
                'msg' => '箱码{#list}重复登记'
            ],
            'not_exist' => [
                'list' => [],
                'msg' => '箱码{#list}权限缺失或箱号错误'
            ]
        ];

        $result = [];
        foreach ($params['boxNos'] as $boxNo){
            $item = $data[$boxNo] ?? [];
            if (empty($item)){
                $errorList['not_exist']['list'][] = $boxNo;
                continue;
            }
            if(redis()->sIsMember($cacheKey.':box',$item['box_no'])){
                $errorList['repeat_code']['list'][] = $item['box_no'];
                continue;
            }

            if ($item['is_reg'] == 1){
                $errorList['repeat_code']['list'][] = $item['box_no'];
                continue;
            }

            redis()->sAdd($cacheKey.':box',$item['box_no']);
            redis()->lpush($cacheKey,json_encode($item,JSON_UNESCAPED_UNICODE));
            $result['list'][] = $item;
        }

        $errorMsg = [];
        logger()->info('checkRegBoxNo',[$errorList]);
        foreach ($errorList as $errData){
            if (!empty($errData['list'])){
                $errorMsg[] = strtr($errData['msg'],['#list' => implode(',',$errData['list'])]);
            }
        }

        if (!empty($errorMsg)){
            throw new BusinessException(implode("\n",$errorMsg));
        }

        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result);
    }

    /**
     * 货品编码校验
     * @RequestMapping(path="/goodsPacking/batchCheckCodeByWarehouse", methods="get,post")
     */
    public function batchCheckCodeByWarehouse(RequestInterface $request)
    {
        $params = $this->validate($request->all(),'batchCheckCodeByWarehouse');
        $file = $request->file('file');
        if (!$file) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '请上传文件');
        }
        $ruleMap = [
            'msg' => [
                'unique_code.required'=>'店内码不能为空'
            ],
            'rule' => ['unique_code'=>'required']
        ];
        $configTemplate = getTemplateInfo(TemplateSetServiceInterface::BATCH_ALLOT_SIGN_NAME);
        // 读取excel数据
        $importData = readExcelByHeader($file, $configTemplate['original_data'], $ruleMap['rule'], $ruleMap['msg']);

        $data = $this->GoodsPackingService->batchCheckCodeByWarehouse($params['w_id'],$importData);
        $userInfo = $this->getUserInfo();
        $cacheKey = CachePre::getKey(CachePre::BATCH_SIGN_LIST,$userInfo['uid'],$params['w_id']);
        $cacheUnSignKey = CachePre::getKey(CachePre::BATCH_UNSIGN_LIST,$userInfo['uid'],$params['w_id']);
        redis()->del([$cacheKey,$cacheKey.':uni',$cacheKey.':errlist',$cacheUnSignKey]);
        if(!redis()->exists($cacheKey)){
            redis()->expireAt($cacheKey,strtotime(date('Y-m-d 23:59:59')));
            redis()->expireAt($cacheKey.':uni',strtotime(date('Y-m-d 23:59:59')));
            redis()->expireAt($cacheKey.':errlist',strtotime(date('Y-m-d 23:59:59')));
            redis()->expireAt($cacheUnSignKey,strtotime(date('Y-m-d 23:59:59')));
        }

        if(isset($data['list']) && !empty($data['list'])){
            $batchList = array_chunk($data['list'],500);
            foreach ( $batchList as $items){
                $codes = [];
                $list = [];
                foreach ($items as $item){
                    $codes[] = $item['unique_code'];
                    $list[] = json_encode($item,JSON_UNESCAPED_UNICODE);
                }
                logger()->info('batchCheckCodeByWarehouse',[$batchList,$items,$codes,$list]);
                if($codes){
                    redis()->sAdd($cacheKey.':uni', ... $codes);
                    redis()->lpush($cacheKey, ... $list);
                }
            }
        }

        if(isset($data['unsign_list']) && !empty($data['unsign_list'])){
            foreach ($data['unsign_list'] as $item){
                redis()->hset($cacheUnSignKey,$item['unique_code'],json_encode($item,JSON_UNESCAPED_UNICODE));
            }
        }
        $error_num = 0;
        if(isset($data['error_list']) && !empty($data['error_list'])){
            $error_num = count($data['error_list']);
            $this->HashService->del($cacheKey.':errlist');
            $this->HashService->saveDataToHash($cacheKey.':errlist', $data['error_list'], 60 * 30,true);
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['error_num'=>$error_num]);
    }

    /**
     * @RequestMapping(path="/goodsPacking/exportSignErrorList", methods="get,post")
     * @return mixed
     */
    public function exportSignErrorList(RequestInterface $request){
        $params = $this->validate($request->all(),'exportSignErrorList');
        $cacheKey = CachePre::getKey(CachePre::BATCH_SIGN_LIST,$this->getUserId(),$params['w_id']);
        $list = $this->HashService->getDataAllFromHash($cacheKey.':errlist');
        if($list){
            try {
                $url = exportToExcel(['unique_code'=>'店内码','msg'=>'错误原因'],$list,'批量签收错误列表');
            }catch (\Exception $e){
                throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
            }
        }else{
            throw new BusinessException('无数据，导出失败！');
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }

    /**
     * 货品编码校验
     * @RequestMapping(path="/goodsPacking/batchSign", methods="get,post")
     */
    public function batchSign(RequestInterface $request)
    {
        $params = $this->validate($request->all(),'batchSign');
        $userInfo = $this->getUserInfo();
        $cacheKey = CachePre::getKey(CachePre::BATCH_SIGN_LIST,$userInfo['uid'],$params['w_id']);
        $cacheUnSignKey = CachePre::getKey(CachePre::BATCH_UNSIGN_LIST,$userInfo['uid'],$params['w_id']);

        $result = $this->GoodsPackingService->batchSignByCacheKey([
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname'],
            'cache_key' => $cacheKey,
        ]);
        redis()->del([$cacheKey,$cacheKey.':uni',$cacheKey.':currentBox',$cacheUnSignKey]);
        if(empty($result)){
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }else{
            return $this->returnApi(ResponseCode::SERVER_ERROR, implode('|',$result));
        }
    }

    /**
     * 批量登记箱号
     * @RequestMapping(path="/goodsPacking/batchRegBox", methods="get,post")
     */
    public function batchRegBox(RequestInterface $request)
    {
        $params = $this->validate($request->all(),'batchRegBox');
        $userInfo = $this->getUserInfo();
        $cacheKey = CachePre::getKey(CachePre::BATCH_REG_LIST,$userInfo['uid']);
        $result = $this->GoodsPackingService->batchRegBoxNo([
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname'],
            'remark' => $params['remark'],
            'cache_key' => $cacheKey,
        ]);
        if($result){
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }else{
            return $this->returnApi(ResponseCode::SERVER_ERROR);
        }
    }

    /**
     * 获取临时存储签收数据
     * @RequestMapping(path="/goodsPacking/tempSignlist", methods="get,post")
     * @return mixed
     */
    public function tempSignlist(RequestInterface $request)
    {
        $params = $this->validate($request->all(),'tempSignlist');
        $userInfo = $this->getUserInfo();
        $page = intval($request->input('page',1));
        $limit = intval($request->input('limit',$this->pageLimit()));
        $cacheKey = CachePre::getKey(CachePre::BATCH_SIGN_LIST,$userInfo['uid'],$params['w_id']);
        $ret = redis()->lRange($cacheKey,($page - 1) * $limit,(($limit * $page) - 1));
        if(is_array($ret)){
            $list = [];
            $count = redis()->lLen($cacheKey);
            foreach ($ret as $item){
                $list[] = json_decode($item,true);
            }
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list, ['count' => $count ?? 0, 'limit' => $limit]);
    }

    /**
     * 获取临时存储签收数据
     * @RequestMapping(path="/goodsPacking/tempUnSignlist", methods="get,post")
     * @return mixed
     */
    public function tempUnSignlist(RequestInterface $request)
    {
        $params = $this->validate($request->all(),'tempUnSignlist');
        $userInfo = $this->getUserInfo();
        $limit = intval($request->input('limit',$this->pageLimit()));
        $cacheKey = CachePre::getKey(CachePre::BATCH_UNSIGN_LIST,$userInfo['uid'],$params['w_id']);
        $ret = redis()->hGetAll($cacheKey);
        if(is_array($ret)){
            $list = [];
            $count = redis()->hLen($cacheKey);
            foreach ($ret as $item){
                $list[] = json_decode($item,true);
            }
        }

        if (isset($params['is_export']) && $params['is_export'] == 1){
            if($list){
                try {
                    $url = exportToExcel(config('file_header.allot_sign_export'),$list,'调拨签收列表');
                }catch (\Exception $e){
                    throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
                }
            }else{
                throw new BusinessException('无数据，导出失败！');
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
        }

        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list, ['count' => $count ?? 0, 'limit' => $limit]);
    }

    /**
     * 获取临时存储登记数据
     * @RequestMapping(path="/goodsPacking/tempReglist", methods="get,post")
     * @return mixed
     */
    public function tempReglist(RequestInterface $request)
    {
        $userInfo = $this->getUserInfo();
        $page = intval($request->input('page',1));
        $delBoxNo = $request->input('box_no','');
        $limit = intval($request->input('limit',$this->pageLimit()));
        $cacheKey = CachePre::getKey(CachePre::BATCH_REG_LIST,$userInfo['uid']);
        $ret = redis()->lRange($cacheKey,($page - 1) * $limit,(($limit * $page) - 1));
        if(is_array($ret)){
            $list = [];
            $count = redis()->lLen($cacheKey);
            foreach ($ret as $item){
                $deItem = json_decode($item,true);
                if (!empty($delBoxNo) && $deItem['box_no'] == $delBoxNo){
                    redis()->lRem($cacheKey, $item, 1);
                    redis()->sRem($cacheKey.':box', $deItem['box_no']);
                    $count--;
                    continue;
                }
                $list[] = $deItem;
            }
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list, ['count' => $count ?? 0, 'limit' => $limit]);
    }

    /**
     * 下载临时存储签收数据
     * @RequestMapping(path="/goodsPacking/exportTempSignlist", methods="get,post")
     * @return mixed
     */
    public function exportTempSignlist(RequestInterface $request)
    {
        $params = $this->validate($request->all(),'tempSignlist');
        $userInfo = $this->getUserInfo();
        $cacheKey = CachePre::getKey(CachePre::BATCH_SIGN_LIST,$userInfo['uid'],$params['w_id']);
        $ret = redis()->lRange($cacheKey,0,-1);
        if(is_array($ret)){
            $list = [];
            foreach ($ret as $item){
                $list[] = json_decode($item,true);
            }
        }
        if($list){
            try {
                $url = exportToExcel(config('file_header.allot_sign_export'),$list,'调拨签收列表');
            }catch (\Exception $e){
                throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
            }
        }else{
            throw new BusinessException('无数据，导出失败！');
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }

    /**
     * 清空临时存储签收数据
     * @RequestMapping(path="/goodsPacking/clearTempSignlist", methods="get,post")
     * @return mixed
     */
    public function clearTempSignlist(RequestInterface $request)
    {
        $params = $this->validate($request->all(),'tempSignlist');
        $userInfo = $this->getUserInfo();
        $cacheKey = CachePre::getKey(CachePre::BATCH_SIGN_LIST,$userInfo['uid'],$params['w_id']);
        $unSignCacheKey = CachePre::getKey(CachePre::BATCH_UNSIGN_LIST,$userInfo['uid'],$params['w_id']);
        redis()->del($cacheKey);
        redis()->del($unSignCacheKey);
        redis()->del($cacheKey.':uni');
        redis()->del($cacheKey.':currentBox');
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
    }

    /**
     * 货品编码校验
     * @RequestMapping(path="/goodsPacking/signCode", methods="get,post")
     */
    public function signCode(RequestInterface $request)
    {
        $params = $this->validate($request->all(),'signCode');
        $params['check_status'] = 1;
        if(isset($params['box_no']) && !empty($params['box_no'])){
            $params['box_nos'] = [$params['box_no']];
            unset($params['box_no']);
        }
        $userInfo = $this->getUserInfo();
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        $data = $this->GoodsPackingService->signCode($params);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $data);
    }
    /**
     * 文件编码校验
     * @RequestMapping(path="/goodsPacking/fileCheck", methods="get,post")
     */
    public function fileCheck(RequestInterface $request)
    {
        $params = $this->validate($request->all(),'fileSign');
        $params['check_status'] = 1;
        $file = $request->file('file');
        $configTemplate = getTemplateInfo(TemplateSetService::TYPE_ALLOT_PACK_SIGN, $params['code_type']);
        // 读取excel数据
        $importData = readExcelByHeader($file, $configTemplate['original_data'],
            ['admin_id'=>'integer','num'=>'integer|min:1'],
            ['admin_id.integer'=>'员工号为数值','num.integer'=>'数量必须为数值','num.min'=>'数量必须大于1']);
        $params['code_list'] = $importData;
        $data = $this->GoodsPackingService->checkCode($params);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $data);
    }

    /**
     * 文件编码校验
     * @RequestMapping(path="/goodsPacking/fileSign", methods="get,post")
     */
    public function fileSign(RequestInterface $request)
    {
        $params = $this->validate($request->all(),'fileSign');
        $params['check_status'] = 1;
        $file = $request->file('file');
        $configTemplate = getTemplateInfo(TemplateSetService::TYPE_ALLOT_PACK_SIGN, $params['code_type']);
        // 读取excel数据
        $importData = readExcelByHeader($file, $configTemplate['original_data'],
            ['admin_id'=>'integer','num'=>'integer|min:1'],
            ['admin_id.integer'=>'员工号为数值','num.integer'=>'数量必须为数值','num.min'=>'数量必须大于1']);
        $params['code_list'] = $importData;
        $userInfo = $this->getUserInfo();
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        $data = $this->GoodsPackingService->signCode($params);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $data);
    }

    /**
     * 箱号质检
     * @RequestMapping(path="/goodsPacking/cancelBox", methods="get,post")
     */
    public function cancelBox(RequestInterface $request)
    {
        $params = $this->validate($request->all(),'cancel');
        $boxIds = explode(",", $params['box_id']);

        $data = $this->GoodsPackingService->cancelBox($boxIds);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $data);
    }

    private function export(array $data){

        $head = [
            'id' => '序号',
            'out_w_name' => '发货仓',
            'in_w_name' => '收货仓',
            'box_no' => '箱号',
            'task_type_name' => '来源',
            'source_no' => '单号',
            'goods_code' => '店内码/条形码',
            'barcode' => '条码',
            'type_name' => '类型',
            'brand_name' => '品牌',
            'cate_name' => '品类',
            'spu_no' => '货号',
            'size' => '规格',
            'num' => '数量',
            'pk_admin_name' => '装箱人',
        ];
        $fileName = '装箱明细-'.date('Y-m-d-H-i-s');
        $eData = [];
        if (!empty($data)) {
            foreach ($data as $k => $v) {
                array_push($eData,[
                    'id' => $k + 1,
                    'out_w_name' => $v['out_w_name'],
                    'in_w_name' => $v['in_w_name'],
                    'box_no' => $v['box_no'],
                    'task_type_name' => PublicCode::PK_TASK_TYPE_NAME[$v['task_type']] ?? '-',
                    'source_no' => $v['source_no'] ?? '-',
                    'goods_code' => $v['goods_code'],
                    'barcode' => "`".$v['barcode']??'',
                    'type_name' => PublicCode::GOODS_CODE_TYPE_NAME[$v['goods_code_type']] ?? '-',
                    'brand_name' => $v['brand_name'] ?? '-',
                    'cate_name' => $v['cate_name'] ?? '-',
                    'spu_no' => $v['spu_no'] ?? '-',
                    'size' => $v['spec_names'] ? join('，',$v['spec_names']) : '-',
                    'num' => $v['num'] ?? '0',
                    'pk_admin_name' => $v['pk_admin_name'] ?? '',
                ]);
            }
        }

        return exportToExcel($head,$eData,$fileName);
    }

    /**
     * 装箱列表
     * @RequestMapping(path="/goodsPacking/list", methods="get,post")
     */
    public function list(RequestInterface $request)
    {
        $params = $request -> all();
        var_dump('POST=1=',$params);
        $page = $params['page'] ?? [];
        $limit = $params['limit'] ?? [];
        $where = $params['where'] ?? [];
        if (isset($where['status']) && in_array((int)$where['status'],[0,1,2,3])) {
            $where['status'] = explode(',',$where['status']);
        }
        var_dump('where=2=',$where);

        // 处理从其他地方过来的请求
        $taskType = $where['task_type'] ?? 0;
        $sourceNo = $where['source_no'] ?? '';
        $lg_no = $params['lg_no'] ?? '';
        if ($request -> isMethod('POST')) {

            // 将get传参加入请求参数里
            if (!empty($taskType)) {
                array_push($where,['task_type'=>$taskType]);
            }
            if (!empty($sourceNo)) {
                array_push($where,['source_no'=>$sourceNo]);
            }
            if (!empty($lg_no)) {
                array_push($where,['lg_no'=>$lg_no]);
            }
            $authWIds = getAuthWIds();
            // 将来源单号相关的仓库id加入当前账户权限仓库
            if ( 1 == $taskType && !empty($sourceNo)) { //任务类型：1=调拨任务，2=退返任务
                // 处理从调拨模块过来的页面，需要调入和调出放都可以查看当前调拨单的仓库权限：获取调拨单号对应 调入和调出仓库id
                $allot = $this -> AllotService -> getAllotBySerialNo($sourceNo);
                if ($allot) {
                    // 追加权限之外的调拨单涉及的仓库id
                    array_push($authWIds,$allot['in_w_id']);
                    array_push($authWIds,$allot['out_w_id']);
                }
            }
            try {
                $ret = $this -> GoodsPackingService -> getGoodsPackingList($authWIds,(int)$page,(int)$limit,$where);
                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
            } catch (\Exception $exception) {
                $ret = $exception -> getMessage();
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
            }
        }
        logger()->debug('装箱列表请求参数',['params'=>$params]);

        return $this -> show("/goodsPacking/list",['params'=>$params]);
    }

    /**
     * 任务装箱列表
     * @RequestMapping(path="/goodsPacking/taskList", methods="get,post")
     */
    public function taskList(RequestInterface $request)
    {
        $sourceNo = $request->input('source_no','');
        if(!$sourceNo){
            throw new BusinessException('来源单号不能为空');
        }
        $authWIds = getAuthWIds();
        // 获取调拨单号对应 调入和调出仓库id
        $allot = $this -> AllotService -> getAllotBySerialNo($sourceNo);
        if ($allot) {
            // 追加权限之外的调拨单涉及的仓库id
            array_push($authWIds,$allot['in_w_id']);
            array_push($authWIds,$allot['out_w_id']);
        }

        $page = intval($request->input('page',1));
        $limit = intval($request->input('limit',30));

        $list = $this -> GoodsPackingService -> getGoodsPackingList($authWIds,$page,$limit,['source_no'=>$sourceNo]);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
    }

    /**
     * 上传装箱数据接口
     * @RequestMapping(path="/goodsPacking/uploadData", methods="get,post")
     * @throws \App\Exception\ValidateException
     */
    public function uploadData(RequestInterface $request)
    {
        // 将上传的盘点数据存入redis
        $params = $request -> all();
        $validator = validate()->make($params, [
            'task_type' => 'required|integer',
            'upload_type' => 'required|integer',
            'source_no' => 'required|string',
        ],[
            'task_type.required'=> '任务类型必填',
            'task_type.integer'=> '任务类型参数必须是正整数',
            'upload_type.required'=> '上传类型必选',
            'upload_type.integer'=> '上传类型参数必须是正整数',
            'source_no.required'=> '来源单号必填',
            'source_no.string'=> '来源单号必须是字符串',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        $userInfo = $this -> session -> get('userInfo');

        if (!$request->hasFile('file')) {
            return $this->returnApi(ErrorCode::REQUEST_FILE_ERROR, ErrorCode::getMessage(ErrorCode::REQUEST_FILE_ERROR));
        }
        $file = $request->file('file');
        $file -> getPathInfo();

        $txtData = [];
        // 店内码上传
        if ($params['upload_type'] == 1) {
            $tplKey = 'goods_packing_unique_code_txt';
            $metaData = getTemplateInfo($tplKey);
            $metaData = explode(',',$metaData['key']);
            $txtData = readTxt($file,$metaData);
        }
        // 条码上传
        if ($params['upload_type'] == 2) {
            $tplKey = 'goods_packing_barcode_txt';
            $metaData = getTemplateInfo($tplKey);
            $metaData = explode(',',$metaData['key']);
            $txtData = readTxt($file,$metaData);
        }

        $newData = [
            'pk_id' => $params['pk_id'] ?? 0,
            'task_type' => $params['task_type'],
            'source_no' => $params['source_no'],
            'goods_codes' => $txtData,
        ];

        if (empty($txtData)) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,'上传文件不能为空','');
        }

        // 根据店内码找出原货架信息组装数据
        try {
            $redis = redis();
            // 店内码上传
            $checkResult = [];
            if ($params['upload_type'] == 1) {
                $redisKey = $this -> _getRedisKey($userInfo['uid'],$params['task_type'],$params['gather_way'],$params['source_no'],'unique_code');

                // 先删除
                $eKey = $redisKey.self::E_DATA_SUFFIX;
                $normalKey = $redisKey.self::NORMAL_DATA_SUFFIX;
                $redis -> del($eKey);
                $redis -> del($normalKey);

                // 取出历史数据，合并，然后再一起校验
                $finalData = $this -> _mergeHistoryData($redisKey,'unique_code',$txtData);
                $newData['goods_codes'] = $finalData;
                $newData['redis_key'] = $redisKey;
                $newData['admin_id'] = $userInfo['uid'];
                $newData['admin_name'] = $userInfo['nickname'];
                $newData['gather_way'] = $params['gather_way'];
                $checkResult = $this -> _bulkCheckUniqueCodes($newData);
                // $redis -> set($redisKey,json_encode($checkResult),60 * 30);
                $this -> _saveCheckResultToHash($redisKey,'unique_code',$checkResult,self::GP_CACHE_EXPIRE);

                // 获取校验结果并返回
                // $ret = $this -> _getBulkCheckResult($userInfo['uid'],$params['gather_way'],$params['task_type'],$params['source_no'],$params['upload_type'],$params['export']);

            }
            // 条码上传
            if ($params['upload_type'] == 2) {
                $redisKey = $this -> _getRedisKey($userInfo['uid'],$params['task_type'],$params['gather_way'],$params['source_no'],'barcode');

                // 先删除
                $eKey = $redisKey.self::E_DATA_SUFFIX;
                $normalKey = $redisKey.self::NORMAL_DATA_SUFFIX;
                $redis -> del($eKey);
                $redis -> del($normalKey);

                $finalData = $this -> _mergeHistoryData($redisKey,'barcode',$txtData,$params['upload_type']);
                $newData['goods_codes'] = $finalData;
                $checkResult = $this -> _bulkCheckBarcode($newData);
                //$redis -> set($redisKey,json_encode($handledData),60 * 30);
                $this -> _saveCheckResultToHash($redisKey,'barcode',$checkResult,self::GP_CACHE_EXPIRE);

                // 获取校验结果并返回
                // $ret = $this -> _getBulkCheckResult($userInfo['uid'],$params['gather_way'],$params['task_type'],$params['source_no'],$params['upload_type'],$params['export']);

            }
            $ret = [
                'e_data' => array_values($checkResult['e_data']),
                'e_num' => $checkResult['e_num'],
                'normal_data' => array_values($checkResult['normal_data']),
                'normal_num' => $checkResult['normal_num'],
                'total' => $checkResult['e_num']+$checkResult['normal_num'],
                'normal_summary' => $this -> _getSummary($checkResult['normal_data'],$params['gather_way'],$params['upload_type']) // _getSummary($normalData,$gather_way,$upload_type=0)
            ];
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$ret);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }

    }

    // 根据店内码找出其所在箱号
    private function searchBoxNoByUnique($data,$uniqueCode)
    {
        $boxNos = [];
        foreach ($data as $k => $v) {
            if ($uniqueCode == $v['unique_code']) {
                array_push($boxNos,$v['box_no']);
            }
        }
        return $boxNos;

    }

    // 根据箱号找出其对应所在位置的索引
    private function getBoxIndex($data,$boxNo)
    {
        foreach ($data as $k => $v) {
            if ($boxNo == $v['box_no']) {
                return $k;
            }
        }
        return false;
    }

    /**
     * 任务详情 - 从redis获取上传的盘点数据列表
     * @RequestMapping(path="/goodsPacking/getUploadData", methods="get,post")
     */
    public function getUploadData(RequestInterface $request)
    {
        // 将上传的盘点数据存入redis
        $userInfo = $this -> session -> get('userInfo');
        try {
            $uniqueKey = $userInfo['uid'].'_goods_packing_data_unique_code';
            $result = redis() -> get($uniqueKey);
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",json_decode($result,true));
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 对扫描条码并提交的数据进行校验并返回校验结果
     * @RequestMapping(path="/goodsPacking/saveScannedBarcode", methods="get,post")
     */
    public function saveScannedBarcode(RequestInterface $request)
    {
        // 将上传的盘点数据存入redis
        $userInfo = $this -> session -> get('userInfo');
        $key = $userInfo['uid'].'_scan_barcode';
        $counterKey = $userInfo['uid'].'_counter';
        $params = $request -> all();
        var_dump('params==',$params);

        $barcode = $params['goods_code'] ?? '';
        $num = $params['num'] ?? 0;
        $taskType = $params['task_type'] ?? 0;
        $sourceNo = $params['source_no'] ?? '';

        //redis() -> del($key);
        $couterNum = $this -> RedisService -> counter($counterKey);
        $exception = '<span style="color:green">正确</span>';
        $eStatus = 1; // 0异常，1正常
        $return = [
            'id' => $couterNum,
            'barcode' => $barcode,
            'num'=>$num,
            'exception'=>$exception,
            'e_status'=>$eStatus
        ];

        // 1、校验条码是否存在
        $checkRes = $this -> SkuService -> checkSpuBarcode([$barcode],false);
        var_dump('条码是否存在==',$checkRes);

        if (!empty($checkRes['not_exists'])) {
            $exception = '<span style="color:#ff0000">条码不存在</span>';
            $eStatus = 0;
            $return['exception'] = $exception;
            $return['e_status'] = $eStatus;
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$return);
        }

        if (1 == $taskType) { // 调拨任务，去查是否属于此次调拨任务
            $checkAllotRes = $this -> AllotService -> checkIfBarcodeInAllotBySerialNo($sourceNo,[$barcode]);
            if (!empty($checkAllotRes['not_exists'])) {
                $exception = '<span style="color:red">条码不属于此批次</span>';
                $eStatus = 0;
                $return['exception'] = $exception;
                $return['e_status'] = $eStatus;
                return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$return);
            }

            // 校验：条码数量是否超出调拨单任务数
            if (!empty($checkAllotRes['exists'])) {
                // 将有效条码数量与 可出库数量比较
                // 单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
                // 商品码类型：1=店内码，2=条码
                var_dump('调拨taskType===',$taskType);
                $OutStockNum = $this -> OutStoreService -> getNotOutStoreNum($sourceNo,(int)$taskType,$barcode,2);
                var_dump('OutStockNum1===',$OutStockNum);
                var_dump('num1==',$num);
                if ($num > $OutStockNum) {
                    $exception = '<span style="color:red">条码数量超出任务数量</span>';
                    $eStatus = 0;
                    $return['exception'] = $exception;
                    $return['e_status'] = $eStatus;
                    return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$return);
                }
            }
        }

        if (2 == $taskType) { // 退返任务，去查是否属于此次退返任务
            $checkBackOrderRes = $this -> BackOrderService -> checkIfBarcodeInBackOrderBySerialNo($sourceNo,[$barcode]);
            var_dump('checkBackOrderRes===',$checkBackOrderRes);
            if (!empty($checkBackOrderRes['not_exists'])) { // 条形码不属于此次任务的，异常信息写入
                var_dump('not belong to this task===');
                $exception = '<span style="color:red">条码不属于此批次</span>';
                $eStatus = 0;
                $return['exception'] = $exception;
                $return['e_status'] = $eStatus;
                return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$return);
            }
            // 下一步校验：条码数量是否超出退返单任务数
            if (!empty($checkBackOrderRes['exists'])) {
                var_dump('belong to this task===');
                // 将有效条码数量与 可出库数量比较
                // 单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
                // 商品码类型：1=店内码，2=条码
                var_dump('退返taskType===',$taskType);
                $OutStockNum = $this -> OutStoreService -> getNotOutStoreNum($sourceNo,(int)$taskType,$barcode,2);
                var_dump('OutStockNum2===',$OutStockNum);
                var_dump('num2==',$num);
                if ($num > $OutStockNum) {
                    $exception = '<span style="color:red">条码数量超出任务数量</span>';
                    $eStatus = 0;
                    $return['exception'] = $exception;
                    $return['e_status'] = $eStatus;
                    return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$return);
                }

            }
        }


        // 2、保存到redis
        // 先取出保存的数据
        //$savedBarcode = $this -> getSavedBarcodeAll();
        // 追加
        //array_push($savedBarcode,$newData);
        //var_dump('savedBarcode==',$savedBarcode);
        // 保存
        //$this -> HashService -> saveDataToHash($key,$savedBarcode,60*30,true);

        return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$return);


    }

    /**
     * 导入装箱条码和数量，进行校验并返回校验结果
     * @RequestMapping(path="/goodsPacking/uploadScannedBarcode", methods="get,post")
     */
    public function uploadScannedBarcode(RequestInterface $request){
        $params = $request -> all();

        $userInfo = $this -> session -> get('userInfo');
        $counterKey = $userInfo['uid'].'_counter';

        $validator = validate()->make($params, [
            'task_type' => 'required|integer',
            'source_no' => 'required|string',
        ],[
            'task_type.required'=> '任务类型必填',
            'task_type.integer'=> '任务类型参数必须是正整数',
            'source_no.required'=> '来源单号必填',
            'source_no.string'=> '来源单号必须是字符串'
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        $return = [];
        $taskType = $params['task_type'] ?? 0;
        $sourceNo = $params['source_no'] ?? '';

        if (!$request->hasFile('file')) {
            return $this->returnApi(ErrorCode::REQUEST_FILE_ERROR, ErrorCode::getMessage(ErrorCode::REQUEST_FILE_ERROR));
        }

        //获取数据
        $file = $request->file('file');
        $file -> getPathInfo();
        $tplKey = 'goods_packing_barcode';
        $metaData = getTemplateInfo($tplKey);
        $metaData = explode(',',$metaData['key']);
        $txtData = readTxt($file,$metaData);

        //条形码
        $barcodes = array_column($txtData, 'barcode');

        //校验条码是否重复
        $unique_barcode = array_unique($barcodes);#获取去掉重复数据的数组
        $repeat_arr = array_diff_assoc($barcodes, $unique_barcode);#获取重复数据的数组
        if (!empty($repeat_arr)){
            foreach ($txtData as $txt_item) {
                if (in_array($txt_item['barcode'], $repeat_arr)) {
                    $return[] = [
                        'id' => $txt_item['row_num'],
                        'barcode' => $txt_item['barcode'],
                        'num'=>$txt_item['nums'],
                        'exception'=>'<span style="color:red">条形码重复</span>',
                        'e_status'=>0
                    ];
                }
            }
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$return);
        }

        //条形码map
        $barcode_map = array_combine(array_column($txtData, 'barcode'), array_values($txtData));

        //校验条码是否存在
        $checkRes = $this -> SkuService -> checkSpuBarcode($barcodes,false);
        if (!empty($checkRes['not_exists'])) {
            foreach ($txtData as $row) {
                if (in_array($row['barcode'], $checkRes['not_exists'])){
                    $exception = '<span style="color:#ff0000">条码不存在</span>';
                    $eStatus = 0;
                }else{
                    continue;
                }
                $return[] = [
                    'id' => $row['row_num'],
                    'barcode' => $row['barcode'],
                    'num'=>$row['nums'],
                    'exception'=>$exception,
                    'e_status'=>$eStatus
                ];
            }
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$return);
        }

        // 调拨任务，去查是否属于此次调拨任务
        if (1 == $taskType) {
            $checkAllotRes = $this -> AllotService -> checkIfBarcodeInAllotBySerialNo($sourceNo,$barcodes);
            if (!empty($checkAllotRes['not_exists'])) {
                foreach ($checkAllotRes['not_exists'] as $not_exist_barcode) {
                    $return[] = [
                        'id' => $barcode_map[$not_exist_barcode]['row_num'],
                        'barcode' => $not_exist_barcode,
                        'num'=>$barcode_map[$not_exist_barcode]['nums'],
                        'exception'=>'<span style="color:red">条码不属于此批次</span>',
                        'e_status'=>0
                    ];
                }
                return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$return);
            }

            // 校验：条码数量是否超出调拨单任务数
            if (!empty($checkAllotRes['exists'])) {
                $outStockNumMap = $this->OutStoreService->getNotOutStoreBarcodeNum($sourceNo,23,$checkAllotRes['exists'],2);
                // 将有效条码数量与 可出库数量比较
                // 单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
                // 商品码类型：1=店内码，2=条码
                foreach ($checkAllotRes['exists'] as $exist_barcode){
                    if (!isset($outStockNumMap[$exist_barcode])){
                        $return[] = [
                            'id' => $barcode_map[$exist_barcode]['row_num'],
                            'barcode' => $barcode_map[$exist_barcode]['barcode'],
                            'num'=>$barcode_map[$exist_barcode]['nums'],
                            'exception'=>'<span style="color:red">出库任务中不存在此条码</span>',
                            'e_status'=>0
                        ];
                    }elseif ($barcode_map[$exist_barcode]['nums'] > $outStockNumMap[$exist_barcode]) {
                        $return[] = [
                            'id' => $barcode_map[$exist_barcode]['row_num'],
                            'barcode' => $barcode_map[$exist_barcode]['barcode'],
                            'num'=>$barcode_map[$exist_barcode]['nums'],
                            'exception'=>'<span style="color:red">条码数量超出任务数量</span>',
                            'e_status'=>0
                        ];
                    }
                }
                if (!empty($return)){
                    return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$return);
                }
            }
        }elseif (2 == $taskType) { #退返任务，去查是否属于此次退返任务
            $checkBackOrderRes = $this -> BackOrderService -> checkIfBarcodeInBackOrderBySerialNo($sourceNo,$barcodes);
            if (!empty($checkBackOrderRes['not_exists'])) { #条形码不属于此次任务的，异常信息写入
                foreach ($checkBackOrderRes['not_exists'] as $not_exist_barcode) {
                    $return[] = [
                        'id' => $barcode_map[$not_exist_barcode]['row_num'],
                        'barcode' => $not_exist_barcode,
                        'num'=>$barcode_map[$not_exist_barcode]['nums'],
                        'exception'=>'<span style="color:red">条码不属于此批次</span>',
                        'e_status'=>0
                    ];
                }
                return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$return);
            }
            // 下一步校验：条码数量是否超出退返单任务数
            if (!empty($checkBackOrderRes['exists'])) {
                $outStockNumMap = $this->OutStoreService->getNotOutStoreBarcodeNum($sourceNo,22,$checkBackOrderRes['exists'],2);
                // 将有效条码数量与 可出库数量比较
                // 单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
                // 商品码类型：1=店内码，2=条码
                foreach ($checkBackOrderRes['exists'] as $exist_barcode){
                    if (!isset($outStockNumMap[$exist_barcode])){
                        $return[] = [
                            'id' => $barcode_map[$exist_barcode]['row_num'],
                            'barcode' => $barcode_map[$exist_barcode]['barcode'],
                            'num'=>$barcode_map[$exist_barcode]['nums'],
                            'exception'=>'<span style="color:red">出库任务中不存在此条码</span>',
                            'e_status'=>0
                        ];
                    }elseif ($barcode_map[$exist_barcode]['nums'] > $outStockNumMap[$exist_barcode]) {
                        $return[] = [
                            'id' => $barcode_map[$exist_barcode]['row_num'],
                            'barcode' => $barcode_map[$exist_barcode]['barcode'],
                            'num'=>$barcode_map[$exist_barcode]['nums'],
                            'exception'=>'<span style="color:red">条码数量超出任务数量</span>',
                            'e_status'=>0
                        ];
                    }
                }
                if (!empty($return)){
                    return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$return);
                }
            }
        }

        foreach ($txtData as $txt_item){
            $couterNum = $this -> RedisService -> counter($counterKey);//添加行号，兼容扫描
            $return[] = [
                'id' => $txt_item['row_num'],
                'barcode' => $txt_item['barcode'],
                'num'=>$txt_item['nums'],
                'exception'=>'<span style="color:green">正确</span>',
                'e_status'=>1
            ];
        }

        return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$return);
    }


    /**
     * 获取保存在redis的已扫描条码
     * @RequestMapping(path="/goodsPacking/getSavedBarcodesList", methods="get,post")
     */
    /*public function getSavedBarcodesList(RequestInterface $request)
    {
        $params = $request -> all();
        $userInfo = $this -> session -> get('userInfo');
        $page = $params['page'];
        $limit = $params['limit'];
        $key = $userInfo['uid'].'_scan_barcode';

        $bocodes = $this -> HashService -> getDataListFromHash($key,(int)$page,(int)$limit);
        var_dump('$bocodes==',$bocodes);
        return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$bocodes);
    }*/

    /**
     * 获取保存在redis的已扫描条码
     * @RequestMapping(path="/goodsPacking/getSavedBarcodesAll", methods="get,post")
     */
    /*public function getSavedBarcodesAll(RequestInterface $request)
    {
        $params = $request -> all();
        $userInfo = $this -> session -> get('userInfo');
        $key = $userInfo['uid'].'_scan_barcode';

        $barcodes = $this -> HashService -> getDataAllFromHash($key);
        var_dump('$bocodes222==',$barcodes);

        $result = [
            'total' => count($barcodes),
            'data' => $barcodes,
        ];
        return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
    }*/

    /*private function getSavedBarcodeAll()
    {
        $userInfo = $this -> session -> get('userInfo');
        $key = $userInfo['uid'].'_scan_barcode';

        $barcodes = $this -> HashService -> getDataAllFromHash($key);
        return $barcodes;
    }*/

    /**
     * 校验箱号是否存在
     * @RequestMapping(path="/goodsPacking/checkBoxNos", methods="get,post")
     */
    public function checkBoxNos(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'box_no' => 'required',
        ]);
        if ($validator->fails()) {
            var_dump( $validator->errors()->first() );
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $boxNo = $params['box_no'];
        try {
            $result = $this -> GoodsPackingService -> checkBoxNos([$boxNo]);
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }


    }


    /**
     * 校验箱号是否存在
     * @RequestMapping(path="/goodsPacking/checkTaskBox", methods="get,post")
     */
    public function checkTaskBox(RequestInterface $request)
    {
        $boxNo = $request->input('box_no','');
        if (!$boxNo) {
            throw new BusinessException('箱号不能为空');
        }
        try {
            $result = $this -> GoodsPackingService -> checkTaskBox($boxNo);
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }


    /**
     * 装箱 作废
     * @RequestMapping(path="/goodsPacking/cancel", methods="get,post")
     */
    public function cancel()
    {
        $id = $this->request->input('id');
        if (!$id) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
        }

        $userInfo = $this->session->get('userInfo');

        try {
            $this->GoodsPackingService->cancelGoodsPacking((int)$id, [
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname']
            ]);
        } catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }

        return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
    }

    /**
     * 装箱 称重
     * @RequestMapping(path="/goodsPacking/weighing", methods="get,post")
     * @param RequestInterface $request
     */
    public function weighing(RequestInterface $request)
    {
        $params = $request -> all();

        $validator = validate()->make($params, [
            'id' => 'required|integer',
            'weight' => 'required',
        ],[
            'id.required'=> '装箱id必填',
            'id.integer'=> '装箱id必须是数字',
            'weight.required'=> '装箱总重数必填',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        $userInfo = $this->session->get('userInfo');
        $adminInfo = [
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname']
        ];

        try {
            $this -> GoodsPackingService -> weighing($params['id'],$params['weight'],$adminInfo);
        } catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }

        return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
    }

    /**
     * 装箱 记录
     * @RequestMapping(path="/goodsPacking/getLatestWeightLog", methods="get,post")
     * @param RequestInterface $request
     */
    public function getLatestWeightLog(RequestInterface $request)
    {
        $params = $request -> all();

        $validator = validate()->make($params, [
            'id' => 'required|integer',
        ],[
            'id.required'=> '装箱id必填',
            'id.integer'=> '装箱id必须是数字',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        try {
            $result = $this -> GoodsPackingService -> getLatestWeightLog($params['id']);
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
    }

    /**
     * 装箱明细批量下载
     * @RequestMapping(path="/goodsPacking/bulkExportPackingBills", methods="get,post")
     * @param RequestInterface $request
     * @return array
     */
    public function bulkExportPackingBills(RequestInterface $request)
    {
        $params = $request -> all();

        $validator = validate()->make($params, [
            'ids' => 'required|array',
        ],[
            'ids.required'=> '装箱id集合必传',
            'ids.array'=> '装箱id必须是数组',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        try {
            $billListData = $this -> GoodsPackingService -> getPackingBillsByPkIds($params['ids']);
            $url = '';
            if ($billListData) {
                $combinations = join(',',array_column($billListData,'combination'));
                // 追加规格
                $billListData = $this -> _appendSpecNames($billListData,$combinations);
                // 导出
                $url = $this -> export($billListData);
            }

            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",['url' => $url]);
        } catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
    }


    /**
     * 批量校验扫描的条码
     * @RequestMapping(path="/goodsPacking/bulkCheckBarcode", methods="get,post")
     */
    public function bulkCheckBarcode(RequestInterface $request){
        // 将上传的盘点数据存入redis
        $params = $request -> all();
        $userInfo = $this -> session -> get('userInfo');
        $validator = validate()->make($params, [
            'gather_way' => 'required|integer',
            'task_type' => 'required|integer',
            'source_no' => 'required|string',
            'box_no' => 'required|string',
            'goods_codes' => 'required|array',
        ],[
            'task_type.required'=> '任务类型必填',
            'task_type.integer'=> '任务类型参数必须是正整数',
            'source_no.required'=> '来源单号必填',
            'source_no.string'=> '来源单号必须是字符串',
            'box_no.required'=> '箱号必填',
            'box_no.string'=> '箱号必须是字符串',
            'goods_codes.required'=> '条码必填',
            'goods_codes.array'=> '条码必须是数组',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $redisKey = $this -> _getRedisKey($userInfo['uid'],$params['task_type'],$params['gather_way'],$params['source_no'],'barcode');

        // 先删除
        $redis = redis();
        $redis -> del($redisKey);

        $goodsCodes = [];
        foreach ($params['goods_codes'] as $goods_code) {
            $item = [
                'box_no' => $params['box_no'],
                'barcode' => $goods_code['barcode'],
                'num' => $goods_code['num'],
                'admin_id' => $userInfo['uid'],
            ];
            // 如果条码数手动修改过，则已修改后的为准，覆盖历史重的同条码数量
            if (isset($goods_code['has_modified'])) {
                $item['has_modified'] = $goods_code['has_modified'];
            }
            array_push($goodsCodes,$item);
        }

        $newData = [
            'pk_id' => $params['pk_id'] ?? 0,
            'task_type' => $params['task_type'],
            'source_no' => $params['source_no'],
            'goods_codes' => $goodsCodes,
        ];

        try {
            // 取出历史数据，合并，然后再一起校验
            $finalData = $this -> _mergeHistoryData($redisKey,'barcode',$goodsCodes);
            $newData['goods_codes'] = $finalData;
            // 根据店内码找出原货架信息组装数据
            $checkResult = $this -> _bulkCheckBarcode($newData);
            // $redis -> set($redisKey,json_encode($checkResult),60 * 30);
            $this -> _saveCheckResultToHash($redisKey,'barcode',$checkResult,self::GP_CACHE_EXPIRE);

            $ret = [
                'e_data' => array_values($checkResult['e_data']),
                'e_num' => $checkResult['e_num'],
                'normal_data' => array_values($checkResult['normal_data']),
                'normal_num' => $checkResult['normal_num'],
                'total' => $checkResult['e_num']+$checkResult['normal_num'],
            ];

            // 获取校验结果并返回
            //$ret = $this -> _getBulkCheckResult($userInfo['uid'],$params['gather_way'],$params['task_type'],$params['source_no'],$params['upload_type'],$params['export']);

            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$ret);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }
    }

    // 批量校验条码
    private function _bulkCheckBarcode($data) {
        $taskType = $data['task_type'] ?? 0;
        $sourceNo = $data['source_no'] ?? '';
        $goodsCodes = $data['goods_codes'] ?? [];

        if (empty($goodsCodes) || empty($taskType) || empty($sourceNo)) throw new BusinessException('参数错误');

        // 每个数据记录其原始key，便于后面校验查找原始位置
        foreach ($goodsCodes as $k => &$v) {
            $v['original_key'] = $k;
        }

        $c = collect($goodsCodes);
        // 集合：将数据按照箱号分组
        $goodsCodesBoxGroup = $c -> groupBy('box_no');

        // 集合：按照条码分组
        $goodsCodesGroup = $c -> groupBy('barcode');

        // 去重后的条码
        $barcodes = array_unique(array_column($goodsCodes,'barcode'));
        try {
            // 1. 校验单号
            $this -> _checkSourceNo($taskType,$sourceNo);

            // 2. 校验箱号是否合法
            $goodsCodes = $this -> _checkBoxNo($goodsCodes);

            // 3. 校验箱号是否已存在
            // pk_id=0，新增数据，需要校验箱号重复
            if (isset($data['pk_id']) && 0 === (int)$data['pk_id']) {
                $goodsCodes = $this -> _checkBoxNoExists($goodsCodes,$goodsCodesBoxGroup);
            }

            $goodsCodes = $this -> _barcodeCoreCheck($taskType,$sourceNo,$goodsCodes,$barcodes,$goodsCodesGroup);

            // 3. 校验条码是否重复
//            $goodsCodesBGroup = collect($goodsCodes) -> groupBy(function ($item) {
//                return $item['box_no'].'-'.$item['barcode'];
//            });
//            $goodsCodes = $this -> _checkIfGoodsCodeRepeat($goodsCodes,$goodsCodesBGroup);

            // 7. 最终数据整理
            return  $this -> _handleFinalDataForBarcode($goodsCodes);

        } catch (\Exception $exception) {
            throw new BusinessException($exception -> getMessage());
        }
    }

    // 条码核心校验部分
    private function _barcodeCoreCheck($taskType,$sourceNo,$goodsCodes,$barcodes,$goodsCodesGroup) {

        // 4. 校验条码是否存在
        $t1 = microtime(true);
        $goodsCodes = $this -> _checkIfBarcodeExists($goodsCodes,$barcodes,$goodsCodesGroup);
        $t2 = microtime(true);
        $diff = number_format($t2-$t1, 3, '.', '');
        $consume_total = $diff." s";
        var_dump('条码核心校验部分-校验条码是否存在-代码耗时：'.$consume_total);

        // 5. 校验条码是否属于此批次
        $t1 = microtime(true);
        $goodsCodes = $this -> _checkIfThisBatch($taskType,$sourceNo,$goodsCodes,$barcodes,$goodsCodesGroup);
        $t2 = microtime(true);
        $diff = number_format($t2-$t1, 3, '.', '');
        $consume_total = $diff." s";
        var_dump('条码核心校验部分-校验条码是否属于此批次-代码耗时：'.$consume_total);

        return $goodsCodes;
    }

    private function _checkIfBarcodeExists($goodsCodes,$barcodes,$goodsCodesGroup) {
        //校验条码是否存在
        $checkRes = $this -> SkuService -> checkSpuBarcode($barcodes,false);
        if (!empty($checkRes['not_exists'])) {
            foreach ($checkRes['not_exists'] as $barcode) {
                $groupData = $goodsCodesGroup -> get($barcode);
                if ($groupData) {
                    foreach ($groupData as $groupDatum) {
                        $goodsCodes[$groupDatum['original_key']]['exception'][] = '条码不存在';
                    }
                }
            }

//            foreach ($goodsCodes as $k => &$goodsCode) {
//                if (in_array($goodsCode['barcode'],$checkRes['not_exists'])) {
//                    $goodsCodes[$goodsCode['original_key']]['exception'][] = '条码不存在';
//                }
//            }
        }
        return $goodsCodes;
    }

    private function _checkIfThisBatch($taskType,$sourceNo,$goodsCodes,$barcodes,$goodsCodesGroup) {
        // 调拨任务，去查是否属于此次调拨任务
        if (1 == $taskType) {
            $checkAllotRes = $this -> AllotService -> checkIfBarcodeInAllotBySerialNo($sourceNo, $barcodes);
            logger() -> debug('checkAllotRes====',[$checkAllotRes]);
            if (!empty($checkAllotRes['not_exists'])) {
                // 不属于此批次，异常弹框提示，终止流程 - 20230215 -yj
                throw new BusinessException(sprintf('条码【%s】错下架，请先移走此条码然后再扫描或导入！',join(',',$checkAllotRes['not_exists'])));

//                foreach ($checkAllotRes['not_exists'] as $not_exist_barcode) {
//                    $groupData = $goodsCodesGroup -> get($not_exist_barcode);
//                    if ($groupData) {
//                        foreach ($groupData as $groupDatum) {
//                            $goodsCodes[$groupDatum['original_key']]['exception'][] = '条码不属于此批次';
//                        }
//                    }
//                }
            }

            // 校验：条码数量是否超出调拨单任务数
            if (!empty($checkAllotRes['exists'])) {
                $outStockNumMap = $this->OutStoreService->getNotOutStoreNumByBatch($sourceNo, (int)$taskType, $checkAllotRes['exists'], 2);
                // $outStockNumMap = $this->OutStoreService->getNotOutStoreBarcodeNum($sourceNo, 23, $checkAllotRes['exists'], 2);
                // 将有效条码数量与 可出库数量比较
                // 单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
                // 商品码类型：1=店内码，2=条码
                logger() -> debug('outStockNumMap==========',[$outStockNumMap]);
                logger() -> debug('goodsCodesGroup==========',[$goodsCodesGroup]);
                foreach ($checkAllotRes['exists'] as $exist_barcode) {
                    $groupData = $goodsCodesGroup -> get($exist_barcode);
                    logger() -> debug('groupData==========',[$groupData]);
                    // 条码总数与剩余可装箱条码数相比，是否超出任务数，若超出，则每个记录都要标记异常 - 20220930
                    // 1. 取出总条码数
                    // 2. 条码总数与剩余可装箱条码数相比
                    // 3. 超出的条码，给条码每个对应记录标记异常
                    if (!isset($outStockNumMap[$exist_barcode])) {
                        if ($groupData) {
                            foreach ($groupData as $groupDatum) {
                                $goodsCodes[$groupDatum['original_key']]['exception'][] = '出库任务中不存在此条码';
                            }
                        }
                    } else {
                        // 上传条码总数
                        $groupDataNumTotal = collect($groupData) -> sum('num');
                        // 当前条码剩余可装箱数量
                        $canPackTotal = $outStockNumMap[$exist_barcode];
                        if ($groupData) {
                            // 总数超出，则每个条码都标记异常
                            if ($groupDataNumTotal > $canPackTotal) {
                                foreach ($groupData as $groupDatum) {
                                    $goodsCodes[$groupDatum['original_key']]['exception'][] = '条码总数超出当前任务可装箱数量';
                                }
                            }
                        }
                    }
                }
            }
        }

        // 退返任务，去查是否属于此次退返任务
        if (2 == $taskType) {
            $checkBackOrderRes = $this -> BackOrderService -> checkIfBarcodeInBackOrderBySerialNo($sourceNo,$barcodes);
            if (!empty($checkBackOrderRes['not_exists'])) { #条形码不属于此次任务的，异常信息写入
                // 不属于此批次，异常弹框提示，终止流程 - 20230215 -yj
                throw new BusinessException(sprintf('条码【%s】错下架，请先移走此条码然后再扫描或导入！',join(',',$checkBackOrderRes['not_exists'])));

//                foreach ($checkBackOrderRes['not_exists'] as $not_exist_barcode) {
//                    $groupData = $goodsCodesGroup -> get($not_exist_barcode);
//                    if ($groupData) {
//                        foreach ($groupData as $groupDatum) {
//                            $goodsCodes[$groupDatum['original_key']]['exception'][] = '条码不属于此批次';
//                        }
//                    }
//                }
            }
            // 下一步校验：条码数量是否超出退返单任务数
            if (!empty($checkBackOrderRes['exists'])) {
                $outStockNumMap = $this->OutStoreService->getNotOutStoreNumByBatch($sourceNo, (int)$taskType,$checkBackOrderRes['exists'], 2);
                logger() -> debug('条码数量是否超出退返单任务数',[$outStockNumMap]);
                //$outStockNumMap = $this->OutStoreService->getNotOutStoreBarcodeNum($sourceNo,22,$checkBackOrderRes['exists'],2);
                // 将有效条码数量与 可出库数量比较
                // 单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
                // 商品码类型：1=店内码，2=条码
                foreach ($checkBackOrderRes['exists'] as $exist_barcode){
                    $groupData = $goodsCodesGroup -> get($exist_barcode);
                    $groupDataNumTotal = collect($groupData) -> sum('num');
                    if ($groupData) {
                        foreach ($groupData as $groupDatum) {
                            if (!isset($outStockNumMap[$exist_barcode])){
                                $goodsCodes[$groupDatum['original_key']]['exception'][] = '出库任务中不存在此条码';
                            }
                            // 总数超出，则每个条码都标记异常
                            if ($groupDataNumTotal > $outStockNumMap[$exist_barcode]) {
                                $goodsCodes[$groupDatum['original_key']]['exception'][] = '条码总数超出当前任务可装箱数量';
                            }
                        }
                    }
                }
            }
        }

        return $goodsCodes;
    }

    /**
     * 批量校验店内码
     * @RequestMapping(path="/goodsPacking/bulkCheckUniqueCodes", methods="get,post")
     */
    public function bulkCheckUniqueCodes(RequestInterface $request)
    {
        // 将上传的盘点数据存入redis
        $params = $request -> all();
        $userInfo = $this -> session -> get('userInfo');
        $validator = validate()->make($params, [
            'gather_way' => 'required|integer',
            'task_type' => 'required|integer',
            'source_no' => 'required|string',
            'box_no' => 'required|string',
            'goods_codes' => 'required|string',
        ],[
            'task_type.required'=> '任务类型必填',
            'task_type.integer'=> '任务类型参数必须是正整数',
            'source_no.required'=> '来源单号必填',
            'source_no.string'=> '来源单号必须是字符串',
            'box_no.required'=> '箱号必填',
            'box_no.string'=> '箱号必须是字符串',
            'goods_codes.required'=> '店内码必填',
            'goods_codes.string'=> '店内码必须是json字符串',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
//        $uniqueKey = $userInfo['uid'].'_goods_packing_data_unique_code'; // 店内码盘点
        $redisKey = $this -> _getRedisKey($userInfo['uid'],$params['task_type'],$params['gather_way'],$params['source_no'],'unique_code');

        if (isset($params['goods_codes']) && !empty($params['goods_codes'])) {
            $params['goods_codes'] = json_decode($params['goods_codes'],true);
        }
        // 先删除
        $redis = redis();
        // $redis -> del($redisKey);

        $goodsCodes = [];
        foreach ($params['goods_codes'] as $goods_code) {
            array_push($goodsCodes,[
                'box_no' => $params['box_no'],
                'unique_code' => $goods_code['unique_code'],
                'admin_id' => $userInfo['uid'],
            ]);
        }

        $newData = [
            'pk_id' => $params['pk_id'] ?? 0,
            'task_type' => $params['task_type'],
            'source_no' => $params['source_no'],
            'goods_codes' => $goodsCodes,
        ];

        try {
            // 取出历史数据，合并，然后再一起校验
            $finalData = $this -> _mergeHistoryData($redisKey,'unique_code',$goodsCodes);
            $newData['goods_codes'] = $finalData;
            // 根据店内码找出原货架信息组装数据
            $newData['redis_key'] = $redisKey;
            $newData['admin_id'] = $userInfo['uid'];
            $newData['admin_name'] = $userInfo['nickname'];
            $newData['gather_way'] = $params['gather_way'];
            $checkResult = $this -> _bulkCheckUniqueCodes($newData);
            //$redis -> set($redisKey,json_encode($checkResult),60 * 30);
            $this -> _saveCheckResultToHash($redisKey,'unique_code',$checkResult,self::GP_CACHE_EXPIRE);

            $ret = [
                'e_data' => array_values($checkResult['e_data']),
                'e_num' => $checkResult['e_num'],
                'normal_data' => array_values($checkResult['normal_data']),
                'normal_num' => $checkResult['normal_num'],
                'total' => $checkResult['e_num']+$checkResult['normal_num'],
            ];

            // 获取校验结果并返回
            //$ret = $this -> _getBulkCheckResult($userInfo['uid'],$params['gather_way'],$params['task_type'],$params['source_no'],$params['upload_type'],$params['export']);

            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$ret);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }
    }

    /**
     * 根据店内码找出原货架信息组装数据
     * @param $data
     */
    private function _bulkCheckUniqueCodes($data)
    {
        $taskType = $data['task_type'] ?? 0;
        $sourceNo = $data['source_no'] ?? '';
        $goodsCodes = $data['goods_codes'] ?? [];

        if (empty($goodsCodes) || empty($taskType) || empty($sourceNo)) throw new BusinessException('参数错误');

        // 装箱店内码异常类型

        try {
            // 1. 校验单号
            $this -> _checkSourceNo($taskType,$sourceNo);

            // 2. 校验箱号是否合法
            $goodsCodes = $this -> _checkBoxNo($goodsCodes);

            $c = collect($goodsCodes);
            // 集合：将数据按照箱号分组
            $goodsCodesBoxGroup = $c -> groupBy('box_no');

            // 集合：按照店内码分组
            $goodsCodesGroup = $c -> groupBy('unique_code');

            // 所有店内码集合
            $uniqueCodes = array_unique(array_column($goodsCodes,'unique_code'));

            // 3. 校验箱号是否已存在
            // pk_id=0，新增数据，需要校验箱号重复
            if (isset($data['pk_id']) && 0 === (int)$data['pk_id']) {
                $goodsCodes = $this -> _checkBoxNoExists($goodsCodes,$goodsCodesBoxGroup);
            }
            $redisKey = $data['redis_key'] ?? '';
            $extend = [];
            if ($redisKey) {
                $extend['redis_key'] = $redisKey;
            }
            $extend['admin_id'] = $data['admin_id'];
            $extend['admin_name'] = $data['admin_name'];
            $extend['gather_way'] = $data['gather_way'];
            $goodsCodes = $this -> _uniqueCoreCheck($taskType,$sourceNo,$uniqueCodes,$goodsCodes,$goodsCodesGroup,$extend);

            // 7. 校验店内码是否重复
//            $goodsCodesGroup = collect($goodsCodes) -> groupBy(function ($item) {
//                return $item['unique_code'];
//            });
//            $goodsCodes = $this -> _checkIfGoodsCodeRepeat($goodsCodes,$goodsCodesGroup);

            // 最终数据整理
            return  $this -> _handleFinalData($goodsCodes);

        } catch (\Exception $exception) {
            throw new BusinessException($exception -> getMessage());
        }
    }

    private function _checkBoxNo($goodsCodes) {
        logger() -> debug('校验箱号下点你饿吗是否重复=============',[$goodsCodes]);
        // 每个数据记录其原始key，便于后面校验查找原始位置
        $statisticsUniqueCodes = [];
        foreach ($goodsCodes as $k => &$v) {
            $v['original_key'] = $k;
            if (!$this -> _checkBoxNoReg($v['box_no'])) {
                $v['exception'][] = '箱号仅允许字母数字或下划线';
            }
            // 如果是店内码，校验其在不同箱号下是否重复
            if (isset($v['unique_code'])) {
                // 不存在时添加，存在则提示 店内码重复 异常
                if (!in_array($v['unique_code'],$statisticsUniqueCodes)) {
                    array_push($statisticsUniqueCodes,$v['unique_code']);
                } else {
                    $v['exception'][] = '店内码重复';
                }
            }

//            // 如果是条码，校验其在不同箱号下总数是否超出可装箱数量
//            if (isset($v['barcode'])) {
//
////                if (!in_array($v['barcode'],$statisticsUniqueCodes)) {
////                    array_push($statisticsUniqueCodes,$v['unique_code']);
////                } else {
////                    $v['exception'][] = '条码总数超出当前任务可装箱数量~';
////                }
//            }
        }
        return $goodsCodes;
    }

    private function _checkBoxNoReg($str) {

        if (preg_match('/^[\w-]+$/',$str)){
            return true;
        }else {
            return false;
        }

    }

    // 店内码核心校验部分
    private function _uniqueCoreCheck($taskType,$sourceNo,$uniqueCodes,$goodsCodes,$goodsCodesGroup,$extend = []) {
        // 收集未出库，错下架店内码，异步调用服务记录追溯问题店内码拣货人-240201-yj
        $notOutStoreGoods = [];
        $wrongOffGoods = [];

        // 4. 校验店内码是否已打包
        $t1 = microtime(true);
        $goodsCodes = $this -> _checkIfUniqueIfPacked($taskType,$sourceNo,$goodsCodes,$goodsCodesGroup,$uniqueCodes);
        $t2 = microtime(true);
        $diff = number_format($t2-$t1, 3, '.', '');
        $consume_total = $diff." s";
        var_dump('店内码核心校验部分-校验店内码是否已打包-代码耗时：'.$consume_total);

        // 6. 校验店内码是否存在
        $t1 = microtime(true);
        $goodsCodes = $this -> _checkUniqueExists($goodsCodes,$goodsCodesGroup,$uniqueCodes);
        $t2 = microtime(true);
        $diff = number_format($t2-$t1, 3, '.', '');
        $consume_total = $diff." s";
        var_dump('店内码核心校验部分-校验店内码是否存在-代码耗时：'.$consume_total);

        // 5. 校验店内码是否未出库，已锁定
        $t1 = microtime(true);
        $goodsCodes = $this -> _checkIfUniqueOutStore($goodsCodes,$goodsCodesGroup,$uniqueCodes,$notOutStoreGoods,['source_no'=>$sourceNo]);
        $t2 = microtime(true);
        $diff = number_format($t2-$t1, 3, '.', '');
        $consume_total = $diff." s";
        var_dump('店内码核心校验部分-校验店内码是否未出库或已锁定-代码耗时：'.$consume_total);

        // 3. 根据任务单号校验店内码是否属于此任务
        $t1 = microtime(true);
        $goodsCodes = $this -> _checkIfUniqueInTheBatch($taskType,$sourceNo,$goodsCodes,$uniqueCodes,$goodsCodesGroup,$wrongOffGoods,$extend);
        $t2 = microtime(true);
        $diff = number_format($t2-$t1, 3, '.', '');
        $consume_total = $diff." s";
        var_dump('店内码核心校验部分-根据任务单号校验店内码是否属于此任务-代码耗时：'.$consume_total);

        // 4. 空扣拦截
        $t1 = microtime(true);
        $goodsCodes = $this -> _checkEmptyTag($goodsCodes);
        $t2 = microtime(true);
        $diff = number_format($t2-$t1, 3, '.', '');
        $consume_total = $diff." s";
        var_dump('店内码核心校验部分-根据任务单号校验店内码是否属于此任务-代码耗时：'.$consume_total);

        // 异步记录未出库，错下架等异常店内码-240201-yj
        // 仅针对店内码和RFID
        $nowCodes = array_unique(array_merge($notOutStoreGoods,$wrongOffGoods));
        logger() -> debug('extend=====',$extend);
        // 如果是批量上传过来的，一定是店内码，此时gather_way=23
        if (in_array($extend['gather_way'],[GoodsPackingCommon::GATHER_WAY_RFID,GoodsPackingCommon::GATHER_WAY_UNIQUE_CODE,23]) && !empty($nowCodes)) {
            $boxNos = array_unique(array_column($goodsCodes,'box_no'));
            // 按箱号区分
            foreach ($boxNos as $boxNo) {
                $asyncKey = 'gp_wrong_'.$boxNo.'_'.$extend['admin_id'];
                $lastAsync = redis() -> get($asyncKey);
                logger() ->debug('异步上次记录key',[$asyncKey]);
                logger() ->debug('异步上次记录',[$lastAsync]);
                $lastAsync = $lastAsync ? json_decode($lastAsync,true) : [];

                // 如果$nowCodes与$lastAsync存在差集，说明上次未调用过
                $diff = array_diff($nowCodes,$lastAsync);
                logger() ->debug('异步记录错下架差集',[$diff]);
                if ($diff) {
                    $p = [
                        'source_code' => $sourceNo,
                        'gather_way' => $extend['gather_way'],
                        'goods_codes' => $goodsCodes,//本次装箱数据，主要用于确定before_code
                        'now_code' => $diff,
                        'admin_id'=> $extend['admin_id'],
                        'admin_name' => $extend['admin_name'],
                    ];
                    logger() -> debug('异步记录未出库，错下架等异常店内码',[$p]);
                    $this -> GoodsPackingService -> exceptionGoodsAdd($p);
                    // 存下已经记录的错下架，用以下次调用时查询是否存在
                    redis() -> set($asyncKey,json_encode($nowCodes));
                    redis() -> expire($asyncKey,60*60*12);
                }
            }


        }

        // 错下架拦截
        if ($wrongOffGoods) {
            throw new BusinessException(sprintf('店内码【%s】错下架，请先移走此店内码然后再扫描或导入！',join(',',$wrongOffGoods)));
        }
        return $goodsCodes;
    }

    private function _checkSourceNo($taskType,$sourceNo) {
        if (1 == $taskType) { // 调拨任务
            $checkRes = $this -> AllotService -> checkSerialNos([$sourceNo]);
            if (!empty($checkRes['not_exists'])) {
                throw new BusinessException('来源单号【调拨单号】不存在');
            }
        }

        if (2 == $taskType) { // 退返任务
            $checkRes = $this -> BackOrderService -> checkSerialNos([$sourceNo]);
            if (!empty($checkRes['not_exists'])) {
                throw new BusinessException('来源单号【退返单号】不存在');
            }

            // 校验退返单是否已发货
            $checkRes = $this -> BackOrderService -> getOrder(['serial_no'=>$sourceNo]);
            if (5 != $checkRes['status']) { // 退返状态 0待部门审核 1部门驳回 2待财务审核 3财务驳回 4待接单 5待出库 6待发货 7已发货 8已签收 -1已作废
                throw new BusinessException('该退返单无法装箱：原因（'.PublicCode::BACK_ORDER_STATUS[$checkRes['status']].'）');
            }
        }
    }

    private function _checkBoxNoExists($goodsCodes,$goodsCodesGroup) {
        $boxNos = array_unique(array_column($goodsCodes,'box_no'));
        $checkRes = $this -> GoodsPackingService -> checkBoxNos($boxNos);
        logger() -> debug('校验箱号是否存在=======',[$checkRes]);
        if (!empty($checkRes['exists'])) {
            foreach ($checkRes['exists'] as $boxNo) {
                // 找到同一箱号下的所有商品，标记异常
                $eBoxNoGoodsList = $goodsCodesGroup -> get($boxNo);
                if ($eBoxNoGoodsList) {
                    foreach ($eBoxNoGoodsList as $eBoxNoGoods) {
                        $goodsCodes[$eBoxNoGoods['original_key']]['exception'][] = '箱号【'.$boxNo.'】已存在';
                    }
                }
            }
        }
        return $goodsCodes;
    }

    private function _checkUniqueExists($goodsCodes,$goodsCodesGroup,$uniqueCodes) {
        $checkURes = $this -> ShelfGoodsCodeMapService -> checkUniqueCodes($uniqueCodes);
        if (!empty($checkURes['not_exists'])) {
            foreach ($checkURes['not_exists'] as $uniqueCode) {
                // 根据店内码找到原始位置，追加异常标记
                $groupData = $goodsCodesGroup -> get($uniqueCode);
                if ($groupData) {
                    foreach ($groupData as $groupDatum) {
                        $goodsCodes[$groupDatum['original_key']]['exception'][] = '店内码不存在';
                    }
                }
            }
        }
        return $goodsCodes;
    }

    private function _checkIfUniqueInTheBatch($taskType,$sourceNo,$goodsCodes,$uniqueCodes,$goodsCodesGroup,&$wrongOffGoods,$extend = []) {
        if (1 == $taskType) { // 调拨任务，去查是否属于此次调拨任务
            //$allotInfo = $this -> AllotService -> getAllotBySerialNo($data['source_no']);
            $checkRes = $this -> AllotService -> checkIfUniqueCodeInAllotBySerialNo($sourceNo,$uniqueCodes);
            logger() -> debug('调拨任务-去查是否属于此次调拨任务',[$checkRes]);
            logger() -> debug('调拨任务-goodsCodes=================',[$goodsCodes]);
            if (!empty($checkRes['not_exists'])) {
                // 店内码不属于此批次，异常弹框提示，终止流程 - 20230215 -yj
                // throw new BusinessException(sprintf('店内码【%s】错下架，请先移走此店内码然后再扫描或导入！',join(',',$checkRes['not_exists'])));
                $wrongOffData = [];
                // 店内码不属于此次任务的，异常信息写入
                foreach ($checkRes['not_exists'] as $k => $uniqueCode) {
                    // 根据店内码找到原始位置，追加异常标记
                    $groupData = $goodsCodesGroup -> get($uniqueCode);
                    if ($groupData) {
                        foreach ($groupData as $groupDatum) {
                            $goodsCodes[$groupDatum['original_key']]['exception'][] = sprintf('店内码【%s】错下架，请先移走此店内码然后再扫描或导入！',$uniqueCode);
                            array_push($wrongOffData,$goodsCodes[$groupDatum['original_key']]);
                        }
                    }
                }
                if (!empty($wrongOffData)) {
                    // 检查redis缓存中是否已存在错下架店内码，若存在，则删除
                    $redisKey = $extend['redis_key'] ?? '';
                    if ($redisKey) {
                        $this -> _deleteWrongOffData([
                            'redis_key' => $redisKey,
                            'wrong_off_data' => array_values($wrongOffData)
                        ]);
                    }
                    // 外部收集错下架店内码，追溯拣货人-240201-yj
                    $wrongOffGoods = $checkRes['not_exists'];
//                    throw new BusinessException(sprintf('店内码【%s】错下架，请先移走此店内码然后再扫描或导入！',join(',',$checkRes['not_exists'])));
                }
            }
        }

        if (2 == $taskType) { // 退返任务，去查是否属于此次退返任务
            $checkRes = $this -> BackOrderService -> checkIfUniqueCodeInBackOrderBySerialNo($sourceNo,$uniqueCodes);
            if (!empty($checkRes['not_exists'])) { // 店内码不属于此次任务的，异常信息写入
                // 店内码不属于此批次，异常弹框提示，终止流程 - 20230215 -yj
                // throw new BusinessException(sprintf('店内码【%s】错下架，请先移走此店内码然后再扫描或导入！',join(',',$checkRes['not_exists'])));
                $wrongOffData = [];
                // 店内码不属于此次任务的，异常信息写入
                foreach ($checkRes['not_exists'] as $k => $uniqueCode) {
                    // 根据店内码找到原始位置，追加异常标记
                    $groupData = $goodsCodesGroup -> get($uniqueCode);
                    if ($groupData) {
                        foreach ($groupData as $groupDatum) {
                            $goodsCodes[$groupDatum['original_key']]['exception'][] = sprintf('店内码【%s】错下架，请先移走此店内码然后再扫描或导入！',$uniqueCode);
                            array_push($wrongOffData,$goodsCodes[$groupDatum['original_key']]);
                        }
                    }
                }
                if (!empty($wrongOffData)) {
                    // 检查redis缓存中是否已存在错下架店内码，若存在，则删除
                    $redisKey = $extend['redis_key'] ?? '';
                    if ($redisKey) {
                        $this -> _deleteWrongOffData([
                            'redis_key' => $redisKey,
                            'wrong_off_data' => array_values($wrongOffData)
                        ]);
                    }

//                    throw new BusinessException(sprintf('店内码【%s】错下架，请先移走此店内码然后再扫描或导入！',join(',',$checkRes['not_exists'])));
                }
//                foreach ($checkRes['not_exists'] as $k => $uniqueCode) {
//                    // 根据店内码找到原始位置，追加异常标记
//                    $groupData = $goodsCodesGroup -> get($uniqueCode);
//                    if ($groupData) {
//                        foreach ($groupData as $groupDatum) {
//                            $goodsCodes[$groupDatum['original_key']]['exception'][] = '店内码不属于此退返任务';
//                        }
//                    }
////                    $goodsCodeInfo = $goodsCodesUMap -> get($uniqueCode);
////                    $goodsCodes[$goodsCodeInfo['original_key']]['exception'][] = '店内码不属于此退返任务';
//                }
            }
        }
        return $goodsCodes;
    }

    private function _checkIfUniqueOutStore($goodsCodes,$goodsCodesGroup,$uniqueCodes,&$notOutStoreGoods,$extend=[]) {
        // 校验货品是否未出库
        $checkInStoreRes = $this -> ShelfGoodsCodeMapService -> checkIfUniqueCodesOutStore($uniqueCodes);
        if (!empty($checkInStoreRes['in_store'])) { // 如果有还在库的，则写入异常信息
            foreach ($checkInStoreRes['in_store'] as $k => $uniqueCode) {
                // 根据店内码找到原始位置，追加异常标记
                $groupData = $goodsCodesGroup -> get($uniqueCode);
                if ($groupData) {
                    foreach ($groupData as $groupDatum) {
                        $goodsCodes[$groupDatum['original_key']]['exception'][] = '店内码未出库';
                        // 收集未出库店内码
                        $notOutStoreGoods[] = $uniqueCode;
                    }
                }
//                $goodsCodeInfo = $goodsCodesUMap -> get($uniqueCode);
//                $goodsCodes[$goodsCodeInfo['original_key']]['exception'][] = '店内码未出库';
            }
        }
        if (!empty($checkInStoreRes['locked'])) { // 店内码锁定，则写入异常信息
            // 锁定店内码先修复，修复后如果还是锁定状态，则抛异常-231220-yj
            $repairResult = GoodsPackingCommon::repairPickUniqueCodes(['source_no'=>$extend['source_no'],'unique_codes'=>$checkInStoreRes['locked']]);
            if ($repairResult['locked']) {
                foreach ($repairResult['locked'] as $k => $uniqueCode) {
                    // 根据店内码找到原始位置，追加异常标记
                    $groupData = $goodsCodesGroup -> get($uniqueCode);
                    if ($groupData) {
                        foreach ($groupData as $groupDatum) {
                            $goodsCodes[$groupDatum['original_key']]['exception'][] = '店内码已锁定';
                            // 收集未出库店内码
                            $notOutStoreGoods[] = $uniqueCode;
                        }
                    }
                }
            }

        }
        // 采购差异
        if (!empty($checkInStoreRes['without_stock'])) {
            foreach ($checkInStoreRes['without_stock'] as $uniqueCode) {
                // 根据店内码找到原始位置，追加异常标记
                $groupData = $goodsCodesGroup -> get($uniqueCode);
                if ($groupData) {
                    foreach ($groupData as $groupDatum) {
                        $goodsCodes[$groupDatum['original_key']]['exception'][] = '店内码是采购差异货品';
                        // 收集未出库店内码
                        $notOutStoreGoods[] = $uniqueCode;
                    }
                }
            }
        }
        return $goodsCodes;
    }

    private function _checkIfUniqueIfPacked($taskType,$sourceNo,$goodsCodes,$goodsCodesGroup,$uniqueCodes) {
        // 校验货品是否已打包
        $checkPackedRes = $this -> GoodsPackingService ->checkIfUniquePackedByTask((int)$taskType, $sourceNo, $uniqueCodes);
        if (!empty($checkPackedRes['packed'])) {
            foreach ($checkPackedRes['packed'] as $k => $v) {
                // 根据店内码找到原始位置，追加异常标记
                $groupData = $goodsCodesGroup -> get($v['unique_code']);
                if ($groupData) {
                    foreach ($groupData as $groupDatum) {
                        $goodsCodes[$groupDatum['original_key']]['exception'][] = '店内码已打包，箱号：'.$v['box_no'];
                    }
                }
//                $goodsCodeInfo = $goodsCodesUMap -> get($v['unique_code']);
//                $goodsCodes[$goodsCodeInfo['original_key']]['exception'][] = '店内码已打包，箱号：'.$v['box_no'];
            }
        }
        return $goodsCodes;
    }

    private function _handleFinalData($goodsCodes) {
        $result = [
            'normal_data' => [],
            'normal_num' => 0,
            'e_data' => [],
            'e_num' => 0,
        ];
        $normalNum = 0;
        $eNum = 0;
        foreach ($goodsCodes as $v) {
            logger() -> debug('_handleFinalData=',$v);
            unset($v['row_num']);
            unset($v['original_key']);
            if (isset($v['exception'])) {
                $eNum++;
                $v['e_type'] = 1; // 1=异常，2=正常
                array_push($result['e_data'],$v);
            } else {
                $normalNum++;
                $v['exception'][] = '正常';
                $v['e_type'] = 2; // 1=异常，2=正常
                array_push($result['normal_data'],$v);
            }
        }
        $result['e_num'] = $eNum;
        $result['normal_num'] = $normalNum;
        return $result;
    }

    private function _handleFinalDataForBarcode($goodsCodes) {
        $result = [
            'normal_data' => [],
            'normal_num' => 0,
            'e_data' => [],
            'e_num' => 0,
        ];
        foreach ($goodsCodes as $v) {
            unset($v['row_num']);
            unset($v['original_key']);
            if (isset($v['exception'])) {
                $v['e_type'] = 1; // 1=异常，2=正常
                array_push($result['e_data'],$v);
            } else {
                $v['exception'][] = '正常';
                $v['e_type'] = 2; // 1=异常，2=正常
                array_push($result['normal_data'],$v);
            }
        }
        $result['e_num'] = array_sum(array_column($result['e_data'],'num'));
        $result['normal_num'] = array_sum(array_column($result['normal_data'],'num'));
        return $result;
    }

    private function _bulkCheckEpc($data) {
        $taskType = $data['task_type'] ?? 0;
        $sourceNo = $data['source_no'] ?? '';
        $goodsCodes = $data['goods_codes'] ?? [];

        if (empty($goodsCodes)) {
            throw new BusinessException('货品不能为空');
        }
        if (empty($taskType)) {
            throw new BusinessException('任务类型不能为空');
        }
        if (empty($sourceNo)) {
            throw new BusinessException('来源单号不能为空');
        }

        $t1 = microtime(true);
        // 每个数据记录其原始key，便于后面校验查找原始位置
        foreach ($goodsCodes as $k => &$v) {
            $v['original_key'] = $k;
            $v['unique_code'] = '';
            $v['barcode'] = '';
            $v['num'] = 1;
        }

        $t2 = microtime(true);
        $diff = number_format($t2-$t1, 3, '.', '');
        $consume_total = $diff." s";
        var_dump('每个数据记录其原始key-代码耗时：'.$consume_total);

        // 集合：将数据按照箱号分组
        $c = collect($goodsCodes);

        // map：epc_code分组
        $epcMap = $c -> keyBy('epc_code');

        // 集合：将数据按照店内码为键映射
        $goodsCodesGroup = $c -> groupBy('epc_code');

        try {
            // 1. 校验单号
            $t1 = microtime(true);
            $this -> _checkSourceNo($taskType,$sourceNo);
            $t2 = microtime(true);
            $diff = number_format($t2-$t1, 3, '.', '');
            $consume_total = $diff." s";
            var_dump('校验单号-代码耗时：'.$consume_total);

            // 2. 校验epc是否重复
            // $this -> _checkIfGoodsCodeRepeat($goodsCodes,$goodsCodesGroup);

            // 3. 校验是epc否绑定
            $t1 = microtime(true);
            $checkExistsRes = $this -> _checkIfEpcExists($goodsCodes,$goodsCodesGroup);
            $t2 = microtime(true);
            $diff = number_format($t2-$t1, 3, '.', '');
            $consume_total = $diff." s";
            var_dump('校验是epc否绑定-代码耗时：'.$consume_total);

            $goodsCodes = $checkExistsRes['goods_codes'];
            $existsEpc = $checkExistsRes['exists_epc'];

            // 4. 存在的epc，开始分开店内码和条码分别校验
            $t1 = microtime(true);

            $redisKey = $data['redis_key'] ?? '';
            $extend = [];
            if ($redisKey) {
                $extend['redis_key'] = $redisKey;
            }
            $extend['admin_id'] = $data['admin_id'];
            $extend['admin_name'] = $data['admin_name'];
            $extend['gather_way'] = $data['gather_way'];

            $goodsCodes = $this -> _checkUniqueAndBarcode($taskType,$sourceNo,$goodsCodes,$existsEpc,$goodsCodesGroup,$extend);
            $t2 = microtime(true);
            $diff = number_format($t2-$t1, 3, '.', '');
            $consume_total = $diff." s";
            var_dump('存在的epc，开始分开店内码和条码分别校验-代码耗时：'.$consume_total);

            // 7. 最终数据整理
            $t1 = microtime(true);
            $result =   $this -> _handleFinalData($goodsCodes);
            $t2 = microtime(true);
            $diff = number_format($t2-$t1, 3, '.', '');
            $consume_total = $diff." s";
            var_dump('最终数据整理-代码耗时：'.$consume_total);
            return $result;

        } catch (\Exception $exception) {
            throw new BusinessException($exception -> getMessage());
        }
    }

    private function _checkUniqueAndBarcode($taskType,$sourceNo,$goodsCodes,$existsEpc,$goodsCodesGroup,$extend=[]) {
        // 存在的epc码，继续下一步校验
        $existsEpcInfo = array_column($existsEpc,NULL,'epc_code');
        $existsEpc = array_keys($existsEpcInfo);
        if (!empty($existsEpc)) {
            // 根据传入的EPC码按绑定店内码和条码归类
            $t1 = microtime(true);
            $checkRes = $this -> ShelfGoodsCodeMapService -> getGoodsCodeMapByEpcCodes($existsEpc);
            $t2 = microtime(true);
            $diff = number_format($t2-$t1, 3, '.', '');
            $consume_total = $diff." s";
            var_dump('根据传入的EPC码按绑定店内码和条码归类-代码耗时：'.$consume_total);
            // 绑定店内码的EPC的店内码集合
            $uniqueCodes = $checkRes['unique_code'];
            // 绑定条码的EPC的条码集合
            // 注意：不同EPC绑定的条码可能是相同的
            $barcodes = $checkRes['barcode'];
            // 校验店内码
            if (!empty($uniqueCodes)) {
                var_dump('校验店内码');
                // 将返回结果的店内码追加到原始数据里面
                foreach ($uniqueCodes as $uniqueCode ) {
                    $groupData = $goodsCodesGroup -> get($uniqueCode['epc_code']);
                    if ($groupData) {
                        foreach ($groupData as $groupDatum) {
                            $goodsCodes[$groupDatum['original_key']]['unique_code'] = $uniqueCode['unique_code'];
                        }
                    }
                }
                // 将$goodsCodesGroup替换成店内码分组
                $goodsCodesUGroup = collect($goodsCodes) -> groupBy('unique_code');
                $uniqueCodes = array_unique(array_column($uniqueCodes,'unique_code'));
                $goodsCodes = $this -> _uniqueCoreCheck($taskType,$sourceNo,$uniqueCodes,$goodsCodes,$goodsCodesUGroup,$extend);
            }


            // 校验条码
            if (!empty($barcodes)) {
                var_dump('校验条码');
                // 将返回结果的店内码追加到原始数据里面
                foreach ($barcodes as $barcode ) {
                    $groupData = $goodsCodesGroup -> get($barcode['epc_code']);
                    if ($groupData) {
                        foreach ($groupData as $groupDatum) {
                            $goodsCodes[$groupDatum['original_key']]['barcode'] = $barcode['bar_code'];
                        }
                    }
                }
                // 将$goodsCodesGroup替换成条码分组
                $goodsCodesBGroup = collect($goodsCodes) -> groupBy('barcode');
                $barcodes = array_unique(array_column($barcodes,'bar_code'));
                $goodsCodes = $this -> _barcodeCoreCheck($taskType,$sourceNo,$goodsCodes,$barcodes,$goodsCodesBGroup);
            }
        }

        return $goodsCodes;
    }

    private function _checkIfGoodsCodeRepeat($goodsCodes,$goodsCodesGroup) {
        foreach ($goodsCodesGroup as $key => $groups) {
            if ($key && count($groups) > 1) {
                // 找出original_key最大者
                $maxKey = $groups -> max('original_key');
                foreach ($groups as $k => $item) {
                    // 最大key者不标记，其余非异常数据全标记重复
                    if ($maxKey != $item['original_key'] && !isset($item['exception'])) {
                        $goodsCodes[$item['original_key']]['exception'][] = '货品重复';
                    }
                }
            }
        }
        return $goodsCodes;
    }

    private function _checkIfEpcExists($goodsCodes,$goodsCodesGroup) {
        logger() -> debug('开始校验epc是否重复',[$goodsCodes]);
        $epcCodes1 = array_unique(array_column($goodsCodes,'epc_code'));
        // 校验epc是否空扣：epc未绑定店内码但是在磁扣池，抛异常，既未绑定店内码又不在磁扣池，直接过滤掉 - 240108-yj
        $checkRes = $this -> ShelfGoodsCodeMapService ->checkEpcExistsAndGetEpcInfo($epcCodes1);
        logger() -> debug('校验磁扣',[$checkRes]);
        if (!empty($checkRes['not_exists'])) {
            logger() -> debug('未绑定店内码的epc',[$checkRes['not_exists']]);

            // 检查是否BB开头，如果是，强拦截 - 20250227 - jy
            $bbEpcCodes = array_filter($checkRes['not_exists'], function($epcCode) {
                return substr($epcCode, 0, 2) === 'BB';
            });

            // 将$checkRes['not_exists']中的BB开头epc码移除
            $checkRes['not_exists'] = array_diff($checkRes['not_exists'], $bbEpcCodes);

            $epcCodes = [];
            if (!empty($checkRes['not_exists'])) {
                $epcCodes = $this->EpcPoolService->all(['epc_code' => $checkRes['not_exists'], 'status' => 1]);
                logger()->debug('未绑定店内码epc磁扣池查询结果', [$epcCodes]);
                $epcCodes = array_column($epcCodes, 'epc_code');
            }

            // 分别判断两种情况，有任意一种就抛出对应提示
            if (!empty($bbEpcCodes) && !empty($epcCodes)) {
                throw new BusinessException(sprintf('epc【%s】是空扣，请先绑店内码再装箱', join(',', array_merge($epcCodes, $bbEpcCodes))));
            } elseif (!empty($bbEpcCodes)) {
                throw new BusinessException(sprintf('epc【%s】是空扣，请先绑店内码再装箱', join(',', $bbEpcCodes)));
            } elseif (!empty($epcCodes)) {
                throw new BusinessException(sprintf('epc【%s】是空扣，请先绑店内码再装箱', join(',', $epcCodes)));
            }

            // 既未绑定店内码又不在磁扣池，直接过滤掉
            $notEpcPoolCodes = array_diff($checkRes['not_exists'],$epcCodes);
            logger() -> debug('未绑定店内码且不在磁扣池的epc',[$notEpcPoolCodes]);
            $goodsCodesMap = array_column($goodsCodes,NULL,'epc_code');
            foreach ($notEpcPoolCodes as $notEpcPoolCode) {
                $originalKey = $goodsCodesMap[$notEpcPoolCode]['original_key'];
                unset($goodsCodes[$originalKey]);
            }
            logger() -> debug('去掉第三方口的goodsCodes',[$goodsCodes]);
        }
        return [
            'goods_codes' => $goodsCodes,
            'exists_epc' => $checkRes['exists'] ?? [],
        ];
    }

//    private function _checkIfEpcExists($goodsCodes,$goodsCodesGroup) {
//        logger() -> debug('开始校验epc是否重复',[$goodsCodes]);
//        $epcCodes1 = array_unique(array_column($goodsCodes,'epc_code'));
//        // 校验epc是否空扣：先查询磁扣池，若磁扣池中存在，再查是否空扣,若空扣，则抛异常
//        logger() ->debug('校验epc是否空扣',[$epcCodes1]);
//        $epcCodes = $this -> EpcPoolService -> all(['epc_code' => $epcCodes1,'status' => 1]);
//        logger() -> debug('磁扣池查询结果',[$epcCodes]);
//        $checkRes = [];
//        $epcCodes = $epcCodes ? array_column($epcCodes,'epc_code') : [];
//        if ($epcCodes) {
//            // 校验epc码是否存在
//            $checkRes = $this -> ShelfGoodsCodeMapService ->checkEpcExistsAndGetEpcInfo($epcCodes);
//            logger() -> debug('校验磁扣',[$checkRes]);
//            if (!empty($checkRes['not_exists'])) {
//                throw new BusinessException(sprintf('epc【%s】是空扣，请先绑店内码再装箱',join(',',$checkRes['not_exists'])));
////            foreach ($checkRes['not_exists'] as $k => $v) {
////                // 根据店内码找到原始位置，追加异常标记
////                $groupData = $goodsCodesGroup -> get($v);
////                if ($groupData) {
////                    foreach ($groupData as $groupDatum) {
////                        $goodsCodes[$groupDatum['original_key']]['exception'][] = 'epc码未绑定';
////                    }
////                }
////            }
//            }
//        }
//
//        // 不在磁扣池的epc是第三方扣，直接过滤掉，校验结果也不出现，仅我方扣，且是空扣，才提示 - 240109-yj
//        $notEpcPoolCodes = array_diff($epcCodes1,$epcCodes);
//        logger() -> debug('不在磁扣池的epc',[$notEpcPoolCodes]);
//        $goodsCodesMap = array_column($goodsCodes,NULL,'epc_code');
//        foreach ($notEpcPoolCodes as $notEpcPoolCode) {
//            $originalKey = $goodsCodesMap[$notEpcPoolCode]['original_key'];
//            unset($goodsCodes[$originalKey]);
//        }
//        logger() -> debug('去掉第三方口的goodsCodes',[$goodsCodes]);
//        return [
//            'goods_codes' => $goodsCodes,
//            'exists_epc' => $checkRes['exists'] ?? [],
//        ];
//    }

    // 将校验结果存入hash
    private function _saveCheckResultToHash($redisKey,$type,$data,$expire) {
        $eKey = $redisKey.self::E_DATA_SUFFIX;
        $normalKey = $redisKey.self::NORMAL_DATA_SUFFIX;

        $this -> HashService -> del($eKey);
        $this -> HashService -> del($normalKey);

        try {
            if (!empty($data['e_data'])) {
                $eSaveData = collect($data['e_data']) -> keyBy(function ($item) {
                    return $item['box_no'].'-'.$this -> _getGoodsCode($item);
                });
                $eSaveData = $eSaveData -> toArray();
                $this -> _saveNewDataToHash($eKey,$eSaveData,$expire);
            }
            if (!empty($data['normal_data'])) {
                $normalSaveData = collect($data['normal_data']) -> keyBy(function ($item) {
                    return $item['box_no'].'-'.$this -> _getGoodsCode($item);
                });
                $normalSaveData = $normalSaveData -> toArray();
                $this -> _saveNewDataToHash($normalKey,$normalSaveData,$expire);
            }
        } catch (\Exception $exception) {
            throw new BusinessException($exception ->getMessage());
        }
    }
    private function _saveCheckResultToHash_bak($redisKey,$type,$data,$expire) {
        $eKey = $redisKey.self::E_DATA_SUFFIX;
        $normalKey = $redisKey.self::NORMAL_DATA_SUFFIX;
        $eSaveData = collect([]);
        $normalSaveData = collect([]);
//        if ('unique_code' == $type) {
//            $eSaveData = array_column($data['e_data'],NULL,'unique_code');
//            $normalSaveData = array_column($data['normal_data'],NULL,'unique_code');
//        }
//        if ('barcode' == $type) {
//            $eSaveData = array_column($data['e_data'],NULL,'barcode');
//            $normalSaveData = array_column($data['normal_data'],NULL,'barcode');
//        }
//        if ('epc_code' == $type) {
//            $eSaveData = array_column($data['e_data'],NULL,'epc_code');
//            $normalSaveData = array_column($data['normal_data'],NULL,'epc_code');
//        }

        $eSaveData = collect($data['e_data']) -> keyBy(function ($item) {
            return $item['box_no'].'-'.$this -> _getGoodsCode($item);
        });
        $normalSaveData = collect($data['normal_data']) -> keyBy(function ($item) {
            return $item['box_no'].'-'.$this -> _getGoodsCode($item);
        });
        try {
            if (!empty($data['e_data'])) {
                // 先取出，追加，在删除，写入
                $lastEData = $this -> HashService -> getDataAllFromHash($eKey);
                if ($lastEData) {
                    foreach ($lastEData as $k => &$v) {
                        $newEList = $eSaveData[$k]['exception'];
                        foreach ($newEList as $item) {
                            array_unshift($v['exception'],$item);
                        }
//                        if ('barcode' == $type) {
//                            // 条码 历次数量累加
//                            $v['exception'] = $v['exception'] + $eSaveData[$k]['num'];
//                        }
                        // 数量以最后一次为准
                        $v['num'] = $eSaveData[$k]['num'];
                        $v['exception'] = array_unique($v['exception']);
                    }
                    $eSaveData = $lastEData;
                }
                $this -> _saveNewDataToHash($eKey,$eSaveData,$expire);
            }
            if (!empty($data['normal_data'])) {
                // 先取出，追加，在删除，写入
                $lastData = $this -> HashService -> getDataAllFromHash($normalKey);
                if ($lastData) {
                    foreach ($lastData as $k => &$v) {
                        $newEList = $normalSaveData[$k]['exception'];
                        foreach ($newEList as $item) {
                            array_unshift($v['exception'],$item);
                        }
                        // 条码数据要累加
//                        $newNum = $normalSaveData[$k]['num'];
//                        if ('barcode' == $type) {
//                            $v['num'] = $v['num'] + $newNum;
//                        }
                        $v['exception'] = array_unique($v['exception']);
                    }
                    $normalSaveData = $lastData;
                }
                $this -> _saveDataToHash($normalKey,$normalSaveData,$expire);
            }
        } catch (\Exception $exception) {
            throw new BusinessException($exception ->getMessage());
        }
    }

    private function _saveNewDataToHash($key,$data,$expire) {
        $this -> _saveDataToHash($key,$data,$expire);
    }

    private function _mergeHistoryData($redisKey,$type,$newData,$uploadType = 0) {
        $t1 = microtime(true);
        // 新上传的文件内数据，如果同条码，则累加
        if (2 == $uploadType) { // 条码上传
            $newDataGroup = collect($newData) -> groupBy(function ($item) {
                return $item['box_no'].'-'.$this -> _getGoodsCode($item);
            });
            $newData2 = [];
            foreach ($newDataGroup as $items) {
                $c  = collect($items);
                $total = $c -> sum('num');
                $item = $c -> first();
                $item['num'] = $total;
                array_push($newData2,$item);
            }
            $newData = $newData2;
        }

        $t2 = microtime(true);
        $diff = number_format($t2-$t1, 3, '.', '');
        $consume_total = $diff." s";
        var_dump('合并历史数据-梳理条码情况-代码耗时：'.$consume_total);

        $eKey = $redisKey.self::E_DATA_SUFFIX;
        $normalKey = $redisKey.self::NORMAL_DATA_SUFFIX;

//        $this -> HashService ->del($eKey);
//        $this -> HashService ->del($normalKey);
        $t1 = microtime(true);
        // 取出旧数据
        $lastEData = $this -> HashService -> getDataAllFromHash($eKey);
        $lastData = $this -> HashService -> getDataAllFromHash($normalKey);

        $t2 = microtime(true);
        $diff = number_format($t2-$t1, 3, '.', '');
        $consume_total = $diff." s";
        var_dump('合并历史数据-取出旧数据-代码耗时：'.$consume_total);

        // 新旧数据合并
        $t1 = microtime(true);
        $lastDataMerge = array_merge($lastEData,$lastData);
        $lastDataMerge = collect($lastDataMerge) -> sortBy('save_sort') -> toArray();
        $mergedData = array_merge($lastDataMerge,$newData);

        $t2 = microtime(true);
        $diff = number_format($t2-$t1, 3, '.', '');
        $consume_total = $diff." s";
        var_dump('合并历史数据-新旧数据合并-代码耗时：'.$consume_total);

        $t1 = microtime(true);
        // 以 箱号-商品码 为空key，条码key相同者累加，店内码，epc以最后一个为准，组装map
        $result = [];
        if (!empty($mergedData)) {
            $redis = redis();
            $incrKey = date('Ymd_').'sort_value';
            $redis -> incr($incrKey);
            $redis -> expire($incrKey,86400);
            foreach ($mergedData as &$mergedDatum) {
                // 以天为单位，第二天从头计数
//                $curTime = $redis -> incr($incrKey);
                // 统一追加保存时间
                $mergedDatum['save_sort'] = microtime(true);
                if (isset($mergedDatum['exception'])) {
                    unset($mergedDatum['exception']);
                }

                if (isset($mergedDatum['e_type'])) {
                    unset($mergedDatum['e_type']);
                }

                if (isset($mergedDatum['original_key'])) {
                    unset($mergedDatum['original_key']);
                }

                if (isset($mergedDatum['row_num'])) {
                    unset($mergedDatum['row_num']);
                }

                // 若是上传的条码，以最后一次数量为准
                if (2 == $uploadType) {
                    $mergedDatum['has_modified'] = 1;
                }

                $itemKey = $mergedDatum['box_no'].'-'.$this -> _getGoodsCode($mergedDatum);
                $goodsCodeType = $this -> _getGoodsCodeType($mergedDatum);
                // 对已经存在的进行修改
                if (isset($result[$itemKey])) {
                    // 条码，累加数量
                    if ($goodsCodeType == 2) {
                        // 手动修改过的数据，以最后一次数量为准
                        if (isset($mergedDatum['has_modified'])) {
                            $result[$itemKey]['num'] = $mergedDatum['num'];
                        } else {
                            $result[$itemKey]['num'] = $result[$itemKey]['num'] + $mergedDatum['num'];
                        }
                    } else {
                        $result[$itemKey]['num'] = 1;
                    }
                } else {
                    $result[$itemKey] = $mergedDatum;
                }

            }
        }

        $t2 = microtime(true);
        $diff = number_format($t2-$t1, 3, '.', '');
        $consume_total = $diff." s";
        var_dump('合并历史数据-重新组装-代码耗时：'.$consume_total);

        $result = array_values($result);
        return $result;

    }

    /**
     * 将数据以hash结构存入redis
     * @param string $key
     * @param array $data
     * @param int $expire
     * @return array|void
     */
    private function _saveDataToHash(string $key, array $data, int $expire)
    {
        // 使用管道批量发送命
        $redis = redis();
        $pipe = $redis -> pipeline();
        foreach ($data as $k => $v) {
            // 以 箱号-商品码为key
            $pipe -> hSet($key,$v['box_no'].'-'.$this -> _getGoodsCode($v), json_encode($v));
        }

        // 按原来的顺序返回每条指令执行结果数组，1=成功，0=失败
        $ret = $pipe -> exec();

        // 设置过期时间
        $redis -> expire($key, $expire);

        return $ret;
    }

    private function _getGoodsCode($item) {
        if (isset($item['epc_code'])) {
            return $item['epc_code'];
        } else {
            if (!empty($item['unique_code'])) {
                return $item['unique_code'];
            }
            if (!empty($item['barcode'])) {
                return $item['barcode'];
            }
        }
    }

    // 判断商品码类型：1=店内码，2=条码，3=epc码
    private function _getGoodsCodeType($item) {
        if (isset($item['epc_code'])) {
            return 3;
        } else {
            if (!empty($item['unique_code'])) {
                return 1;
            }
            if (!empty($item['barcode'])) {
                return 2;
            }
        }
    }

    /**
     * 获取装箱校验结果（历史累计结果）
     * @RequestMapping(path="/goodsPacking/getBulkCheckResult", methods="get,post")
     */
    public function getBulkCheckResult(RequestInterface $request)
    {
        // 将上传的盘点数据存入redis
        $params = $request -> all();
        $userInfo = $this -> session -> get('userInfo');
        $validator = validate()->make($params, [
            'task_type' => 'required|integer',
            'gather_way' => 'required|integer',
            'upload_type' => 'required|integer',
            'source_no' => 'required',
        ],[
            'task_type.required'=> '任务类型必填',
            'task_type.integer'=> '任务类型参数必须是正整数',
            'gather_way.required'=> '采集方式必填',
            'gather_way.integer'=> '采集方式类型参数必须是正整数',
            'upload_type.required'=> '上传类型必填',
            'upload_type.integer'=> '上传类型必须是正整数',
            'source_no.required'=> '来源单号必填',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $ret = $this -> _getBulkCheckResult($userInfo['uid'],$params['gather_way'],$params['task_type'],$params['source_no'],$params['upload_type'],$params['export']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }
    }

    private function _getBulkCheckResult($adminId,$gather_way,$task_type,$source_no,$upload_type,$export=0)
    {

        $redisKey = $this -> _makeRedisKey($adminId,$gather_way,$task_type,$source_no,$upload_type);

        try {
            // e_type = 1 异常 ，e_type = 2 正常
            // 异常
            $eData = $this -> getSortedDataAllFromHash($redisKey.self::E_DATA_SUFFIX,'save_sort','desc');
            // 正常
            $normalData = $this -> getSortedDataAllFromHash($redisKey.self::NORMAL_DATA_SUFFIX,'save_sort','desc');
            $result = [
                'e_data' => array_values($eData),
                'e_num' => count($eData),
                'normal_data' => array_values($normalData),
                'normal_num' => count($normalData),
                'total' => count($eData)+count($normalData),
            ];

            if (isset($export) && 1 == $export) {
                $codeType = 0;
                // 店内码
                if (3 == $gather_way) {
                    $codeType = 1;
                }
                // 条码
                if (2 == $gather_way) {
                    $codeType = 2;
                }
                // RFID
                if (1 == $gather_way) {
                    $codeType = 3;
                }
                // 批量上传
                if (23 == $gather_way) {
                    // 店内码
                    if (1 == $upload_type) {
                        $codeType = 1;
                    }
                    // 条码
                    if (2 == $upload_type) {
                        $codeType = 2;
                    }
                }
                if (empty($eData) && empty($normalData)) throw new BusinessException('下载数据为空');
                $exportUrl= $this -> exportStResult(array_merge($eData,$normalData),$codeType);
                return ['url'=>$exportUrl];
            }
            return $result;
        } catch (\Exception $exception) {
            throw new BusinessException($exception -> getMessage());
        }
    }

    private function _getSummary($normalData,$gather_way,$upload_type=0) {
        logger() ->debug('gather_way==='.$gather_way);
        logger() ->debug('upload_type==='.$upload_type);
        $result = [];
        $normalData = collect($normalData) -> groupBy('box_no') -> toArray();
        foreach ($normalData as $boxNo => $items) {
            $total = 0;
            foreach ($items as $item) {
                if (isset($item['unique_code']) && $item['unique_code']) {
                    $total++;
                }
                if (isset($item['barcode']) && $item['barcode']) {
                    $total = $total + $item['num'];
                }
            }
            array_push($result,[
                'box_no' => $boxNo,
                'gather_way' => $gather_way,
                'goods_code_type' => $this -> convertGatherWayToCodeType($gather_way,$upload_type),
                'total' => $total,
            ]);
        }

        return $result;
    }

    private function convertGatherWayToCodeType($gatherWay,$upload_type=0) {

        switch ((int)$gatherWay) {
            case 2:
                $codeType = 2;
                break;
            case 3:
                $codeType = 1;
                break;
            case 23:
                $codeType = $upload_type;
                break;
            default :
                $codeType = 0;
                break;
        }
        return $codeType;
    }

    private function exportStResult(array $data,$codeType){
        if (empty($data)) {
            throw new BusinessException('数据为空');
        }
        $head = [
            'box_no' => '箱号',
            'goods_code_type' => '类型',
            'goods_code' => '店内码/条形码',
            'num' => '数量',
            'exception' => '结果',
        ];
        if (3 == $codeType) {
            $head = [
                'epc_code' => 'epc码',
                'box_no' => '箱号',
                'goods_code_type' => '类型',
                'goods_code' => '店内码/条形码',
                'num' => '数量',
                'exception' => '结果',
            ];
        }
        $fileName = '装箱-'.date('Y-m-d-H-i-s');
        $eData = [];
        if (!empty($data)) {
            foreach ($data as $k => $v) {
                // 商品码类型：1=店内码，2=条形码，3=epc码
                $goodsCodeType = '';
                $goodsCode = '';
                if (1 == $codeType) {
                    $goodsCodeType = '店内码';
                    $goodsCode = $v['unique_code'];
                }
                if (2 == $codeType) {
                    $goodsCodeType = '条码';
                    $goodsCode = $v['barcode'];
                }
                if (3 == $codeType) {
                    $goodsCodeType = 'epc码';
                    $goodsCode = $v['epc_code'];
                }
                $item = [
                    'box_no' => $v['box_no'],
                    'goods_code_type' => $goodsCodeType,
                    'goods_code' => $goodsCode,
                    'num' => $v['num'] ?? 1,
                    'exception' => join(',',$v['exception']),
                ];
                if (3 == $codeType) {
                    $item = array_merge($item,[
                        'epc_code' => $v['epc_code']
                    ]);
                }
                array_push($eData,$item);
            }
        }

        try {
            return exportToExcel($head,$eData,$fileName);
        } catch (\Exception $exception) {
            throw new BusinessException($exception -> getMessage());
        }

    }

    private function _makeRedisKey($adminId,$gather_way,$task_type,$sourceNo,$upload_type) {
        $redisKey = '';
        // RFID
        if ($gather_way == 1) {
            $redisKey = $this -> _getRedisKey($adminId,$task_type,$gather_way,$sourceNo,'epc_code');
        }

        // 条码
        if ($gather_way == 2) {
            $redisKey = $this -> _getRedisKey($adminId,$task_type,$gather_way,$sourceNo,'barcode');
        }

        // 店内码
        if ($gather_way == 3) {
            $redisKey = $this -> _getRedisKey($adminId,$task_type,$gather_way,$sourceNo,'unique_code');
        }

        if ($gather_way == 23) {
            // 获取操作人员信息
            if ($upload_type == 1) {
                $redisKey = $this->_getRedisKey($adminId, $task_type, $gather_way, $sourceNo,'unique_code');
            }
            if ($upload_type == 2) {
                $redisKey = $this->_getRedisKey($adminId, $task_type, $gather_way, $sourceNo,'barcode');
            }
        }

        return $redisKey;
    }

    private function _getRedisKey($adminId,$taskType,$gatherWay,$sourceNo,$suffix='') {
        return 'goods_packing_'.$adminId.'_'.$taskType.'_'.$gatherWay.'_'.$sourceNo.'_'.$suffix;
    }

    /**
     * 删除已校验的正常，异常数据
     * @RequestMapping(path="/goodsPacking/deleteCheckedData", methods="get,post")
     */
    public function deleteCheckedData(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'task_type' => 'required|integer',
            'source_no' => 'required',
        ],[
            'task_type.required'=> '任务类型必填',
            'task_type.integer'=> '任务类型参数必须是正整数',
            'source_no.integer'=> '来源单号必填',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        // 登录用户
        $userInfo = $this->session->get('userInfo');

        try {
            // $redisKey = $this -> _makeRedisKey($userInfo['uid'],$params['gather_way'],$params['task_type'],$params['upload_type']);
            // 批量删除
            $this -> _bulkDelRedisData($userInfo['uid'],$params['gather_way'],$params['source_no'],$params['task_type']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),[]);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    // 批量模糊删除redis数据
    private function _bulkDelRedisData($adminId,$gather_way,$sourceNo,$task_type) {
        $redis = redis();

        $redisKey1e = $this -> _getRedisKey($adminId,$task_type,1,$sourceNo,'epc_code').self::E_DATA_SUFFIX;
        $redisKey1normal = $this -> _getRedisKey($adminId,$task_type,1,$sourceNo,'epc_code').self::NORMAL_DATA_SUFFIX;

        $redisKey2e = $this -> _getRedisKey($adminId,$task_type,2,$sourceNo,'barcode').self::E_DATA_SUFFIX;
        $redisKey2normal = $this -> _getRedisKey($adminId,$task_type,2,$sourceNo,'barcode').self::NORMAL_DATA_SUFFIX;

        $redisKey3e = $this -> _getRedisKey($adminId,$task_type,3,$sourceNo,'unique_code').self::E_DATA_SUFFIX;
        $redisKey3normal = $this -> _getRedisKey($adminId,$task_type,3,$sourceNo,'unique_code').self::NORMAL_DATA_SUFFIX;

        $redisKey231e = $this -> _getRedisKey($adminId, $task_type, 23, $sourceNo,'unique_code').self::E_DATA_SUFFIX;
        $redisKey231normal = $this -> _getRedisKey($adminId, $task_type, 23,$sourceNo, 'unique_code').self::NORMAL_DATA_SUFFIX;

        $redisKey232e = $this -> _getRedisKey($adminId, $task_type, 23, $sourceNo,'barcode').self::E_DATA_SUFFIX;
        $redisKey232normal = $this -> _getRedisKey($adminId, $task_type, 23, $sourceNo,'barcode').self::NORMAL_DATA_SUFFIX;

        $pile = $redis -> pipeline();

        $pile -> del($redisKey1e);
        $pile -> del($redisKey1normal);
        $pile -> del($redisKey2e);
        $pile -> del($redisKey2normal);
        $pile -> del($redisKey3e);
        $pile -> del($redisKey3normal);
        $pile -> del($redisKey231e);
        $pile -> del($redisKey231normal);
        $pile -> del($redisKey232e);
        $pile -> del($redisKey232normal);

        $pile -> exec();

    }

    /**
     * 数据验证
     * @param array $data
     * @return array
     */
    protected function validate(array $data,$secne='default'){

        $message = [
            'code_list.array' => '货品编码参数有误!',
            'code_list.required' => '货品编码参数有误!',
            'boxNos.array' => '箱号参数有误!',
            'box_no.string' => '箱号参数有误!',
            'remark.string' => '备注参数有误!',
            'boxNos.required' => '箱号不能为空!',
            'box_id.required' => '箱号id不能为空!',
            'w_id.required' => '仓库id不能为空!',
            'unique_code.required' => '店内码不能为空!',
            'sourceNo.required' => '关联单号不能为空!',
            'source_no.required' => '关联单号不能为空!',
            'code_type.in' => '导入方式错误!',
            'code_type.required' => '请选择导入方式!',
        ];
        $rules = [
            'boxNos' => 'required|array',
            'box_no' => 'string',
            'remark' => 'string',
            'code_type' => [
                'required',
                Rule::in([1,2,3,4])
            ],
            'code_list' => 'required|array',
            'box_id' => 'required',
            'w_id' => 'required',
            'is_export' => 'integer',
            'unique_code' => 'required',
            'sourceNo' => 'required',
            'source_no' => 'required'
        ];

        $secnes = [
            'quality' => ['boxNos','sourceNo'],
            'checkSignCode' => ['code_list','source_no','code_type','box_no'],
            'checkCodeByWarehouse' => ['w_id','unique_code'],
            'checkRegBoxNo' => ['boxNos'],
            'batchCheckCodeByWarehouse' => ['w_id'],
            'exportSignErrorList' => ['w_id'],
            'tempSignlist' => ['w_id'],
            'tempUnSignlist' => ['w_id','is_export'],
            'clearTempSignlist' => ['w_id'],
            'batchSign' => ['w_id'],
            'batchRegBox' => ['remark'],
            'signCode' => ['code_list','source_no','code_type','box_no'],
            'fileSign' => ['source_no','code_type'],
            'cancel' => ['box_id'],
        ];
        $useRule = [];
        if(isset($secnes[$secne])){
            foreach ($secnes[$secne] as $item){
                $useRule[$item] = $rules[$item];
            }
        }else{
            throw new BusinessException('验证场景值有误');
        }

        $validator = validate()->make(
            $data,
            $useRule,
            $message
        );

        return $validator->validate(); //验证数据有效性
    }

    /**
     * 获取单号下已装箱货品数量
     * @RequestMapping(path="/goodsPacking/getPackedGoodsTotalBySerialNo", methods="get,post")
     */
    public function getPackedGoodsTotalBySerialNo(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'task_type' => 'required|integer',
            'source_no' => 'required',
        ],[
            'task_type.required'=> '任务类型必填',
            'task_type.integer'=> '任务类型参数必须是正整数',
            'source_no.integer'=> '来源单号必填',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        // 登录用户
        $userInfo = $this->session->get('userInfo');

        try {
            $total = $this -> GoodsPackingService -> getPackedGoodsTotalBySerialNo($params['task_type'],$params['source_no']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['total' => $total]);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    /**
     * 从hash表里获取所有数据
     * @param $key
     * @param string $sortField 排序字段
     * @param string $sort 排序方向 默认 倒序
     * @param bool $needDecode 是都需要将数据解码，存入的是json时使用
     * @return array
     */
    private function getSortedDataAllFromHash($key, string $sortField,string $sort = 'desc', $needDecode = true)
    {
        $data = redis() -> hGetAll($key);
        logger() ->debug('排序前==1===',[$data]);

        if (!$data) {
            $data = [];
        }
        if ($needDecode) {
            foreach ($data as $k => &$v) {
                $v = json_decode($v, true);
            }
        }
        if ('asc' == $sort) {
            $data = collect($data) -> sortBy($sortField) -> toArray();
        }
        if ('desc' == $sort) {
            $data = collect($data) -> sortByDesc($sortField) -> toArray();
        }
        logger() ->debug('排序后==2===',[$data]);
        return $data;
    }

    /**
     * 为店内码装箱的数据追加epc
     * @RequestMapping(path="/goodsPacking/syncEpcCodeForUniqueCodeBox", methods="get,post")
     */
    public function syncEpcCodeForUniqueCodeBox(RequestInterface $request)
    {
        // 将上传的盘点数据存入redis
        $params = $request -> all();
        $validator = validate()->make($params, [
            'task_type' => 'required|integer',
            'source_no' => 'required|string',
        ],[
            'task_type.required'=> '来源单号类型必填',
            'task_type.integer'=> '来源单号必填必须是正整数',
            'source_no.required'=> '来源单号必填',
            'source_no.string'=> '来源单号必须是字符串',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $this -> GoodsPackingService -> syncEpcCodeForUniqueCodeBox($params['task_type'],$params['source_no']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),[]);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }
    }

    /**
     * 校验扫描的箱号或铅封号
     * @RequestMapping(path="/goodsPacking/checkBoxNoOrLeadSealNo", methods="get,post")
     */
    public function checkBoxNoOrLeadSealNo(RequestInterface $request)
    {
        // 将上传的盘点数据存入redis
        $params = $request -> all();
        $validator = validate()->make($params, [
            'no' => 'required|string',
        ],[
            'no.required'=> '单号必填',
            'no.string'=> '单号必须是字符串',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $res = $this -> GoodsPackingService -> checkBoxNoOrLeadSealNo([$params['no']]);
            if (!empty($res['exists'])) {
                return $this -> returnApi(ErrorCode::REQUEST_ERROR,'当前箱号同历史箱号或历史铅封重复，请选择其他箱号！','');
            }
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),'');
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }
    }

    /**
     * 校验扫描的箱号是否在指定来源单号下
     * @RequestMapping(path="/goodsPacking/checkBoxNoInSourceNo", methods="get,post")
     */
    public function checkBoxNoInSourceNo(RequestInterface $request)
    {
        // 将上传的盘点数据存入redis
        $params = $request -> all();
        $validator = validate()->make($params, [
            'no' => 'required|string',
            'source_no' => 'required|string',
        ],[
            'source_no.required'=> '来源单号必填',
            'source_no.string'=> '来源单号必须是字符串',
            'no.required'=> '单号必填',
            'no.string'=> '单号必须是字符串',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $res = $this -> GoodsPackingService -> checkBoxNoInSourceNo([$params['no']],$params['source_no']);
            if (!empty($res['not_exists'])) {
                return $this -> returnApi(ErrorCode::REQUEST_ERROR,'包裹号在列表里不存在！',$res);
            }
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$res);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }
    }

    /**
     * 校验扫描的箱号是否系统系统生成
     * @RequestMapping(path="/goodsPacking/checkIfBoxNoLegal", methods="get,post")
     */
    public function checkIfBoxNoLegal(RequestInterface $request)
    {
        // 将上传的盘点数据存入redis
        $params = $request -> all();
        $validator = validate()->make($params, [
            'box_no' => 'required|string',
        ],[
            'box_no.required'=> '箱号必填',
            'box_no.string'=> '箱号必须是字符串',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $res = $this -> _checkIfLeadSealRight($params['box_no']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['result' => $res ? 1 : 0]);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }
    }

    private function _checkIfLeadSealRight($boxNo) {
        if (empty($boxNo)) return false;
        // 以系统字符串前缀开始，为真
        var_dump('substr='.substr($boxNo,0,2));
        if ('ZX' == substr($boxNo,0,2)) { // 暂时先这么校验
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取铅封号的绑定信息
     * @RequestMapping(path="/goodsPacking/getLeadSealNosBindInfo", methods="get,post")
     */
    public function getLeadSealNosBindInfo(RequestInterface $request)
    {
        // 将上传的盘点数据存入redis
        $params = $request -> all();
        $validator = validate()->make($params, [
            'lead_seal_no' => 'required|string',
        ],[
            'lead_seal_no.required'=> '铅封号必填',
            'lead_seal_no.string'=> '铅封号必须是字符串',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $res = $this -> GoodsPackingService -> getLeadSealNosBindInfo([$params['lead_seal_no']]);
            if (!empty($res)) {
                foreach ($res as $v) {
                    if ($params['lead_seal_no'] == $v['lead_seal_no']) {
                        return $this -> returnApi(ErrorCode::REQUEST_ERROR,sprintf('当前铅封号已绑定了箱号 %s ，铅封号无效！',$v['box_no']),$res);
                    }
                }
            }
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$res);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }
    }

    /**
     * 保存称重与绑铅封信息
     * @RequestMapping(path="/goodsPacking/saveWeightAndLeadSealNo", methods="get,post")
     */
    public function saveWeightAndLeadSealNo(RequestInterface $request)
    {
        // 将上传的盘点数据存入redis
        $params = $request -> all();
        $validator = validate()->make($params, [
            'box_no' => 'required|string',
//            'weight' => 'required|string',
            'source_no' => 'required|string',
        ],[
            'box_no.required'=> '箱号必填',
            'box_no.string'=> '箱号必须是字符串',
            'source_no.required'=> '任务单号必填',
            'source_no.string'=> '任务单号必须是字符串',
//            'weight.required'=> '重量必填',
//            'weight.string'=> '重量必须是字符串',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $leadSealNo = $params['lead_seal_no'] ?? '';
        logger() -> debug('params==',[$params]);
        $userInfo = $this->session->get('userInfo');
        $adminInfo = [
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname']
        ];
        try {
            $res = $this -> GoodsPackingService -> saveWeightAndLeadSealNo($params['box_no'],$leadSealNo,$params['source_no'],$params['weight'],$adminInfo);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$res);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }
    }

    /**
     * 装箱打印次数+1
     * @RequestMapping(path="/goodsPacking/incrPrintTimes", methods="get,post")
     */
    public function incrPrintTimes(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'box_nos' => 'required|string',
        ],[
            'box_nos.required'=> '箱号必填',
            'box_nos.string'=> '箱号必须是字符串',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        logger() -> debug('装箱打印次数+1params==',[$params]);
        $boxNos = explode(',',$params['box_nos']);
        try {
            $res = $this -> GoodsPackingService -> incrPrintTimes($boxNos);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$res);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }
    }

    /**
     * 删除错下架缓存
     * @RequestMapping(path="/goodsPacking/deleteWrongOffData", methods="get,post")
     */
//    public function deleteWrongOffData(RequestInterface $request)
//    {
//        $params = $request -> all();
//        $validator = validate()->make($params, [
//            'task_type' => 'required|integer',
//            'source_no' => 'required|string',
//            'box_no' => 'required|string',
//            'gather_way' => 'required|integer',
//            'wrong_off_data' => 'required',
//        ],[
//            'task_type.required'=> '任务类型必填',
//            'task_type.integer'=> '任务类型参数必须是正整数',
//            'source_no.required'=> '来源单号必填',
//            'source_no.string'=> '来源单号必须是字符串',
//            'box_no.required'=> '箱号必填',
//            'box_no.string'=> '箱号必须是字符串',
//            'gather_way.required'=> '采集方式必选',
//            'gather_way.integer'=> '采集方式必须是正整数',
//            'wrong_off_data.required'=> '错下架数据必填',
//        ]);
//        if ($validator->fails()) {
//            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
//        }
//        // 登录用户
//        $userInfo = $this->session->get('userInfo');
//        $redisKey = $this -> _getRedisKey($userInfo['uid'],$params['task_type'],$params['gather_way'],$params['source_no'],'epc_code');
//
//        try {
//            $res = $this -> _deleteWrongOffData([
//                'redis_key' => $redisKey,
//                'wrong_off_data' => $redisKey,
//            ]);
//            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),[]);
//        } catch (\Exception $exception) {
//            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
//        }
//    }

    // 删除错下架数据
    private function _deleteWrongOffData($params) {
        $hashKey = $params['redis_key'] ?? '';
        $wrongOffData = $params['wrong_off_data'] ?? '';;
        if ($hashKey && $wrongOffData) {
            $hashKey = $hashKey.self::E_DATA_SUFFIX;
            // 删除错下架数据
            $pipe = redis() -> pipeline();
            foreach ($wrongOffData as $item) {
                $field = $item['box_no'].'-'.$this -> _getGoodsCode($item);
                logger() -> debug('删除错下架数据-hashkey======'.$hashKey);
                logger() -> debug('删除错下架数据-key======'.$field);
                $pipe -> hDel($hashKey,$field);
            }
            $pipe -> exec();
        }
    }

    /**
     * 批量打印
     * @RequestMapping(path="/goodsPacking/batchPrintBillList", methods="get,post")
     */
    public function batchPrintBillList(RequestInterface $request){

        $params = $request -> all();
        $ids = $params['ids'];
        var_dump('ids===',[$ids]);
        $detailList = [];
        if ($ids) {
            $ids = explode(',',$ids);
            foreach ($ids as $id) {
                $detailData = $this -> GoodsPackingService -> getGoodsPackingOne($id);
                array_push($detailList,$detailData);
            }
        } else {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,'请选择数据');
        }

        if (empty($detailList)) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,'无打印数据');
        }

        return $this -> show("/goodsPacking/batchPrintBillList",['detail_list' => $detailList,'ids' => $params['ids']]);
    }

    /**
     * 获取批量打印内容
     * @RequestMapping(path="/goodsPacking/getBatchPrintBillList", methods="get,post")
     */
    public function getBatchPrintBillList(RequestInterface $request){

        $params = $request -> all();
        $ids = $params['ids'];
        var_dump('ids===',[$ids]);
        $detailList = [];
        if ($ids) {
            $ids = explode(',',$ids);
            foreach ($ids as $id) {
                $detailData = $this -> GoodsPackingService -> getGoodsPackingOne($id);
                array_push($detailList,$detailData);
            }
        } else {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,'请选择数据');
        }

        if (empty($detailList)) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,'无打印数据');
        }

        try {
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$detailList);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 获取指定调拨单下的装箱人列表
     * @RequestMapping(path="/goodsPacking/getPkAdminList", methods="get,post")
     */
    public function getPkAdminList(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'source_no' => 'required|string',
        ],[
            'source_no.required'=> '来源单号必填',
            'source_no.string'=> '来源单号必须是字符串',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $res = $this -> GoodsPackingService -> getPkAdminList($params['source_no']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$res);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }
    }

    /**
     * 获取箱码打印相关信息
     * @RequestMapping(path="/goodsPacking/getBoxNoPrintInfo", methods="get,post")
     */
    public function getBoxNoPrintInfo(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'source_no' => 'required|string',
            'pk_ids' => 'required|array',
        ],[
            'source_no.required'=> '来源单号必填',
            'source_no.string'=> '来源单号必须是字符串',
            'pk_ids.required'=> '装箱id必填',
            'pk_ids.array'=> '装箱id必须是数组',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $res = $this -> GoodsPackingService -> getBoxNoPrintInfo($params['source_no'],$params['pk_ids']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$res);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }
    }

    /**
     * 获取箱码打印相关信息
     * @RequestMapping(path="/goodsPacking/getBackBoxNoPrintInfo", methods="get,post")
     */
    public function getBackBoxNoPrintInfo(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'box_no' => 'required|array',
        ],[
            'box_no.required'=> '箱号必填',
            'box_no.array'=> '箱号必须是数组',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $res = $this -> GoodsPackingService -> getBackBoxNoPrintInfo($params['box_no']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$res);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }
    }

    /**
     * 漏装箱列表
     * @RequestMapping(path="/goodsPacking/notBoxList", methods="get,post")
     */
    public function notBoxList(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'source_no' => 'required|string',
        ],[
            'source_no.required'=> '来源单号必填',
            'source_no.string'=> '来源单号必须是字符串',
        ]);
        $page = $params['page'] ?? 1;
        $size = $params['limit'] ?? 10;
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $export = $params['export'] ?? 0;
        try {
            $res = $this -> GoodsPackingService -> notBoxList($params,(int) $page,(int) $size,$export);
            // $export=2，标识获取全部数据
            if (2 == $export) {
                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$res['data']);
            }
            if (1 == $export) {
                $url = GoodsPackingCommon::exportNotBoxList($res['data']);
                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['url'=>$url]);
            }
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$res);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }
    }

    /**
     * 未见实物标记
     * @RequestMapping(path="/goodsPacking/notSeenMark", methods="get,post")
     */
    public function notSeenMark(RequestInterface $request)
    {
        $params = $request -> all();
        logger() -> debug('未见实物标记',[$params]);
        $validator = validate()->make($params, [
            'allot_id' => 'required|integer',
        ],[
            'allot_id.required'=> '调拨单ID必填',
            'allot_id.integer'=> '调拨单ID必须是整数',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $userInfo = $this -> session -> get('userInfo');
        $adminInfo = [
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname'],
        ];
        $token = RedisLock::lock(CacheData::getKey(CacheData::ZX_NOT_SEEN_MARK_LOCK,$adminInfo['admin_id']),CacheData::ZX_NOT_SEEN_MARK_LOCK_TIME);
        if($token === false) {
            throw new BusinessException("请求过于频繁，请稍后再试!");
        }
        try {

            $params['admin_info'] = $adminInfo;
            $res = $this -> GoodsPackingService -> notSeenMark($params);
            $uniqueCodes = join(',',array_column($params['data'], 'unique_code'));
            $logData = [
                'snow_id' => $this->request->getAttribute('snow_id'),
                'op_id' => $params['allot_id'],
                'req_router_name' => '标注未见实物', // 操作类型
                'model_name' => 'allot', // 操作模块
                'remark' => "店内码：{$uniqueCodes}", // 操作内容
            ];
            logger() -> debug('记录标注未见实物日志',[$logData]);
            wlog((string)$logData['snow_id'], $logData);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$res);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        } finally {
            RedisLock::unlock(CacheData::getKey(CacheData::ZX_NOT_SEEN_MARK_LOCK,$adminInfo['admin_id']),$token);
        }
    }

    /**
     * 批量打印漏装箱列表
     * @RequestMapping(path="/goodsPacking/batchPrintNotBoxList", methods="get,post")
     */
    public function batchPrintNotBoxList(RequestInterface $request){

        $params = $request -> all();
        logger() -> debug('批量打印漏装箱列表',[$params]);
        return $this -> show("/goodsPacking/batchPrintNotBoxList",['export' => $params['export'],'source_no' => $params['source_no'],'pick_order_detail_ids' => $params['pick_order_detail_ids']]);
    }

    public function _checkEmptyTag($goodsCodes)
    {
        logger() -> debug('校验空吊牌=====',[$goodsCodes]);
        // 校验是否空扣，不在过滤BB扣 - 20250717 -yj
        $uniqueCodes = array_column($goodsCodes,'unique_code');
        // 校验是否空扣
        if ($uniqueCodes) {
            $checkURes = $this -> GoodsPackingService -> _checkIsEmptyTag($uniqueCodes);
            logger() -> debug('校验空吊牌结果=====',[$checkURes]);
            if ($checkURes) {
                throw new BusinessException(sprintf('店内码 %s 是空吊牌，请先补打空吊牌再装箱！',join(',',array_column($checkURes,'unique_code'))));
            }
        }
        return $goodsCodes;
    }

}
