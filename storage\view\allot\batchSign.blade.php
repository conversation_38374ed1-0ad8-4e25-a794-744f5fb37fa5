<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/layui/css/layui.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/static/css/myui.css"/>
    <link href="/static/css/pearCommon.css" rel="stylesheet"/>
    <script src="/static/layui/layui.js" charset="utf-8"></script>
    <script src="/static/js/jquery.min.js"></script>
</head>
<style>
    .clear{ clear:both}
</style>

<body class="pear-container">
<div class="layui-card">
    <hr class="layui-bg-gray">
    <div class="layui-card-body layui-form layui-form-pane">
            <hr class="layui-bg-gray">
        <div class="layui-row">
            <form>
            <div class="layui-form-item">
                <label class="layui-form-label">调入仓</label>
                <div class="layui-input-inline" style="width: 60%">
                    <select name="w_id" id="w_id" lay-search lay-filter="w_id">
                        <option value="">请选择</option>
                        @if ($warehouse_list)
                            @foreach($warehouse_list as $warehouse_info)
                                <option value="{{$warehouse_info['id']}}">{{$warehouse_info['name']}}</option>
                            @endforeach
                        @endif
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-row">
                    <label class="layui-form-label">批量导入店内码</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <div class="layui-input-inline">
                            <input type="file" id="file" name="file" lay-verify="required" class="layui-input" >
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">文件格式：</label>
                            <div class="layui-input-block" style="padding: 8px 0px 8px 0px">
                                TXT、CSV
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">数据格式：</label>
                            <div class="layui-input-block" style="padding: 8px 0px 8px 0px">
                                {{$tplInfo['remark']}} <a href="{{$tplInfo['down_url']}}" style="color: #027DB4" target="_blank">模板下载</a>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-btn-group">
                            <button class="pear-btn pear-btn-md pear-btn-primary" type="button" data-id="{{$info['id']}}" id="file_import_btn" >
                                上传
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-row">
                    <label class="layui-form-label">扫码店内码</label>
                    <div class="layui-input-inline" style="width: 60%">
                    <input type="text" class="layui-input unique_code" id="unique_code" name="unique_code" placeholder="请扫描待签店内码" >
                        <span class="err_msg" style="color:#ff0000;font-weight:bold"></span>
                    </div>
                </div>
            </div>
                <div class="layui-form-item">
                    <div class="layui-row">
                        <label class="layui-form-label">EPC码</label>
                        <div class="layui-input-inline" style="width: 60%">
                            <div class="layui-col-xs6">
                                <input type="text" class="layui-input epc_code" id="epc_code" name="epc_code" placeholder="请扫描待签店内码" >
                                <span class="epc_err_msg" style="color:#ff0000;font-weight:bold"></span>
                            </div>
                            <div class="layui-col-xs6">
                                <input lay-filter="switchTest" type="checkbox" name="close" title="RFID开关" id="switchTest">
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="layui-card">
            <div class="layui-row"><button class="pear-btn pear-btn-md pear-btn-primary" style="margin: 10px 0 10px 150px;" id="import_btn" >
                    确认签收
                </button>
            </div>
        </div>
    </div>
</div>

<div class="layui-tab" lay-filter="tab-all">
    <ul class="layui-tab-title">
        <li class="layui-this">签收列表 <span id="sign_count"></span></li>
        <li>待签列表 <span id="unsign_count"></span></li>
    </ul>

    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show">
            <div class="layui-card">
                <div class="layui-card-body">
                    <table id="list" lay-filter="list"></table>
                </div>
            </div>
            <table class="layui-hide" id="goods_list"></table>
        </div>

        <div class="layui-tab-item">
            <div class="layui-card">
                <div class="layui-card-body">
                    <table id="unsign_list" lay-filter="unsign_list"></table>
                </div>
            </div>
        </div>

        <div class="clear"></div>
    </div>
</div>




<script type="text/html" id="lay-toolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm pear-btn-primary" lay-event="export">下载</button>
        <button class="layui-btn layui-btn-sm pear-btn-primary" lay-event="errorExport">错误下载</button>
        <button class="layui-btn layui-btn-sm pear-btn-primary" lay-event="unSignExport">未签收下载</button>
    </div>
</script>
<script type="text/html" id="detail_panel">
    <a class="pear-btn pear-btn-sm" lay-event="rePrint">补打</a>
</script>
<script src="http://127.0.0.1:8000/CLodopfuncs.js"></script>
<script>
    layui.config({
        base: '/static/js/' //自定义模块
    }).use(['element', 'table', 'comm','iframeTools'], function () {
        // Tab的切换功能，切换事件监听等，需要依赖element模块
        var $ = layui.jquery, table = layui.table, element = layui.element ,upload = layui.upload;
        var form = layui.form;
        var layer = layui.layer;
        var iframeTools = layui.iframeTools;
        var w_id = $('#w_id').val() ? $('#w_id').val():0;
        var comm = layui.comm

        var goodsCodes = []
        var finalGoodsCodes = [] // 最终要提交校验得epc
        var epcDom = $('#epc_code')
        var epcErrorDom = $('.epc_err_msg');

        var RFID_HOST = 'http://127.0.0.1:30010';//RFID IP
        var RFID_START_URL = RFID_HOST + '/api/Reader/connectAndStart';//开启RFID
        var RFID_STOP_URL = RFID_HOST + '/api/Reader/stopAndDisconnect';//暂停RFID
        var RFID_READ_URL = RFID_HOST + '/api/Reader/continueRead';//读取RFID数据地址


        $("#file").click(function (e) {
            const myFile = $("#file");
            // 触发
            // myFile.click()
            // 监听change事件
            myFile.unbind().change(function (e) {
                $("#file_import_btn").click();
            })
        })

        $("#file_import_btn").click(function () {
            if($("#file").val() == ''){
                layer.alert("上传文件不能为空！");
                return  false;
            }
            var wId = $("#w_id").val();

            if(!wId){
                layer.alert("请选择调入仓");
                return;
            }
            var formData = new FormData();
            formData.append('w_id', wId);
            formData.append('file', document.getElementById('file').files[0]);

            console.log(formData)
            $.ajax({
                url: '/goodsPacking/batchCheckCodeByWarehouse',
                type: 'post',
                data: formData,
                dataType: "json",
                cache: false, // 设置为false，上传文件不需要缓存。
                contentType: false, // 设置为false,因为是构造的FormData对象,所以这里设置为false
                processData: false,// 设置为false,因为data值是FormData对象，不需要对数据做处理
                success: function (data) {
                    if (data.code === 200) {
                        table.reload("list", {
                            page: {
                                curr: 1 //重新从第 1 页开始
                            },
                            where: {
                                w_id: wId
                            }
                        });
                        table.reload("unsign_list", {
                            where: {
                                w_id: wId
                            }
                        });
                        if(data.data.error_num > 0){
                            layer.alert('错误数据'+data.data.error_num+'条，详情请下载！');
                        }
                    } else {
                        layer.alert(data.msg);
                    }
                }
            }).always(function(){
                $("#file").val('')
            });;
            return false;
        })
        //表格初始化
        var dataTable = table.render({
            elem: '#list',
            url:'/goodsPacking/tempSignlist',
            where: {'w_id': w_id},
            method: 'post',
            page:true ,//开启分页
            skin: 'line',
            limits: [10,50,80,100],
            limit:50,
            toolbar: '#lay-toolbar',
            cols: [[
                {type:'numbers', width:80, title: 'ID'},
                {field:'unique_code', title: "店内码"},
                {field:'barcode', title: "条形码"},
                {field:'brand_name', title: "品牌"},
                {field:'category_name', title: "品类"},
                {field:'out_w_name', title: "调出仓"},
                {field:'serial_no', title: "调拨单号"},
                {field:'box_no', title: "箱号"},
                {field:'right', title: '操作', templet:"#detail_panel"}
            ]]
            ,defaultToolbar: [{
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print'],
            done: function (res, curr, count) {
                $('#sign_count').text("("+count+")");
            }
        });

        var unsignTable = table.render({
            elem: '#unsign_list',
            url:'/goodsPacking/tempUnSignlist',
            where: {'w_id': w_id},
            method: 'post',
            page:false ,//开启分页
            skin: 'line',
            toolbar: '#lay-toolbar',
            cols: [[
                {type:'numbers', width:80, title: 'ID'},
                {field:'unique_code', title: "店内码"},
                {field:'barcode', title: "条形码"},
                {field:'brand_name', title: "品牌"},
                {field:'category_name', title: "品类"},
                {field:'out_w_name', title: "调出仓"},
                {field:'serial_no', title: "调拨单号"},
                {field:'box_no', title: "箱号"},
            ]]
            ,defaultToolbar: [{
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print'],
            done: function (res, curr, count) {
                $('#unsign_count').text("("+count+")");
            }
        });

        //监听工具条
        table.on('tool(list)', function (obj) { //注：tool是工具条事件名，dataTable是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            if (layEvent === 'rePrint') {
                print(data.unique_code);
            }else if (layEvent === 'refresh') {
                refresh();
            }
        });

        table.on('toolbar(list)', function(obj){
            switch(obj.event){
                case 'export':
                    var wId = $("#w_id").val();
                    $.post('/goodsPacking/exportTempSignlist', {w_id:wId}, function (r) {
                        var loading = $(this).find('.layui-icon');
                        if (loading.is(":visible")) return;
                        loading.show();
                        if (r.code === 200) {
                            layer.alert(r.msg, function(index){
                                layer.close(index);
                                window.open(r.data.url);
                            });
                        } else {
                            layer.alert(r.msg);
                        }
                        loading.hide();
                    });
                    break;
                case 'errorExport':
                    var wId = $("#w_id").val();
                    $.post('/goodsPacking/exportSignErrorList', {w_id:wId}, function (r) {
                        var loading = $(this).find('.layui-icon');
                        if (loading.is(":visible")) return;
                        loading.show();
                        if (r.code === 200) {
                            layer.alert(r.msg, function(index){
                                layer.close(index);
                                window.open(r.data.url);
                            });
                        } else {
                            layer.alert(r.msg);
                        }
                        loading.hide();
                    });
                    break;
                case 'unSignExport':
                    var wId = $("#w_id").val();
                    $.post('/goodsPacking/tempUnSignlist', {w_id:wId,is_export:1}, function (r) {
                        var loading = $(this).find('.layui-icon');
                        if (loading.is(":visible")) return;
                        loading.show();
                        if (r.code === 200) {
                            layer.alert(r.msg, function(index){
                                layer.close(index);
                                window.open(r.data.url);
                            });
                        } else {
                            layer.alert(r.msg);
                        }
                        loading.hide();
                    });
                    break;
            };
        });

        $("#unique_code").keypress(function (e) {
            var code = e.which || e.keyCode;
            if(code == 13){
                uniqueChangeMonitor($(this).val().trim());
            }
        })

        $("input[name='import_type']").change(function () {
            var batch_type = $('input:radio[name="import_type"]:checked').val();
            if(batch_type  == 1){
                $("#barcode_panel").hide();
                $("#unique_panel").show();
            }else{
                $("#barcode_panel").show();
                $("#unique_panel").hide();
            }
        });

        form.on('select(w_id)', function (data) {
            var wId = $("#w_id").val();
            table.reload("list", {
                page: {
                    curr: 1 //重新从第 1 页开始
                },
                where: {
                    w_id: wId
                }
            });
            table.reload("unsign_list", {
                where: {
                    w_id: wId
                }
            });
        });

        function print(uniqueCode){
            LODOP.SET_LICENSES("","94D9E78FC9B721D1EBF7165B69114B39A35","","");

            LODOP.PRINT_INITA(0,0,188,113,"");
            LODOP.SET_PRINT_MODE("PRINT_NOCOLLATE",1);
            LODOP.ADD_PRINT_BARCODE(10,10,168,89,"128Auto",uniqueCode);
            LODOP.SET_PRINT_STYLEA(0,"FontSize",18);
            LODOP.SET_PRINT_STYLEA(0,"Horient",2);
            LODOP.SET_PRINT_STYLEA(0,"Vorient",2);
            LODOP.PRINT();
        }

        function uniqueChangeMonitor(uniqueCode){
            // var uniqueCode = $(input).val().trim();
            var wId = $("#w_id").val();
            if(!wId){
                layer.alert("请选择调入仓");
                return;
            }

            var params = {};
            params.unique_code = uniqueCode;
            params.w_id = wId;

            $.ajax({
                url: "/goodsPacking/checkCodeByWarehouse",
                type: 'post',
                data: JSON.stringify(params),
                dataType: "json",
                contentType: 'application/json',
                success: function (r) {
                    $(".unique_code").val("")
                    $('.err_msg').text("");
                    if (r.code === 200) {
                        table.reload("unsign_list", {
                            where: {
                                w_id: wId
                            }
                        });
                        table.reload("list", {
                            page: {
                                curr: 1 //重新从第 1 页开始
                            },
                            where: {
                                w_id: wId
                            }
                        });
                        print(uniqueCode);
                    }else {
                        $('.err_msg').text(r.msg);
                    }
                },
                statusCode: {
                    500: function(response) {
                        if(response.responseJSON.msg){
                            $(".unique_code").val("")
                            $('.err_msg').text(response.responseJSON.msg);
                        }else{
                            layer.msg('服务器有误。500!')
                        }
                    },
                    422: function(response) {
                        if(response.responseJSON.msg){
                            $(".unique_code").val("")
                            $('.err_msg').text(response.responseJSON.msg);
                        }else{
                            layer.msg('参数错误!')
                        }
                    }
                }
            });
        }

        $("#import_btn").click(function () {
            var wId = $("#w_id").val();
            $(this).addClass("layui-btn-disabled").attr("disabled",true);
            $.post('/goodsPacking/batchSign', {w_id:wId}, function (r) {
                table.reload("list", {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
                table.reload("unsign_list", {
                    where: {
                        w_id: wId
                    }
                });
                layer.alert(r.msg);
            }).always(function(){
                $('#import_btn').removeClass("layui-btn-disabled").attr("disabled",false);
            });
            return false;
        })




        form.on('checkbox(switchTest)', function (data) {
            console.log( data );　　//打印当前选择的信息
            if( data.elem.checked){　　　　　　//判断当前多选框是选中还是取消选中
                goodsCodes = []
                connectAndStart()
                continueRead()
            }else{
                goodsCodes = []
                $('#content').val('');
                stopAndDisconnect();
            }
            var value = data.value;   //获取选中的value值
        });

        /***************************  空格打开关闭rfid开关     ******************************/
        // 禁止按空格键时使用页面page down页面下翻滚动事件
        function PSDPD_KeyCheck(key) {
            if (key.target.nodeName == "INPUT" || key.target.nodeName == "TEXTAREA" || key.target.nodeName == "SELECT") return;
            if (key.target.hasAttribute("contenteditable") && key.target.getAttribute("contenteditable") == "true") return;
            if (key.ctrlKey || key.altKey || key.metaKey) return;
            if (key.key == ' ') {
                key.stopPropagation();
                key.preventDefault();
                return false;
            }
        }

        document.addEventListener('keydown', PSDPD_KeyCheck);

        $(document).keyup(function (event) {
            if (event.keyCode === 32) {
                if ($('#switchTest').is(':checked')) {
                    console.log('空格执行关闭')
                    layui.use('form', function () {
                        $('#switchTest').prop("checked", false);
                        form.render();
                    });
                    // goodsCodes = []
                    //submitQuery({epc_code: epcDom.val()})
                    //submitQuery(form.val('form'))
                    epcDom.val('');
                    stopAndDisconnect();
                } else {
                    console.log('空格执行开启')
                    layui.use('form', function () {
                        $('#switchTest').prop("checked", true);
                        form.render();
                    });
                    // goodsCodes = []
                    epcDom.val('');
                    connectAndStart()
                    continueRead()
                }
            }
        });

        /***************************     RFID获取     ******************************/
        var timer;
        function stopAndDisconnect() {
            clearInterval(timer);
            curlPost(RFID_STOP_URL)
        }

        function connectAndStart() {
            curlPost(RFID_START_URL);
        }

        function continueRead() {
            console.log('持续读取')
            timer=setInterval(Read,800);
        }

        function Read() {
            console.log('读取')
            curlPost(RFID_READ_URL);
            console.log(RFID_READ_URL)
        }

        function curlPost(url,params) {
            console.log(url);
            $.ajax({
                url: url,
                type: 'POST',
                data:{StartReadParam:{MemoryBank:'EPC',ExcludeDuplication:'true'}},
                dataType: 'JSON',
                success: function (result) {
                    console.log('epc请求success结果==',result);
                    if ('boolean' != typeof (result)) {
                        var epcRes = comm.array_unique(comm.array_column(result.Data, 'Epc'))
                        console.log('epcRes====================', comm.array_unique(epcRes))
                        var content_epc_info = epcDom.val()

                        if (epcRes.length > 1) {
                            // epcErrorDom.text("无法识别多个磁扣")
                            layer.msg('无法识别多个磁扣',{icon:2,time:1500})
                            return;
                        }

                        // if (content_epc_info !== epcRes[0]){
                            content_epc_info = epcRes[0]
                            epcDom.val(content_epc_info)
                            if (content_epc_info){
                                $.post('/epc/bindInfo', {epc_code:content_epc_info}, function (r) {
                                    console.log(r)
                                    if (r.code === 200) {
                                        if (r.data.unique_code) {
                                            uniqueChangeMonitor(r.data.unique_code)
                                        }else{
                                            // epcErrorDom.text("当前磁扣是空扣")
                                            layer.msg('当前磁扣是空扣',{icon:2,time:1500})
                                        }

                                    } else {
                                        layer.msg(r.msg,{icon:2,time:1500})
                                        // layer.alert(r.msg);
                                    }
                                });
                            }
                        // }
                    }

                },
                error: function (error) {
                    console.log(typeof (error));
                    console.log('epc请求结果fail==',error);
                    layer.msg('设备读取EPC码失败',{icon:2,time:1500})
                }
            });
        }
    })
</script>
</body>

</html>
