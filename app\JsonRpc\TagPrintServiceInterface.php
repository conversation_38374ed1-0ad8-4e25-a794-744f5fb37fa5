<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * Interface TagPrintServiceInterface
 * @package App\JsonRpc
 */
interface TagPrintServiceInterface
{
    public function print(array $params);

    /**
     * 返回吊牌信息
     * @param int $sku_id
     * @param array $extend [code=>店内码/条码, code_type=>1/2]
     * @return array
     */
    public function getTagData(int $sku_id, array $extend);

    /**
     * 保存打印吊牌日志
     * @param array $data
     * @return bool
     */
    public function createPrintLog(array $data);

    /**
     * 获取吊牌打印记录和装箱信息
     * @param array $params
     * @param int $perPage
     * @param int $currentPage
     * @return array
     */
    public function getPrintLog(array $params);

    /**
     * 绑定epc
     * @param array $params
     */
    public function bindEpc(array $params);

    /**
     * rfid吊牌打印，返回吊牌信息
     * @param array $params
     * @return array
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function printTag(array $params);

    /**
     * 检测epc是否被绑定
     * @param array $params
     */
    public function checkEpcBind(array $params);

    /**
     * rfid吊牌打印，返回吊牌信息
     * @param array $params
     * @return array
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function printFrontTag(array $params);

    public function getPrintList(array $params);

    /**
     * 将店内码和rfid绑定,并入库  单个
     * @return bool
     */
    public function frontBindEpcInStore(array $params);

    /**
     * 将店内码入库 多个
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function batchRrontInStore(array $params);

    /**
     * 获取空吊牌列表
     */
    public function kongList(array $params);

    public function getPrintWIds(array $params);

    //获取空吊牌对应的仓库
    public function emptyTagWids();
}