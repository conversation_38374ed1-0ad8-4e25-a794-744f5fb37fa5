<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <link href="/static/layui/css/layui.css" rel="stylesheet"/>
    <link href="/static/css/pearCommon.css" rel="stylesheet"/>
</head>
<style>
    .clear {
        clear: both
    }

    .table_01 {
        text-align: center;
        margin: 10px;
        width: 90%;
    }

    .font_nums {
        font-size: 18px;
        font-weight: bold;
    }

    table, th, td {
        border: 1px solid rgba(0, 0, 0, .1);
    }

    .fail_info {
        color: red;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }

    .success_info {
        color: green;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }

    .layui-card-body {
        position: static !important;
    }
</style>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="">
            <div class="layui-form-item">
                <label class="layui-form-label">扫描:</label>
                <div class="layui-input-inline" style="width: 100px !important;">
                    <select name="make_type" id="make_type">
                        <option value="unique_code" selected>店内码</option>
                        <option value="order_code">订单号</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <input type="text" id="code" name="code" placeholder="" class="layui-input">
                </div>
                <div style="float:left;">
                    <button class="pear-btn pear-btn-md pear-btn-primary" id="check_out" lay-submit lay-filter="query">
                        <i class="layui-icon layui-icon-search"></i>
                        查询
                    </button>
                    <button class="pear-btn pear-btn-md" id="reset" type="reset" lay-submit lay-filter="reset">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                </div>
                <div id="pick_order_source" style="float:left;line-height: 36px; color: #ff0000; margin-left: 40px; font-size: 20px; font-weight: bold;"></div>
            </div>
            <div class="layui-form-item">
                <div id="pick_order_error" style="float:left;line-height: 36px; margin-left: 20%; font-size: 20px; font-weight: bold;"></div>
            </div>
        </form>
    </div>
</div>
<div class="layui-card" id="order_info">
</div>

<script src="/static/layui/layui.js"></script>
<script src="http://127.0.0.1:8000/CLodopfuncs.js"></script>
<script type="text/javascript">
    layui.use(['jquery', 'table', 'element', 'form', 'layer', 'iframeTools', 'laytpl'], function () {
        var form = layui.form;
        var table = layui.table;
        var $ = layui.jquery;
        var element = layui.element;// Tab的切换功能，切换事件监听等，需要依赖element模块
        var layer = layui.layer;
        var iframeTools = layui.iframeTools;
        var laytpl = layui.laytpl;
        $("#code").focus();

        //查询
        form.on('submit(query)', function (data) {
            $.ajax({
                url: '/order/get_make',
                type: 'post',
                data: {
                    make_type: data.field.make_type,
                    code: data.field.code
                },
                dataType: 'JSON',
                success: function (result) {
                    if (result.code == 200) {
                        $("#pick_order_error").html(data.field.code);
                        $("#pick_order_error").css('color','green');
                        $("#order_info").html(result.data.html);
                        $("#pick_order_source").html(result.data.order_source);
                        $("#code").val('');
                        var making_info = form.val("making_info");
                        if (result.data.print_ticket){
                            print_ticket(making_info);
                        }
                        if (result.data.print_express){
                            $("#pick-order-time").focus();
                            print_express(making_info);
                        }
                        form.render();
                    }else if(result.code == 400){
                        $("#pick_order_error").html(data.field.code + '  ' + result.msg);
                        $("#pick_order_error").css('color','red');
                        $("#order_info").html(result.data.html);
                        $("#pick_order_source").html(result.data.order_source);
                        $("#code").val('');
                        form.render();
                    } else {
                        $("#pick_order_error").html(data.field.code +'  '+ result.msg);
                        $("#pick_order_error").css('color','red');
                        $("#code").val('');
                        $("#code").focus();
                    }
                },
                error: function () {
                    layer.alert(JSON.stringify('服务器相应错误'));
                }
            });
            return false;
        });

        //重置
        form.on('submit(reset)', function (data) {
            order_code = $('#order_code').val();
            if (order_code != '' && typeof(order_code) != 'undefined'){
                $.ajax({
                    url: '/order/reset_make',
                    type: 'post',
                    data: {
                        order_code: order_code
                    },
                    dataType: 'JSON',
                    success: function (result) {
                        if (result.code == 200) {
                            window.location.reload();
                        } else {
                            layer.alert(JSON.stringify(result.msg));
                        }
                    },
                    error: function () {
                        layer.alert(JSON.stringify('服务器相应错误'));
                    }
                });
            }
        });

        //查看订单详情
        $(document).on('click','.order_code', function (data) {
            var order_code = $(this).attr('data-code');
            iframeTools.layer_iframe('订单详情', '/order/detail/' + order_code);
            return false;
        });

        //加入制单区
        form.on('submit(add_make)', function (data) {
            $.ajax({
                url: '/order/add_make',
                type: 'post',
                data: data.field,
                dataType: 'JSON',
                success: function (result) {
                    if (result.code == 200) {
                        $("#order_info").html(result.data.html);
                        var making_info = form.val("making_info");
                        if (result.data.print_ticket){
                            print_ticket(making_info);
                        }
                        if (result.data.print_express){
                            print_express(making_info);
                        }
                        form.render();
                    }else{
                        layer.alert(result.msg);
                    }
                },
                error: function () {
                    layer.alert(JSON.stringify('服务器相应错误'));
                }
            });
            return false;
        });

        //删除商品
        $(document).on( 'click', '.del', function (data) {
            var unique_code = $(this).attr('data-code');
            var order_code = $(this).attr('data-content');
            $.ajax({
                url: '/order/del_make',
                type: 'post',
                data: {
                    unique_code:unique_code,
                    order_code:order_code
                },
                dataType: 'JSON',
                success: function (result) {
                    if (result.code == 200) {
                        $("#order_info").html(result.data);
                        form.render();
                    }else{
                        layer.alert(JSON.stringify(result.msg));
                    }
                },
                error: function () {
                    layer.alert(JSON.stringify('服务器相应错误'));
                }
            });
        });

        //作废发货记录
        $(document).on('click', '.cancel', function (data) {
            var express_code = $(this).attr('data-express');
            layer.confirm('是否作废发货记录？', function (index) {
                // var load = layer.load();
                $.post("/order/cancel_express", {express_code: express_code}, function (res) {
                    if (res.code == 200) {
                        layer.msg(res.msg, {icon: 1, time: 500}, function () {
                            window.location.reload();
                        })
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 1000})
                    }
                });
            });
            return false;
        });

        //打印购物小票
        form.on('submit(print_ticket)', function (data) {
            print_ticket(data.field);
            return false;
        });

        //补打购物小票
        form.on('submit(late_print_ticket)', function (data) {
            print_ticket(data.field);
            return false;
        });

        //打印快递面单
        form.on('submit(print_express)', function (data) {
            print_express(data.field);
            return false;
        });

        //补打印快递面单
        form.on('submit(late_print_express)', function (data) {
            doConnect();
            $.ajax({
                url: '/order/get_late_print_express',
                type: 'post',
                data: data.field,
                dataType: 'JSON',
                success: function (result) {
                    // console.log(result.data);
                    if (result.code == 200) {
                        do_print('', result.data);
                    }else{
                        layer.alert(JSON.stringify(result.msg));
                    }
                }
            })
            return false;
        });

        //全部打印
        form.on('submit(print_all)', function (data) {
            //打印小票
            if(data.field.source != 5){
                print_ticket(data.field);
            }
            //打印快递面单
            print_express(data.field);
            return false;
        });

        //打印购物小票模板
        function print_ticket(field_info){
            $.ajax({
                url: '/order/get_order_print',
                type: 'post',
                data: field_info,
                dataType: 'JSON',
                success: function (result) {
                    if (result.code == 200) {
                        var line_height=0;
                        var de_hight=0.2;
                        //整体页面设置
                        //LODOP=getLodop(document.getElementById('LODOP'),document.getElementById('LODOP_EM'));
                        LODOP.SET_LICENSES("","94D9E78FC9B721D1EBF7165B69114B39A35","","");
                        LODOP.PRINT_INITA(de_hight+"cm","0.00cm","7.90cm","15.00cm","好超值发货单打印任务");
                        LODOP.SET_PRINT_PAGESIZE(3,'7.90cm','0.45cm',"");
                        LODOP.SET_PRINT_STYLE("FontSize",8);
                        LODOP.SET_PRINT_STYLE("FontName", "微软雅黑");
                        LODOP.SET_PRINT_COPIES(1);

                        //组织打印数据
                        //页面元素
                        LODOP.ADD_PRINT_IMAGE(de_hight+"cm","2cm","3cm","2.1cm","<img border='0' src='/static/images/logo_417.png'>");
                        LODOP.SET_PRINT_STYLEA(0,"Stretch",2);
                        LODOP.SET_PRINT_STYLEA(0,"Alignment",2);
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",15);

                        de_hight+=2.2;
                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","0.2cm","6.5cm","0.70cm","BIGOFFS发货单");
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",16);
                        LODOP.SET_PRINT_STYLEA(0,"Alignment",2);

                        //订单条码
                        de_hight+=1;
                        LODOP.ADD_PRINT_BARCODE(de_hight+"cm","0.9cm","7cm","1cm","128Auto",result.data.order_info.branch_serial_no);

                        de_hight+=1.5;
                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","0.1cm","2.00cm","0.30cm","订单号：");
                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","1.7cm","5.00cm","0.30cm",result.data.order_info.branch_serial_no);
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",9);

                        // de_hight+=0.5;
                        // LODOP.ADD_PRINT_TEXT(de_hight+"cm","0.1cm","2.00cm","0.30cm","流水号：");
                        // LODOP.ADD_PRINT_TEXT(de_hight+"cm","1.7cm","5.00cm","0.30cm","O022101280000000039");
                        // LODOP.SET_PRINT_STYLEA(0,"FontSize",9);

                        de_hight+=0.5;
                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","0.1cm","2.00cm","0.30cm","下单时间：");
                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","1.7cm","5.00cm","0.30cm",result.data.order_info.add_time);
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",9);

                        //横线
                        de_hight+=1;
                        LODOP.ADD_PRINT_LINE(de_hight+"cm","0.1cm",de_hight+"cm","9cm",2,1);

                        de_hight+=0.5;
                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","0.1cm","2.00cm","0.30cm","商品");
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",9);
                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","6.0cm","2.00cm","0.30cm","数量");
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",9);

                        de_hight+=0.5;
                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","0.1cm","2.00cm","0.30cm","规格");
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",9);

                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","6.0cm","2.00cm","0.30cm","单价");
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",9);

                        $.each(result.data.detail_list,function(key,val) {
                            //商品名称
                            de_hight += 0.8;
                            LODOP.ADD_PRINT_TEXT(de_hight + "cm", "0.1cm", "10cm", "0.30cm", val.goods_name);
                            LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                            LODOP.SET_PRINT_STYLEA(0,"FontName", "宋体");

                            //条码
                            de_hight += 0.5;
                            LODOP.ADD_PRINT_TEXT(de_hight + "cm", "0.1cm", "10cm", "0.30cm", val.barcode);
                            LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                            LODOP.SET_PRINT_STYLEA(0,"FontName", "宋体");

                            //数量
                            LODOP.ADD_PRINT_TEXT(de_hight + "cm", "6.0cm", "2.00cm", "0.30cm", val.nums);
                            LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                            LODOP.SET_PRINT_STYLEA(0,"FontName", "宋体");

                            //规格
                            de_hight += 0.5;
                            LODOP.ADD_PRINT_TEXT(de_hight + "cm", "0.1cm", "8cm", "0.30cm", val.spec_name);
                            LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                            LODOP.SET_PRINT_STYLEA(0,"FontName", "宋体");

                            //单价
                            LODOP.ADD_PRINT_TEXT(de_hight + "cm", "6.0cm", "2.00cm", "0.30cm", val.sale_price);
                            LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                            LODOP.SET_PRINT_STYLEA(0,"FontName", "宋体");

                        });


                        //横线
                        de_hight+=1;
                        LODOP.ADD_PRINT_LINE(de_hight+"cm","0.1cm",de_hight+"cm","9cm",2,1);


                        //收件人
                        de_hight+=0.5;
                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","0.1cm","7.9cm","0.30cm","收件人："+result.data.order_info.receiving_name);
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",12);

                        //联系方式
                        de_hight+=0.6;
                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","0.1cm","7.9cm","0.30cm","联系方式："+result.data.order_info.receiving_phone);
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",12);

                        //买家备注
                        de_hight+=0.6;
                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","0.1cm","7.9cm","0.30cm","买家备注："+result.data.order_info.remark);
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",12);

                        //收货地址
                        de_hight+=0.6;
                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","0.1cm","7.4cm","0.85cm","收货地址："+result.data.order_info.receiving_address);
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",12);

                        //横线
                        de_hight+=1.55;
                        LODOP.ADD_PRINT_LINE(de_hight+"cm","0.1cm",de_hight+"cm","9cm",2,1);

                        de_hight+=0.5;
                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","0.5cm","7.15cm","0.85cm","感谢您对BIGOFFS的支持与理解！");
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",12);

                        //小程序二维码
                        LODOP.ADD_PRINT_BARCODE(de_hight+0.8+"cm","0.5cm","3.3cm","3.3cm","QRCode","https://mp.weixin.qq.com/a/~~V5B-ixalD1Q~6-w5tdRXSyLWNAMyfmCRFA~~");

                        LODOP.ADD_PRINT_TEXT(de_hight+1.35+"cm","3.8cm","4cm","1cm","BIGOFFS小程序");
                        LODOP.SET_PRINT_STYLEA(0,"Alignment",1);
                        LODOP.SET_PRINT_STYLEA(0,"FontName", "宋体");
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",9);

                        LODOP.ADD_PRINT_TEXT(de_hight+1.95+"cm","3.8cm","4cm","1cm","如需帮助，敬请关注");
                        LODOP.SET_PRINT_STYLEA(0,"Alignment",1);
                        LODOP.SET_PRINT_STYLEA(0,"FontName", "宋体");
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",9);

                        LODOP.ADD_PRINT_TEXT(de_hight+2.55+"cm","3.8cm","4cm","1cm","我们将竭诚为您服务！");
                        LODOP.SET_PRINT_STYLEA(0,"Alignment",1);
                        LODOP.SET_PRINT_STYLEA(0,"FontName", "宋体");
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",9);

                        de_hight+=4;
                        LODOP.ADD_PRINT_TEXT(de_hight+"cm","0.1cm","7.00cm","1.00cm",".");
                        LODOP.SET_PRINT_STYLEA(0,"Alignment",2);

                        LODOP.SET_PRINTER_INDEXA("bigoffs_xiaopiao");
                        // LODOP.PREVIEW();
                        LODOP.PRINT();
                    } else {
                        layer.alert(JSON.stringify(result.msg));
                    }
                },
                error: function () {
                    layer.alert(JSON.stringify('服务器相应错误'));
                }
            });
        }

        //获取快递面单信息
        function print_express(field_info){
            if (Object.keys(field_info).length <= 3){
                layer.alert('制单区为空，请确认后再打印');
            }else{
                doConnect();
                $.ajax({
                    url: '/order/get_order_express',
                    type: 'post',
                    data: field_info,
                    dataType: 'JSON',
                    success: function (result) {
                        if (result.code == 200) {
                            do_print('', result.data);
                        }else{
                            layer.alert(JSON.stringify(result.msg));
                        }
                    }
                })
            }
        }

        //确认发货
        form.on('submit(delivery)', function (data) {
            // layer.confirm('是否确认发货？', function (index) {
            //     // var load = layer.load();
            //     $.post("/order/confirm_delivery", data.field, function (res) {
            //         if (res.code == 200) {
            //             layer.msg(res.msg, {icon: 1, time: 500}, function () {
            //                 window.location.reload();
            //             })
            //         } else {
            //             layer.msg(res.msg,{icon: 1, time: 500})
            //         }
            //     });
            // });



            layer.confirm('是否确认发货？',{
                    btn:['确认','取消'],
                    success:function(){
                        this.enterEsc = function (event) {
                            if (event.keyCode === 13) {
                                $(".layui-layer-btn0").click();
                                return false; //阻止系统默认回车事件
                            }else if(event.keyCode == 27){
                                $(".layui-layer-btn1").click();
                                return false;
                            }
                        };
                        $(document).on('keydown', this.enterEsc); //监听键盘事件，关闭层
                    },
                    end:function(){
                        $(document).off('keydown',this.enterEsc); //解除键盘关闭事件
                    }
                },
                function (index) {//点击确定后执行的方法体
                    //这里可以写需要处理的流程
                    $.post("/order/confirm_delivery", data.field, function (res) {
                        if (res.code == 200) {
                            layer.msg(res.msg, {icon: 1, time: 500}, function () {
                                window.location.reload();
                            })
                        } else {
                            layer.msg(res.msg,{icon: 2, time: 800})
                        }
                    });
                    layer.close(index);    //执行完后关闭
                });
                return false;

        });

        //电子面单打印
        function doConnect()
        {
            socket = new WebSocket('ws://localhost:13528');
            //如果是https的话，端口是13529
            // socket = new WebSocket('wss://localhost:13529');
            // 打开Socket
            socket.onopen = function(event)
            {
                // 监听消息
                socket.onmessage = function(event)
                {
                    console.log('Client received a message',event);
                };
                // 监听Socket的关闭
                socket.onclose = function(event)
                {
                    console.log('Client notified socket has closed',event);
                };
            };
        }

        /**
         * 获取请求的UUID，指定长度和进制,如
         * get_UUID(8, 2)   //"01001010" 8 character (base=2)
         * get_UUID(8, 10) // "47473046" 8 character ID (base=10)
         * get_UUID(8, 16) // "098F4D35"。 8 character ID (base=16)
         *
         */
        function get_UUID(len, radix) {
            var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
            var uuid = [], i;
            radix = radix || chars.length;
            if (len) {
                for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random()*radix];
            } else {
                var r;
                uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
                uuid[14] = '4';
                for (i = 0; i < 36; i++) {
                    if (!uuid[i]) {
                        r = 0 | Math.random()*16;
                        uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
                    }
                }
            }
            return uuid.join('');
        }

        /***
         * 构造request对象
         */
        function get_request_object(cmd) {
            var request  = new Object();
            request.requestID=get_UUID(8, 16);
            request.version="1.0";
            request.cmd=cmd;
            return request;
        }

        /**
         * 打印电子面单
         * printer 指定要使用那台打印机
         * waybillArray 要打印的电子面单的数组
         */
        function do_print( printer, data)
        {
            var request = get_request_object("print");
            request.task = new Object();
            request.task.taskID = get_UUID(8,10);
            request.task.preview = false;
            request.task.printer = printer;
            var documents = new Array();
            var doc = new Object();
            doc.documentID = data.express_code;
            var content = new Array();
            content.push(data.print_data);
            doc.contents = content;
            documents.push(doc);
            request.task.documents = documents;
            // console.log(request);
            socket.send(JSON.stringify(request));
        }

    })
</script>
</body>
</html>
