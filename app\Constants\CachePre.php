<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Constants;

use Hyperf\Constants\AbstractConstants;

/**
 * 缓存前缀
 * Class CachePre
 * @package App\Constants
 */
class CachePre
{

    const INSTORE_LIST_DATA = 'INSTORE:LIST:%d:%d';//采购入库导入商品数据 INSTORE:LIST:用户名id:任务单id
    const INSTORE_DIFFLIST_DATA = 'INSTORE:DIFFLIST:%d:%d';//采购入库导入差异商品数据 INSTORE:DIFFLIST:用户名id:任务单id
    const INSTORE_TOTAL_DATA = 'INSTORE:TOTAL:%d:%d';//采购入库导入商品数据 INSTORE:TOTAL:用户名id:任务单id
    const ALLOT_BATCH_IN_DATA = 'ALLOT:BATCHIN:DATA:%d';//调拨批量入库数据 INSTORE:TOTAL:用户名id:任务单id
    const BATCH_CHANGE_BARCODE = 'BATCH_CHANGE_BARCODE:%d';//调拨批量入库数据 INSTORE:TOTAL:用户名id
    const BATCH_SIGN_LIST = 'BATCH_SIGN_LIST:%d:%s';//调拨签收数据 BATCH_SIGN_LIST:用户名id:入库仓id
    const BATCH_UNSIGN_LIST = 'BATCH_UNSIGN_LIST:%d:%s';//调拨未签收数据 BATCH_UNSIGN_LIST:用户名id:入库仓id
    const BATCH_REG_LIST = 'BATCH_REG_LIST:%d';//调拨箱号登记数据 BATCH_REG_LIST:用户名id
    const EMAIL_CACHE_DATA = 'EMAIL_CACHE_DATA:%s:%s';//调拨批量入库数据 BATCH_SIGN_LIST:用户名id:单据类型
    const REBACK_HANDOVER_ADD_LIST = 'REBACK_HANDOVER_ADD_LIST:%d:%s';//回库交接-发起 REBACK_HANDOVER_ADD_LIST:用户名id:入库仓id
    const REBACK_HANDOVER_TAKE_LIST = 'REBACK_HANDOVER_TAKE_LIST:%d:%s';//回库交接-接收 REBACK_HANDOVER_TAKE_LIST:用户名id:入库仓id
    const OUT_STOCK_LOCK = 'OUT_STOCK_LOCK:%d:%s';//货品出库锁 OUT_STOCK_LOCK:用户名id:单号

    const CACHE_ONE_HOUR = 3600;
    const CACHE_ONE_DAY = 3600;

    public static function getKey(string $preTpl,... $params){
        return sprintf($preTpl,... $params);
    }
}
