<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>吊牌打印</title>
    <link href="/static/layui/css/layui.css" rel="stylesheet"/>
    <link href="/static/css/pearCommon.css" rel="stylesheet"/>
    <link href="/static/css/myui.css" rel="stylesheet"/>
</head>
<style>
    .clear {
        clear: both
    }

    .table_01 {
        text-align: center;
        margin: 10px;
        width: 90%;
    }

    .font_nums {
        font-size: 18px;
        font-weight: bold;
    }

    table, th, td {
        border: 1px solid rgba(0, 0, 0, .1);
    }

    .fail_info {
        color: red;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }

    .success_info {
        color: green;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }

    .layui-card-body {
        position: static !important;
    }
    .grid-demo{margin: 20px;}
    .grid-demo_1{margin: 10px;}
    .lable_form{font-size: 18px; font-weight: 600; width: 120px !important; color: #333;}
    .lable_font{font-size: 18px; font-weight: 600; width: 300px !important; color: #666; text-align:left !important;}
    .lable_nums{font-size: 18px; font-weight: 600; color: #5b5b5b;}

    #allot_no{display: none;}
    #in_store_type_div{display: none;}
    #in_store_print{display: none;}
    #in_store_print_rfid{display: none;}
    #allot_print_rfid{display: none;}
    .input_font{font-weight: 600; font-size: 18px;}
    .layui-form-select{position: relative; font-size: 18px; font-weight: 600;}
    .layui-table th{font-size: 18px !important; font-weight: 600 !important; text-align: center;}
    .layui-table tr{font-size: 18px !important; font-weight: 600 !important; text-align: center;}
    .required_sign{color: red; padding-right: 5px;}
    #check_unique_info{display: none; font-size: 20px; font-weight: 600; width: 300px !important; color: rgb(0, 191, 160); text-align:left !important;}
    #print_tag_info{display: none}
    #allot_barcode_print{display: none;}
    #allot_barcode_print_rfid{display: none;}
    #allot_unique_code_print{display: none;}
    #allot_unique_code_print_rfid{display: none;}
    #allot_unique_code_aili_print_bind_rfid{display: none;}
    #in_store_barcode_aili_print_rfid{display: none;}
    #in_store_barcode_print{display: none;}
    #in_store_barcode_print_rfid{display: none;}
    #epc_switch{position: absolute; margin-top:2.5%; right:13%; z-index: 1;}
    #epc_switch_rfid{position: absolute; margin-top:8.9%; right:13%; z-index: 1;}
    #epc_switch_barcode{position: absolute; margin-top:8.7%; right:13%; z-index: 1;}
    #epc_switch_barcode_rfid{position: absolute; margin-top:15.1%; right:13%; z-index: 1;}
    #print_price_nums{padding-top: 10px; display: none;}
    .wait_bind{font-size: 18px;font-weight: 800;color: rgb(64, 158, 255);}

    .layui-form-checkbox[lay-skin="primary"] span {
        color: #f00 !important;
        font-size: 15px;
        font-weight: bold;
    }


</style>
<body class="pear-container">
<div id="print_price_nums">
    <label class="layui-form-label lable_form">打印数量</label>
    <input style="font-size: 30px;margin-right: -15px;" type="button" class="fl rate_ul_r_jian layui-btn layui-btn-primary" value="-"></input>
    <input style="font-size: 20px; width: 100px;" class="rate_ul_r_input layui-btn layui-btn-primary" type="text" name="print_barcode_num" id="print_barcode_num" value=1><span class="rate_ul_r_icon"></span>
    <input style="font-size: 30px; margin-left: -5px;" type="button" class="fr rate_ul_r_jia layui-btn layui-btn-primary" value="+"></input>
</div>

<form class="layui-form" lay-filter='tag_form' action="">
    <div class="layui-bg-gray" style="padding: 10px;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md5">
                <div class="layui-card">
                    <div class="layui-card-header">一、生产配置</div>
                    <div class="layui-card-body">
                        <div class="layui-row" style="background-color: #e8e2e23b;padding: 20px 0px 20px 0px;">
                            <div class="grid-demo grid-demo-bg3">
                                <div class="layui-input-inline"><label class="layui-form-label lable_form"><span class="required_sign">*</span>吊牌模版</label></div>
                                <div class="layui-input-inline">
                                    <select name="temp_type" id="temp_type" lay-filter="temp_type" class="input_font" lay-verify="temp_type">
                                        <option value="" selected>请选择吊牌模版</option>
                                        @if ($template_type)
                                            @foreach ($template_type as $key => $value)
                                                <option selected = "{{$value['checked']}}" value="{{ $value['type'] }}">{{ $value["name"] }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                            <div class="grid-demo grid-demo-bg3">
                                <div class="layui-input-inline"><label class="layui-form-label lable_form">当前模板</label></div>
                                <div class="layui-input-inline" style="width:300px !important;">
                                    <label class="layui-form-label lable_form" style="width: 220px !important;" id="temp_name">
                                        @if ($template_type)
                                            @foreach ($template_type as $key => $value)
                                                @if($value['checked'])
                                                    {{ $value["name"] }}
                                                @endif
                                            @endforeach
                                        @endif
                                    </label>
                                </div>
                            </div>
                            <div class="grid-demo grid-demo-bg3">
                                <div class="layui-input-inline"><label class="layui-form-label lable_form"><span class="required_sign">*</span>生产类型</label></div>
                                <div class="layui-input-inline">
                                    <select name="produce_type" id="produce_type" lay-filter="produce_type" class="input_font" lay-verify="produce_type">
                                        <option value="" selected>请选择生产类型</option>
                                        @if ($production_type)
                                            @foreach ($production_type as $key => $value)
                                                <option value={{ $key }}>{{ $value }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                            <div class="grid-demo grid-demo-bg3" id="allot_no">
                                <div class="layui-input-inline" style="float: left;"><label class="layui-form-label lable_form">来源调拨单号</label></div>
                                <div class="layui-input-inline" style="width: 300px; float: left;">
                                    <input type="text" name="allot_no" lay-verify="allot_no" autocomplete="off" placeholder="请输入来源调拨单号" class="layui-input input_font" onkeypress="javascript:if(event.keyCode == 32)event.returnValue = false;">
                                </div>
                                <div style="float:left; width: 130px; margin:5px;" id="check_rfid_div">
                                    <input type="checkbox" name="check_rfid" id="check_rfid" lay-skin="primary" title="磁扣约束池" checked style="float: left;">
                                </div>
                            </div>
                            <div class="grid-demo grid-demo-bg3" id="in_store_type_div">
                                <div class="layui-input-inline"><label class="layui-form-label lable_form"><span class="required_sign">*</span>入库方式</label></div>
                                <div class="layui-input-inline">
                                    <select name="in_store_type" id="in_store_type" lay-filter="in_store_type" class="input_font" lay-verify="in_store_type">
                                        @if ($in_store_type)
                                            @foreach ($in_store_type as $key => $value)
                                                <option value={{ $key }}>{{ $value }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-row">
                            <div class="layui-col-md3">
                                <div class="grid-demo_1 grid-demo-bg1">
                                    <label class="lable_nums">生产记录</label> <label class="lable_nums" id="print_nums">0</label>
                                </div>
                            </div>
                            <div class="layui-col-md4">
                                <div class="grid-demo_1 grid-demo-bg2 lable_nums"></div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="grid-demo_1 grid-demo-bg2 lable_nums" id="copybtn" data-clipboard-action="copy" data-clipboard-target=".print_log">一键复制编码</div>
                            </div>
                            <div class="layui-col-md2">
                                <div class="grid-demo_1 grid-demo-bg2 lable_nums" id="clear_table">清空</div>
                            </div>
                        </div>
                        <div class="layui-row">
                            <div class="print_log">
                                <table class="layui-table" id="print_log" name="print_log">
                                    <thead>
                                    <tr>
                                        <th>商品编码</th>
                                        <th>数量</th>
                                    </tr>
                                    </thead>
                                    <tbody>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md7">
                <div class="layui-card">
                    <div class="layui-card-header">二、吊牌生产</div>
                    {{--调拨生产--}}
                    <div class="layui-card-body" id="allot_print">
                        <div class="layui-tab" lay-filter="tab_info">
                            <ul class="layui-tab-title">
                                <li class="layui-this" lay-id="1">店内码打印</li>
                                <li lay-id="2">条形码打印</li>
                                <input name="print_type" id="print_type" type="hidden">
                            </ul>
                            <div class="layui-tab-content">
                                <div id="epc_switch"><input lay-filter="epc_switch_unique" type="checkbox" name="close" title="RFID开关" id="epc_switch_unique"></div>
                                {{--店内码打印--}}
                                <div class="layui-tab-item layui-show" id="allot_unique_code_print_show"></div>
                                {{--条码打印--}}
                                <div class="layui-tab-item" id="allot_barcode_print_show"></div>
                            </div>
                        </div>
                    </div>
                    {{--调拨生产-RFID--}}
                    <div class="layui-card-body" id="allot_print_rfid">
                        <div class="layui-tab" lay-filter="tab_info_rfid">
                            <ul class="layui-tab-title">
                                <li class="layui-this" lay-id="1">店内码打印</li>
                                <li lay-id="2">条形码打印</li>
                                <input name="print_type_rfid" id="print_type_rfid" type="hidden">
                            </ul>
                            <div class="layui-tab-content">
                                <div id="epc_switch_rfid"><input lay-filter="epc_switch_unique_rfid" type="checkbox" name="close" title="RFID开关" id="epc_switch_unique_rfid"></div>
                                {{--店内码打印--}}
                                <div class="layui-tab-item layui-show" id="allot_unique_code_print_show_rfid"></div>
                                {{--条码打印--}}
                                <div class="layui-tab-item" id="allot_barcode_print_show_rfid"></div>
                            </div>
                        </div>
                    </div>
                    {{--新品入库--}}
                    <div class="layui-card-body" id="in_store_print">
                        <div class="layui-tab">
                            <ul class="layui-tab-title">
                                <li class="layui-this">条形码打印</li>
                            </ul>
                            <div class="layui-tab-content">
                                <div id="epc_switch_barcode"><input lay-filter="epc_switch_instore_barcode" type="checkbox" name="close" title="RFID开关" id="epc_switch_instore_barcode"></div>
                                {{--条码打印吊牌--}}
                                <div class="layui-tab-item layui-show" id="in_store_barcode_print_show"></div>
                            </div>
                        </div>
                    </div>
                    {{--新品入库-RFID--}}
                    <div class="layui-card-body" id="in_store_print_rfid">
                        <div class="layui-tab">
                            <ul class="layui-tab-title">
                                <li class="layui-this">条形码打印</li>
                            </ul>
                            <div class="layui-tab-content">
                                <div id="epc_switch_barcode_rfid"><input lay-filter="epc_switch_instore_barcode" type="checkbox" name="close" title="RFID开关" id="epc_switch_instore_barcode_rfid"></div>
                                <input type="hidden" name="in_store_unique_code" id="in_store_unique_code_rfid">
                                {{--条码打印吊牌--}}
                                <div class="layui-tab-item layui-show" id="in_store_barcode_print_show_rfid"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

{{--打印吊牌时，需要显示的商品信息--}}
<div class="layui-col-md12" id="print_tag_info">
    <div class="layui-col-md2">
        <img id="spu_image" style="width: 100px;height: 100px" src="">
    </div>
    <div class="layui-col-md8">
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form" id="code_type"></label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_unique_code"></label>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form">销售价:</label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_sale_price"></label>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form">吊牌价:</label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_tag_price"></label>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form">品类:</label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_category"></label>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form">品牌:</label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_brand"></label>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form">SKU:</label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_sku"></label>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form">货号:</label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_spu_no"></label>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-input-inline"><label class="layui-form-label lable_form" id="print_spec_key"></label></div>
            <div class="layui-input-inline">
                <label class="layui-form-label lable_font" id="print_spec_value"></label>
            </div>
        </div>
    </div>
</div>

{{--调拨生产，条码打印吊牌--}}
<div id="allot_barcode_print">
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form"><span class="required_sign">*</span>仓库名称</label>
                    </div>
                    <div class="layui-col-md7">
                        <select name="w_id" id="w_id" lay-filter="w_id" class="input_font" lay-verify="w_id" lay-search>
                            @if ($warehouse_list)
                                @foreach ($warehouse_list as $key => $value)
                                    <option value={{ $key }}>{{ $value }}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2"><label class="layui-form-label lable_form">识别RFID</label></div>
                    <div class="layui-col-md7">
                        <input type="text" id="epc_code" name="epc_code" lay-verify="epc_code" placeholder="请将RFID标签放到采集台上" class="layui-input input_font">
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px;">
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form"><span class="required_sign">*</span>扫描条形码</label>
                    </div>
                    <div class="layui-col-md7">
                        <input type="text" name="barcode" lay-verify="barcode" placeholder="请扫描条形码" class="layui-input input_font" id="barcode">
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">&nbsp;</div>
                    <div class="layui-col-md8">
                        <button type="button" class="layui-btn" lay-submit lay-filter="allot_print_barcode">打印</button>
                        <label class="wait_bind" id="wait_bind"></label>
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px;">
                        <label class="layui-form-label" id="check_unique_info"></label>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12" id="print_unique_info"></div>
    </div>
</div>

{{--调拨生产，条码打印吊牌--rfid --}}
<div id="allot_barcode_print_rfid">
    <div class="layui-row">
        <input type="hidden" name="tag_print_log_id" id="tag_print_log_id">
        <div class="layui-col-md12">
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2"><label class="layui-form-label lable_form"><span class="required_sign">*</span>仓库名称</label></div>
                    <div class="layui-col-md7">
                        <select name="w_id" id="w_id" lay-filter="w_id" class="input_font" lay-verify="w_id" lay-search>
                            @if ($warehouse_list)
                                @foreach ($warehouse_list as $key => $value)
                                    <option value={{ $key }}>{{ $value }}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form"><span class="required_sign">*</span>扫描条形码</label>
                    </div>
                    <div class="layui-col-md7">
                        <input type="text" name="barcode" lay-verify="barcode" placeholder="请扫描条形码" class="layui-input input_font" id="barcode_rfid" onkeypress="javascript:if(event.keyCode == 32)event.returnValue = false;">
                    </div>
                    <div class="layui-col-md2" style="margin-left: 20px;">
                        <button type="button" class="layui-btn" lay-submit lay-filter="allot_print_barcode_rfid">打印</button>
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2"><label class="layui-form-label lable_form"><span class="required_sign">*</span>识别RFID</label></div>
                    <div class="layui-col-md7">
                        <input type="text" id="epc_code" name="epc_code" lay-verify="epc_code" placeholder="请将RFID标签放到采集台上" class="layui-input input_font" onkeypress="javascript:if(event.keyCode == 32)event.returnValue = false;">
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px;">
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">&nbsp;</div>
                    <div class="layui-col-md8">
                        <button type="button" class="layui-btn" lay-submit lay-filter="allot_bind_barcode_rfid">绑定</button>
                        <label class="wait_bind" id="wait_bind"></label>
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px;">
                        <label class="layui-form-label" id="check_unique_info"></label>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12" id="print_unique_info"></div>
    </div>
</div>

{{--调拨生产，店内码打印吊牌--}}
<div id="allot_unique_code_print">
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2"><label class="layui-form-label lable_form">识别RFID</label></div>
                    <div class="layui-col-md7">
                        <input type="text" id="epc_code" name="epc_code" lay-verify="epc_code" placeholder="请将RFID标签放到采集台上" class="layui-input input_font">
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px;"></div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form"><span class="required_sign">*</span>扫描店内码</label>
                    </div>
                    <div class="layui-col-md7">
                        <input type="text" name="unique_code" lay-verify="unique_code" placeholder="请扫描店内码" class="layui-input input_font" id="unique_code">
                    </div>
                    <div class="layui-col-md2" style="margin-left: 20px;">
                        <button type="button" class="layui-btn" lay-submit lay-filter="allot_print_uniqe">查询</button>
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">&nbsp;</div>
                    <div class="layui-col-md8">
                        <button type="button" class="layui-btn" lay-submit lay-filter="allot_print_uniqe">打印</button>
                        <label class="wait_bind" id="wait_bind"></label>
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px;">
                        <label class="layui-form-label" id="check_unique_info"></label>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12" id="print_unique_info"></div>
    </div>
</div>

{{--调拨生产，店内码打印吊牌--rfid--}}
<div id="allot_unique_code_print_rfid">
    <div class="layui-row">
        <input type="hidden" name="tag_print_log_id" id="tag_print_log_id">
        <div class="layui-col-md12">
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form"><span class="required_sign">*</span>扫描店内码</label>
                    </div>
                    <div class="layui-col-md7">
                        <input type="text" name="unique_code" lay-verify="unique_code" placeholder="请扫描店内码" class="layui-input input_font" id="unique_code_rfid" onkeypress="javascript:if(event.keyCode == 32)event.returnValue = false;">
                    </div>
                    <div class="layui-col-md2" style="margin-left: 20px;">
                        <button type="button" class="layui-btn" lay-submit lay-filter="allot_print_uniqe_rfid">打印</button>
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2"><label class="layui-form-label lable_form"><span class="required_sign">*</span>识别RFID</label></div>
                    <div class="layui-col-md7">
                        <input type="text" id="epc_code" name="epc_code" lay-verify="epc_code" placeholder="请将RFID标签放到采集台上" class="layui-input input_font" onkeypress="javascript:if(event.keyCode == 32)event.returnValue = false;">
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px;"></div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">&nbsp;</div>
                    <div class="layui-col-md8">
                        <button type="button" class="layui-btn" lay-submit lay-filter="allot_bind_uniqe_rfid">绑定</button>
                        <label class="wait_bind" id="wait_bind"></label>
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px;">
                        <label class="layui-form-label" id="check_unique_info"></label>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12" id="print_unique_info"></div>
    </div>
</div>

{{--调拨生产，店内码打印吊牌-艾利打印机-绑定--rfid--}}
<div id="allot_unique_code_aili_print_bind_rfid">
    <div class="layui-row">
        <input type="hidden" name="tag_print_log_id" id="tag_print_log_id">
        <div class="layui-col-md12">
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form"><span class="required_sign">*</span>扫描店内码</label>
                    </div>
                    <div class="layui-col-md7">
                        <input type="text" name="unique_code" lay-verify="unique_code" placeholder="请扫描店内码" class="layui-input input_font" id="unique_code" onkeypress="javascript:if(event.keyCode == 32)event.returnValue = false;">
                    </div>
                    <div class="layui-col-md2" style="margin-left: 20px;">
                        <button type="button" class="layui-btn" lay-submit lay-filter="allot_print_uniqe">打印绑定</button>
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">&nbsp;</div>
                    <div class="layui-col-md8">
                        <label class="wait_bind" id="wait_bind"></label>
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px;">
                        <label class="layui-form-label" id="check_unique_info"></label>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12" id="print_unique_info"></div>
    </div>
</div>

{{--新品入库，条码打印吊牌--}}
<div id="in_store_barcode_print">
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form"><span class="required_sign">*</span>到货预约单</label>
                    </div>
                    <div class="layui-col-md7">
                        <input type="text" name="arrival_order_no" id="arrival_order_no" placeholder="请输入到货预约单号" class="layui-input input_font">
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2"><label class="layui-form-label lable_form">识别RFID</label></div>
                    <div class="layui-col-md7">
                        <input type="text" name="epc_code" lay-verify="epc_code" id="epc_code" placeholder="请将RFID标签放到采集台上" class="layui-input input_font">
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px; margin-top: 3px;">

                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form"><span class="required_sign">*</span>扫描条形码</label>
                    </div>
                    <div class="layui-col-md7">
                        <input type="text" name="in_store_barode" id="in_store_barode" placeholder="请扫描条形码" class="layui-input input_font">
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">&nbsp;</div>
                    <div class="layui-col-md8">
                        <button type="button" class="layui-btn" lay-submit lay-filter="instore_print_barcode">打印</button>
                        <label class="wait_bind" id="wait_bind"></label>
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px;">
                        <label class="layui-form-label" id="check_unique_info"></label>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12" id="print_unique_info"></div>
    </div>
</div>

{{--新品入库，条码打印吊牌-rfid--}}
<div id="in_store_barcode_print_rfid">
    <div class="layui-row">
        <input type="hidden" name="tag_print_log_id" id="tag_print_log_id">
        <div class="layui-col-md12">
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form"><span class="required_sign">*</span>到货预约单</label>
                    </div>
                    <div class="layui-col-md7">
                        <input type="text" name="arrival_order_no" id="arrival_order_no_rfid" placeholder="请输入到货预约单号" class="layui-input input_font" onkeypress="javascript:if(event.keyCode == 32)event.returnValue = false;">
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form"><span class="required_sign">*</span>扫描条形码</label>
                    </div>
                    <div class="layui-col-md7">
                        <input type="text" name="in_store_barode" id="in_store_barode_rfid" placeholder="请扫描条形码" class="layui-input input_font" onkeypress="javascript:if(event.keyCode == 32)event.returnValue = false;">
                    </div>
                    <div class="layui-col-md2" style="margin-left: 20px;">
                        <button type="button" class="layui-btn" lay-submit lay-filter="instore_print_barcode_rfid">打印</button>
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2"><label class="layui-form-label lable_form"><span class="required_sign">*</span>识别RFID</label></div>
                    <div class="layui-col-md7">
                        <input type="text" name="epc_code" lay-verify="epc_code" id="epc_code" placeholder="请将RFID标签放到采集台上" class="layui-input input_font" onkeypress="javascript:if(event.keyCode == 32)event.returnValue = false;">
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px; margin-top: 3px;">

                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">&nbsp;</div>
                    <div class="layui-col-md8">
                        <button type="button" class="layui-btn" lay-submit lay-filter="instore_print_barcode_bind_rfid">绑定</button>
                        <label class="wait_bind" id="wait_bind"></label>
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px;">
                        <label class="layui-form-label" id="check_unique_info"></label>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12" id="print_unique_info"></div>
    </div>
</div>
<div id="in_store_barcode_aili_print_rfid">
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form"><span class="required_sign">*</span>到货预约单</label>
                    </div>
                    <div class="layui-col-md7">
                        <input type="text" name="arrival_order_no" id="arrival_order_no" placeholder="请输入到货预约单号" class="layui-input input_font">
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">
                        <label class="layui-form-label lable_form"><span class="required_sign">*</span>扫描条形码</label>
                    </div>
                    <div class="layui-col-md7">
                        <input type="text" name="in_store_barode" id="in_store_barode" placeholder="请扫描条形码" class="layui-input input_font">
                    </div>
                </div>
            </div>
            <div class="grid-demo grid-demo-bg3">
                <div class="layui-row">
                    <div class="layui-col-md2">&nbsp;</div>
                    <div class="layui-col-md8">
                        <button type="button" class="layui-btn" lay-submit lay-filter="instore_print_barcode">打印绑定</button>
                        <label class="wait_bind" id="wait_bind"></label>
                    </div>
                    <div class="layui-col-md2" style="margin-left: 10px;">
                        <label class="layui-form-label" id="check_unique_info"></label>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12" id="print_unique_info"></div>
    </div>
</div>
<script src="/static/layui/layui.js"></script>
<script src="/static/js/vue.js"></script>
<script src="http://127.0.0.1:8000/CLodopfuncs.js"></script>
{{--<script src="/static/js/jquery.min.js"></script>--}}
<script src="/static/js/clipboard.min.js"></script>

<script type="text/javascript">
    layui.config({
        base: '/static/js/' //自定义模块
    }).use(['jquery', 'table', 'element', 'form', 'layer', 'iframeTools', 'laytpl', 'comm'], function () {
        var form = layui.form;
        var table = layui.table;
        var $ = layui.jquery;
        var element = layui.element;// Tab的切换功能，切换事件监听等，需要依赖element模块
        var layer = layui.layer;
        var iframeTools = layui.iframeTools;
        var laytpl = layui.laytpl;
        var comm =layui.comm;
        var goodsCodes = [];

        $(document).ready(function() {
            // 假设你的select的元素有一个ID为'mySelect'
            var selectedValue = 3;// 获取需要选中的值，可能来自于服务器端的数据或者本地存储
            $('#temp_type').val(selectedValue);
            form.render('select'); // 更新layui的select视图
        });

        var RFID_HOST = 'http://127.0.0.1:30010';//RFID IP
        var RFID_START_URL = RFID_HOST + '/api/Reader/connectAndStart';//开启RFID
        var RFID_STOP_URL = RFID_HOST + '/api/Reader/stopAndDisconnect';//暂停RFID
        var RFID_READ_URL = RFID_HOST + '/api/Reader/continueRead';//读取RFID数据地址
        var  TAG_IMG = [
            // 1== 买断  2== 代销   3==联营
            { id: "0", src: "" },
            {
                id: "1",
                src: "http://bigoffs-rep.oss-cn-beijing.aliyuncs.com/upload/2023-03-09/af393ea3-52e1-ce72-5a22-5f0a0503f2b1.jpg"
            },
            {
                id: "2",
                src: "http://bigoffs-rep.oss-cn-beijing.aliyuncs.com/upload/2023-03-09/f097ced4-5af9-510b-0fee-225a8feb6035.jpg"
            },
            {
                id: "3",
                src: "http://bigoffs-rep.oss-cn-beijing.aliyuncs.com/upload/2023-03-09/90a009db-9300-0b50-a752-cb8219429146.jpg"
            }
        ];

        $('#allot_unique_code_print_show').html($('#allot_unique_code_print').html());
        $('#print_uniqe_epc_switch').html();


        //一键复制
        var clipboard = new ClipboardJS('#copybtn');
        clipboard.on('success', function(e) {
            layer.msg('复制成功', {icon: 1,anim: 6});
        });
        clipboard.on('error', function(e) {
            layer.msg('复制失败', {icon: 5,anim: 6});
        });

        //一键清空
        $('#clear_table').click(function () {
            $("#print_log  tr:not(:first)").remove();
            $('#print_nums').html(0);
        });


        /*******************  开启关闭rfid  *********************/
        //调拨 开启rfid
        form.on('checkbox(epc_switch_unique)', function (data) {
            if (!checkTempType(1)){
                return false;
            }

            if (!checkProduceType(1)){
                return false;
            }

            if( data.elem.checked){　　　　　　//判断当前多选框是选中还是取消选中
                goodsCodes = []
                connectAndStart()
                continueRead()
            }else{
                goodsCodes = []
                $('#epc_code').val('');
                stopAndDisconnect();
            }
            var value = data.value;   //获取选中的value值
        });
        //调拨 开启rfid
        form.on('checkbox(epc_switch_unique_rfid)', function (data) {
            if (!checkTempType(1)){
                return false;
            }

            if (!checkProduceType(1)){
                return false;
            }

            if( data.elem.checked){　　　　　　//判断当前多选框是选中还是取消选中
                goodsCodes = []
                connectAndStart()
                continueRead()
            }else{
                goodsCodes = []
                $('#epc_code').val('');
                stopAndDisconnect();
            }
            var value = data.value;   //获取选中的value值
        });
        //新品入库 开启rfid
        form.on('checkbox(epc_switch_instore_barcode)', function (data) {
            if (!checkTempType(3)){
                return false;
            }
            if (!checkProduceType(3)){
                return false;
            }
            if ($('#in_store_type').val().length == 0){
                $('#in_store_type').css('border-color', 'red');
                layer.msg('请选择入库方式', {icon: 5,anim: 6});
                $('#epc_switch_instore_barcode').prop("checked", false);
                form.render('checkbox')
                return false;
            }

            if( data.elem.checked){　　　　　　//判断当前多选框是选中还是取消选中
                goodsCodes = []
                connectAndStart()
                continueRead()
            }else{
                goodsCodes = []
                $('#epc_code').val('');
                stopAndDisconnect();
            }
            var value = data.value;   //获取选中的value值
        });
        /*******************  开启关闭rfid  *********************/

        /***************************  空格打开关闭rfid开关     ******************************/
        // 禁止按空格键时使用页面page down页面下翻滚动事件
        function PSDPD_KeyCheck(key) {
            if (key.target.nodeName == "INPUT" || key.target.nodeName == "TEXTAREA" || key.target.nodeName == "SELECT") return;
            if (key.target.hasAttribute("contenteditable") && key.target.getAttribute("contenteditable") == "true") return;
            if (key.ctrlKey || key.altKey || key.metaKey) return;
            if (key.key == ' ') {
                key.stopPropagation();
                key.preventDefault();
                return false;
            }
        }
        document.addEventListener('keydown', PSDPD_KeyCheck);
        $(document).keyup(function (event) {
            if (event.keyCode === 32) {
                if ($('#produce_type').val() == 1 && ($('#temp_type').val() == 1 || $('#temp_type').val() == 2)) {//调拨
                    if (!checkTempType(1)){
                        return false;
                    }
                    if (!checkProduceType(1)){
                        return false;
                    }

                    if ($('#epc_switch_unique').is(':checked')) {
                        console.log('空格执行关闭')
                        layui.use('form', function () {
                            $('#epc_switch_unique').prop("checked", false);
                            form.render();
                        });
                        goodsCodes = []
                        $('#epc_code').val('');
                        stopAndDisconnect();
                    } else {
                        console.log('空格执行开启')
                        layui.use('form', function () {
                            $('#epc_switch_unique').prop("checked", true);
                            form.render();
                        });
                        goodsCodes = []
                        connectAndStart()
                        continueRead()
                    }
                }
                else if ($('#produce_type').val() == 1 && ($('#temp_type').val() == 3 || $('#temp_type').val() == 5 || $('#temp_type').val() == 6)) {//调拨 rfid吊牌
                    console.log('produce type====='+$('#produce_type').val()+"==="+$('#temp_type').val());
                    if (!checkTempType(1)){
                        return false;
                    }
                    if (!checkProduceType(1)){
                        return false;
                    }

                    if ($('#epc_switch_unique_rfid').is(':checked')) {
                        console.log('空格执行关闭')
                        layui.use('form', function () {
                            $('#epc_switch_unique_rfid').prop("checked", false);
                            form.render();
                        });
                        goodsCodes = []
                        $('#epc_code').val('');
                        stopAndDisconnect();
                    } else {
                        console.log('空格执行开启')
                        layui.use('form', function () {
                            $('#epc_switch_unique_rfid').prop("checked", true);
                            form.render();
                        });
                        goodsCodes = []
                        connectAndStart()
                        continueRead()
                    }
                }
                else if($('#produce_type').val() == 2 && ($('#temp_type').val() == 3 || $('#temp_type').val() == 5 || $('#temp_type').val() == 6)){//新品入库 rfid吊牌
                    if (!checkTempType(3)){
                        return false;
                    }
                    if (!checkProduceType(3)){
                        return false;
                    }

                    if ($('#epc_switch_instore_barcode_rfid').is(':checked')) {
                        console.log('空格执行关闭-新品入库')
                        layui.use('form', function () {
                            $('#epc_switch_instore_barcode_rfid').prop("checked", false);
                            form.render();
                        });
                        goodsCodes = []
                        $('#epc_code').val('');
                        stopAndDisconnect();
                    } else {
                        console.log('空格执行开启-新品入库')

                        layui.use('form', function () {
                            $('#epc_switch_instore_barcode_rfid').prop("checked", true);
                            form.render();
                        });
                        goodsCodes = []
                        connectAndStart()
                        continueRead()
                    }
                }
                else if($('#produce_type').val() == 2 && ($('#temp_type').val() == 1 || $('#temp_type').val() == 2)) {//新品入库
                    if (!checkTempType(3)){
                        return false;
                    }
                    if (!checkProduceType(3)){
                        return false;
                    }

                    if ($('#epc_switch_instore_barcode').is(':checked')) {
                        console.log('空格执行关闭-新品入库')
                        layui.use('form', function () {
                            $('#epc_switch_instore_barcode').prop("checked", false);
                            form.render();
                        });
                        goodsCodes = []
                        $('#epc_code').val('');
                        stopAndDisconnect();
                    } else {
                        console.log('空格执行开启-新品入库')

                        layui.use('form', function () {
                            $('#epc_switch_instore_barcode').prop("checked", true);
                            form.render();
                        });
                        goodsCodes = []
                        connectAndStart()
                        continueRead()
                    }
                }
            }
        });
        /***************************  空格打开关闭rfid开关     ******************************/

        /***************************     RFID获取     ******************************/
        var timer;
        function stopAndDisconnect() {
            clearInterval(timer);
            curlPost(RFID_STOP_URL)
        }

        function connectAndStart() {
            curlPost(RFID_START_URL);
        }

        function continueRead() {
            console.log('持续读取')
            timer=setInterval(Read,800);
        }

        function Read() {
            // console.log('读取')
            curlPost(RFID_READ_URL);
            // console.log(RFID_READ_URL)
        }

        function curlPost(url,params) {
            // console.log(url);
            $.ajax({
                url: url,
                type: 'POST',
                data:{StartReadParam:{MemoryBank:'EPC',ExcludeDuplication:'true'}},
                dataType: 'JSON',
                success: function (result) {
                    // console.log('epc请求success结果==',result);
                    if ('boolean' != typeof(result) && result.Data.length > 0) {
                        var before_epc_info = $("#epc_code").val()

                        // 将返回结果提取，去重
                        var epcRes = comm.array_unique(comm.array_column(result.Data,'Epc'))
                        console.log('epc res========================', epcRes);
                        if (epcRes.length > 1){
                            layer.msg('不可放入多个RFID!', {icon: 5,anim: 6});
                            return false;
                        }

                        //检测板上的rfid是否在此次读取rfid中已被绑定
                        var ar = epcRes.filter(function(n) {
                            return goodsCodes.indexOf(n) != -1
                        });
                        if (ar != ''){
                            $("#epc_code").val('');
                            return false;
                        }

                        $("#epc_code").val('');
                        $("#epc_code").val(epcRes);
                        now_epc_code = $("#epc_code").val();
                        check_rfid = $("#check_rfid").is(':checked');
                        console.log('check rfid:');
                        console.log(check_rfid);
                        checkEpcRes = true;

                        if (before_epc_info !== now_epc_code && now_epc_code != '') {
                            $('#print_unique_info').html('');

                            if (check_rfid && $('#produce_type').val() == 1 ){//校验是否在磁扣池中
                                $.ajax({
                                    url: '/tagPrint/checkEpcPool',
                                    type: 'post',
                                    data: JSON.stringify({
                                        epc_code : now_epc_code
                                    }),
                                    dataType: "json",
                                    contentType: 'application/json',
                                    processData: false,
                                    success: function (data) {
                                        if (data.code !== 200 && data.data.is_set_ecp == false){
                                            layer.alert("此磁扣未加入到磁扣池，请先加入磁扣池！")
                                        }else{
                                            $.ajax({
                                                url: '/tagPrint/isAgainPrint',
                                                type: 'post',
                                                data: JSON.stringify({
                                                    epc_code : now_epc_code,
                                                    is_again_print: 0,
                                                    check_rfid : check_rfid
                                                }),
                                                dataType: "json",
                                                contentType: 'application/json',
                                                processData: false,
                                                success: function (data) {
                                                    //rfid吊牌模版绑定时，检测是否已打印
                                                    console.log('temp type===========', $('#temp_type').val());
                                                    if ($('#temp_type').val() == 3){
                                                        if ($('#tag_print_log_id').val().length == 0){
                                                            layer.msg('请先打印吊牌！', {icon: 5,anim: 6});
                                                            return false;
                                                        }
                                                    }

                                                    if (data.code !== 200 && data.data.is_again_print == 1 && $('#temp_type').val() != 3) {
                                                        layer.open({
                                                            type: 1
                                                            ,title: false //不显示标题栏
                                                            ,closeBtn: false
                                                            ,area: '300px;'
                                                            ,shade: 0.8
                                                            ,id: 'LAY_layuipro' //设定一个id，防止重复弹出
                                                            ,btn: ['确定', '取消']
                                                            ,shadeClose: true
                                                            ,btnAlign: 'c'
                                                            ,moveType: 1 //拖拽模式，0或者1
                                                            ,content: '<div style="padding: 50px; line-height: 22px; background-color: #393D49; color: #fff; font-weight: 300;">是否补打吊牌？</div>'
                                                            ,yes: function(layero){
                                                                $.ajax({
                                                                    url: '/tagPrint/isAgainPrint',
                                                                    type: 'post',
                                                                    data: JSON.stringify({
                                                                        epc_code : now_epc_code,
                                                                        is_again_print: 1
                                                                    }),
                                                                    dataType: "json",
                                                                    contentType: 'application/json',
                                                                    processData: false,
                                                                    success: function (data) {
                                                                        console.log("data===========", data);
                                                                        show_print_tag_info(data.data.tag_info, 0);
                                                                        layer.close(layer.index);
                                                                        //检测店内码是否已出库
                                                                        if(data.data.unique_code_status == 2){
                                                                            layer.msg("店内码已出库", {
                                                                                icon: 0,
                                                                                time: 3000
                                                                            });
                                                                        }
                                                                        //打印吊牌
                                                                        print_tag(data.data.tag_info, $('#temp_type').val(), 1);
                                                                    },
                                                                    error: function (e) {
                                                                        layer.alert("检测是否需要补打吊牌失败！")
                                                                    },
                                                                });
                                                            }
                                                            ,btn2: function(index, layero){
                                                                $('#unique_code').focus();
                                                            }

                                                            ,success: function(layero, index){
                                                                this.enterConfirm = function(event){
                                                                    if(event.keyCode === 13){
                                                                        $(".layui-layer-btn0").click();
                                                                        return false; //阻止系统默认回车事件
                                                                    }
                                                                };
                                                                $(document).on('keydown', this.enterConfirm); //监听键盘事件

                                                                // 点击确定按钮回调事件
                                                                $(".layui-layer-btn0").on("click",function() {
                                                                })
                                                            },
                                                            end: function(){
                                                                $(document).off('keydown', this.enterConfirm); //解除键盘事件
                                                            }

                                                        });
                                                    }else{
                                                        $('#wait_bind').html('等待绑定');
                                                        if ($('#produce_type').val() == 2){//新品入库
                                                            if ($('#temp_type').val() == 3 || $('#temp_type').val() == 6 || $('#temp_type').val() == 5){
                                                                bindEpc();
                                                            }else {
                                                                $('#in_store_barode').focus();
                                                            }
                                                        }else if($('#produce_type').val() == 1){//调拨生产
                                                            if ($('#temp_type').val() == 3 || $('#temp_type').val() == 4 || $('#temp_type').val() == 6 || $('#temp_type').val() == 5){
                                                                bindEpc();
                                                            }else{
                                                                if($('#print_type').val() == 1 || $('#print_type').val() == ''){
                                                                    $('#unique_code').focus();
                                                                }else if($('#print_type').val() == 2){
                                                                    $('#barcode').focus();
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                error: function (e) {
                                                    layer.alert("检测是否需要补打吊牌失败！")
                                                },
                                            });
                                        }
                                    },
                                    error: function (e) {
                                        layer.alert("检测磁扣是否在磁扣池中失败！")
                                    },
                                });
                            }else{
                                $.ajax({
                                    url: '/tagPrint/isAgainPrint',
                                    type: 'post',
                                    data: JSON.stringify({
                                        epc_code : now_epc_code,
                                        is_again_print: 0,
                                        check_rfid : check_rfid
                                    }),
                                    dataType: "json",
                                    contentType: 'application/json',
                                    processData: false,
                                    success: function (data) {
                                        //rfid吊牌模版绑定时，检测是否已打印
                                        console.log('temp type===========', $('#temp_type').val());
                                        if ($('#temp_type').val() == 3){
                                            if ($('#tag_print_log_id').val().length == 0){
                                                layer.msg('请先打印吊牌！', {icon: 5,anim: 6});
                                                return false;
                                            }
                                        }

                                        if (data.code !== 200 && data.data.is_again_print == 1 && $('#temp_type').val() != 3) {
                                            layer.open({
                                                type: 1
                                                ,title: false //不显示标题栏
                                                ,closeBtn: false
                                                ,area: '300px;'
                                                ,shade: 0.8
                                                ,id: 'LAY_layuipro' //设定一个id，防止重复弹出
                                                ,btn: ['确定', '取消']
                                                ,shadeClose: true
                                                ,btnAlign: 'c'
                                                ,moveType: 1 //拖拽模式，0或者1
                                                ,content: '<div style="padding: 50px; line-height: 22px; background-color: #393D49; color: #fff; font-weight: 300;">是否补打吊牌？</div>'
                                                ,yes: function(layero){
                                                    $.ajax({
                                                        url: '/tagPrint/isAgainPrint',
                                                        type: 'post',
                                                        data: JSON.stringify({
                                                            epc_code : now_epc_code,
                                                            is_again_print: 1
                                                        }),
                                                        dataType: "json",
                                                        contentType: 'application/json',
                                                        processData: false,
                                                        success: function (data) {
                                                            console.log("data===========", data);
                                                            show_print_tag_info(data.data.tag_info, 0);
                                                            layer.close(layer.index);

                                                            //检测店内码是否已出库
                                                            if($('#produce_type').val() == 1 && data.data.unique_code_status == 2){
                                                                layer.msg("店内码已出库", {
                                                                    icon: 0,
                                                                    time: 3000
                                                                });
                                                            }

                                                            //打印吊牌
                                                            print_tag(data.data.tag_info, $('#temp_type').val(), 1);
                                                        },
                                                        error: function (e) {
                                                            layer.alert("检测是否需要补打吊牌失败！")
                                                        },
                                                    });
                                                }
                                                ,btn2: function(index, layero){
                                                    $('#unique_code').focus();
                                                }

                                                ,success: function(layero, index){
                                                    this.enterConfirm = function(event){
                                                        if(event.keyCode === 13){
                                                            $(".layui-layer-btn0").click();
                                                            return false; //阻止系统默认回车事件
                                                        }
                                                    };
                                                    $(document).on('keydown', this.enterConfirm); //监听键盘事件

                                                    // 点击确定按钮回调事件
                                                    $(".layui-layer-btn0").on("click",function() {
                                                    })
                                                },
                                                end: function(){
                                                    $(document).off('keydown', this.enterConfirm); //解除键盘事件
                                                }

                                            });
                                        }else{
                                            $('#wait_bind').html('等待绑定');
                                            if ($('#produce_type').val() == 2){//新品入库
                                                if ($('#temp_type').val() == 3 || $('#temp_type').val() == 6 || $('#temp_type').val() == 5){
                                                    bindEpc();
                                                }else {
                                                    $('#in_store_barode').focus();
                                                }
                                            }else if($('#produce_type').val() == 1){//调拨生产
                                                if ($('#temp_type').val() == 3 || $('#temp_type').val() == 4 || $('#temp_type').val() == 6 || $('#temp_type').val() == 5){
                                                    bindEpc();
                                                }else{
                                                    if($('#print_type').val() == 1 || $('#print_type').val() == ''){
                                                        $('#unique_code').focus();
                                                    }else if($('#print_type').val() == 2){
                                                        $('#barcode').focus();
                                                    }
                                                }
                                            }
                                        }
                                    },
                                    error: function (e) {
                                        layer.alert("检测是否需要补打吊牌失败！")
                                    },
                                });
                            }
                            return false;
                        }
                    }
                },
                error: function (error) {
                    console.log(typeof (error));
                    console.log('epc请求结果fail==',error);
                    layer.msg('设备读取EPC码失败',{icon:2,time:1500})
                    //$("#goods_codes").text(JSON.stringify(error));
                }
            });
        }

        //显示店内码打印吊牌时的内容
        function show_print_tag_info(tag_info, sign = 1) {
            console.log('show print tag info=============',tag_info);
            if (sign == 1){
                $('#wait_bind').html(tag_info.code+'绑定成功');
            }else{
                if (($('#temp_type').val() == 6 || $('#temp_type').val() == 7 || $('#temp_type').val() == 8) && ($('#print_type_rfid').val() == 2 || $('#in_store_type').val() == 2)){
                    layer.msg("艾利打印机仅支持支店内码打印！",{icon:2,time:3500});
                    return true;
                }else if ($('#temp_type').val() == 3 || $('#temp_type').val() == 6 || $('#temp_type').val() == 5){
                    $('#wait_bind').html(tag_info.code+'校验成功 等待绑定');
                }else if($('#temp_type').val() == 7 || $('#temp_type').val() == 8){
                    $('#wait_bind').html(tag_info.code+' 校验成功 ');
                }else{
                    $('#wait_bind').html(tag_info.code+'校验成功');
                }
            }

            $('#wait_bind').css('color', 'rgb(0, 191, 160)');
            var code_type = '';
            $("#spu_image").attr("src", tag_info.image);
            if (tag_info.code_type == 1){//店内码
                code_type = '店内码';
            }else{//条形码
                code_type = '条形码';
            }
            // $('#check_unique_info').html(tag_info.code+'校验成功');
            // $('#check_unique_info').show();


            var print_tag_info = '<div class="layui-col-md2">' +
                '        <img id="spu_image" style="width: 100px;height: 100px" src="">' +
                '    </div>' +
                '    <div class="layui-col-md8">' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form" id="code_type">'+code_type+'</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_unique_code">'+tag_info.code+'</label></div>' +
                '        </div>' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form">销售价:</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_sale_price">'+tag_info.sale_price+'</label></div>' +
                '        </div>' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form">吊牌价:</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_tag_price">'+tag_info.tag_price+'</label></div>' +
                '        </div>' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form">品类:</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_category">'+tag_info.category_name+'</label></div>' +
                '        </div>' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form">品牌:</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_brand">'+tag_info.brand_name+'</label></div>' +
                '        </div>' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form">SKU:</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_sku">'+tag_info.SKU+'</label></div>' +
                '        </div>' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form">货号:</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_spu_no">'+tag_info.spu_no+'</label></div>' +
                '        <div class="layui-row">' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_form" id="print_spec_key">'+tag_info.spec_key+':</label></div>' +
                '            <div class="layui-input-inline"><label class="layui-form-label lable_font" id="print_spec_value">'+tag_info.spec_value+'</label></div>' +
                '        </div>' +
                '    </div>';
            $('#print_unique_info').html(print_tag_info);
        }
        //打印吊牌
        function print_tag(print_info, temp_type, number) {
            console.log("temp_type=================================="+temp_type);

            if ( (temp_type == 6 || temp_type ==7 || temp_type == 8) && ($('#print_type_rfid').val() == 2 || $('#in_store_type').val() == 2)){
                layer.msg("艾利打印机仅支持支店内码打印！",{icon:2,time:3500});
                return true;
            }

            //累计打印数量
            print_nums = $('#print_nums').html()*1;
            print_nums += number*1;
            $('#print_nums').html(print_nums);
            //追加数据
            $("#print_log tbody").prepend('<tr><td>'+print_info.code+'</td><td>'+number+'</td></tr>');

            var sale_price = print_info.sale_price.split(".")
            var priceLength = sale_price[0].length
            var priceOneWidth = ""
            var priceTwoLeft = ""
            var priceSignleft = ""
            var priceOneLeft = ""
            if (temp_type == 3){//rfid吊牌
                if (priceLength == 1) {
                    priceOneWidth = "14mm"
                    priceTwoLeft = "18mm"
                } else if (priceLength == 2) {
                    priceOneWidth = "20mm"
                    priceTwoLeft = "24mm"
                } else if (priceLength == 3) {
                    priceOneWidth = "27mm"
                    priceTwoLeft = "31mm"
                } else if (priceLength == 4) {
                    priceOneWidth = "33mm"
                    priceTwoLeft = "37mm"
                }
            }else if(temp_type == 5){
                //检测价格长度
                if (priceLength == 1) {
                    priceSignleft = "13mm"
                    priceOneLeft = "16mm"
                    priceOneWidth = "12mm"
                    priceTwoLeft = "20mm"
                } else if (priceLength == 2) {
                    priceSignleft = "11mm"
                    priceOneLeft = "14mm"
                    priceOneWidth = "15mm"
                    priceTwoLeft = "22mm"
                } else if (priceLength == 3) {
                    priceSignleft = "9mm"
                    priceOneLeft = "12mm"
                    priceOneWidth = "18mm"
                    priceTwoLeft = "25mm"
                } else if (priceLength == 4) {
                    priceSignleft = "6mm"
                    priceOneLeft = "8mm"
                    priceOneWidth = "21mm"
                    priceTwoLeft = "26mm"
                }
            }else{
                if (priceLength == 1) {
                    priceOneWidth = "11mm"
                    priceTwoLeft = "17mm"
                } else if (priceLength == 2) {
                    priceOneWidth = "17mm"
                    priceTwoLeft = "23mm"
                } else if (priceLength == 3) {
                    priceOneWidth = "24mm"
                    priceTwoLeft = "30mm"
                } else if (priceLength == 4) {
                    priceOneWidth = "30mm"
                    priceTwoLeft = "36mm"
                }
            }

            // let LODOP = getLodop() //获得打印
            // LODOP.SET_LICENSES(
            //     "",
            //     "7353DCADCE6BD1DFDAEA717887A2627B",
            //     "C94CEE276DB2187AE6B65D56B3FC2848",
            //     ""
            // )
            LODOP.SET_LICENSES("","94D9E78FC9B721D1EBF7165B69114B39A35","","");

            if (temp_type == 1){//  5*10  无建议销售价
                console.log('5*10吊牌');
                LODOP.PRINT_INITA("0mm", "0mm", "50", "100mm", "任务名")

                LODOP.SET_PRINT_PAGESIZE(1, 500, 1000, "")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 16)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                LODOP.ADD_PRINT_TEXT("22mm", "5mm", "12mm", "3mm", "尺码")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 8)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                var spec5Top='18.9mm'
                var spec5Size=16
                if (print_info.spec_value.length >7 && print_info.spec_value.length <=11){
                    spec5Top='20.5mm'
                    spec5Size=12
                }else if (print_info.spec_value.length >11){
                    spec5Top='21.5mm'
                    spec5Size=10
                }
                LODOP.ADD_PRINT_TEXT(spec5Top,"13mm","30mm","3mm",print_info.spec_value) //11
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", spec5Size)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                LODOP.ADD_PRINT_TEXT("31.2mm", "5mm", "12mm", "3mm", "品牌：")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 8)
                LODOP.ADD_PRINT_TEXT(
                    "31.2mm",
                    "17mm",
                    "30mm",
                    "3mm",
                    print_info.brand_name
                )
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 8)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")

                LODOP.ADD_PRINT_TEXT("34.5mm", "5mm", "12mm", "3mm", "货号：")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 8)
                LODOP.ADD_PRINT_TEXT("34.5mm", "17mm", "30mm", "3mm", print_info.spu_no)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 8)

                LODOP.ADD_PRINT_TEXT("37.5mm", "5mm", "12mm", "3mm", "吊牌价：")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 8)
                LODOP.ADD_PRINT_TEXT("37.5mm", "17mm", "30mm", "3mm", "￥"+print_info.tag_price)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 8)

                LODOP.ADD_PRINT_TEXT(
                    "41.7mm",
                    "5mm",
                    "40mm",
                    "3mm",
                    "产地：见吊牌或水洗标："
                )
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 6)

                LODOP.ADD_PRINT_TEXT(
                    "44.5mm",
                    "5mm",
                    "42mm",
                    "3mm",
                    "经销商：好超值(天津)信息技术有限公司"
                )
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 6)

                LODOP.ADD_PRINT_TEXT(
                    "47.2mm",
                    "5mm",
                    "42mm",
                    "3mm",
                    "本品牌商品销售价以小程序扫码结果为准"
                )
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 6)

                LODOP.ADD_PRINT_TEXT("56mm", "5mm", "5mm", "4mm", "¥")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 15)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                LODOP.ADD_PRINT_TEXT(
                    "53.5mm",
                    "6mm",
                    priceOneWidth,
                    "4mm",
                    sale_price[0]
                )
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 30)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                LODOP.ADD_PRINT_TEXT(
                    "60mm",
                    priceTwoLeft,
                    "15mm",
                    "4mm",
                    "." + sale_price[1]
                )
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 14)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                LODOP.ADD_PRINT_BARCODE(
                    "70.8mm",
                    "10mm",
                    "34mm",
                    "7mm",
                    "128Auto",
                    print_info.code
                )
                LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 0)

                LODOP.ADD_PRINT_TEXT("78mm", "7mm", "40mm", "6mm", print_info.code)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 10)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 2)
                LODOP.SET_PRINT_STYLEA(0, "Horient", 2)

                if (print_info.co_model && TAG_IMG[print_info.co_model].src) {
                    LODOP.ADD_PRINT_IMAGE(
                        "86mm",
                        "38mm",
                        "8mm",
                        "8mm",
                        `<img  border='0' src='${TAG_IMG[print_info.co_model].src}' />`
                    )
                    LODOP.SET_PRINT_STYLEA(0, "Stretch", 2) //(可变形)扩展缩放模式
                    LODOP.SET_PRINT_STYLEA(0, "Vorient", 1)
                }

                LODOP.SET_PRINT_COPIES(number) //指定份数
                // LODOP.PRINT_DESIGN();
                //LODOP.SET_PRINTER_INDEX('吊牌打印机');
                // LODOP.PREVIEW();
                LODOP.PRINT() //执行打印
            }
            else if(temp_type == 2){//4*9
                LODOP.PRINT_INITA("0mm", "0mm", "40", "90mm", "任务名")
                LODOP.SET_PRINT_PAGESIZE(1, 400, 900, "")

                LODOP.ADD_PRINT_TEXT("22mm", "3mm", "12mm", "3mm", "尺码")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                var specTop='19.5mm'
                var specSize=13
                // TJ00022634
                if (print_info.spec_value.length >5 && print_info.spec_value.length <=7){
                    specTop='21mm'
                    specSize=10
                    // JM01278895
                }else if(print_info.spec_value.length >7){
                    specTop='21.5mm'
                    specSize=8
                    // TS00024403
                }
                LODOP.ADD_PRINT_TEXT(specTop,"10mm","33mm","3mm",print_info.spec_value) //11
                LODOP.SET_PRINT_STYLEA(0, "FontSize", specSize)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                if (priceLength == 1) {
                    priceOneWidth = "8mm"
                    priceTwoLeft = "12mm"
                } else if (priceLength == 2) {
                    priceOneWidth = "13mm"
                    priceTwoLeft = "17mm"
                } else if (priceLength == 3) {
                    priceOneWidth = "18.5mm"
                    priceTwoLeft = "22.5mm"
                } else if (priceLength == 4) {
                    priceOneWidth = "23.8mm"
                    priceTwoLeft = "27.8mm"
                }

                LODOP.ADD_PRINT_TEXT("29.7mm", "3mm", "12mm", "3mm", "品牌：")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.ADD_PRINT_TEXT(
                    "29.7mm",
                    "12mm",
                    "30mm",
                    "3mm",
                    print_info.brand_name
                )
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")

                LODOP.ADD_PRINT_TEXT("32.6mm", "3mm", "12mm", "3mm", "货号：")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.ADD_PRINT_TEXT("32.6mm", "12mm", "30mm", "3mm", print_info.spu_no)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)

                LODOP.ADD_PRINT_TEXT("35.6mm", "3mm", "12mm", "3mm", "吊牌价：")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.ADD_PRINT_TEXT("35.6mm", "12mm", "30mm", "3mm", "￥"+print_info.tag_price)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)

                LODOP.ADD_PRINT_TEXT(
                    "39.4mm",
                    "3mm",
                    "37mm",
                    "3mm",
                    "产地：见吊牌或水洗标"
                )
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                LODOP.ADD_PRINT_TEXT(
                    "41.7mm",
                    "3mm",
                    "37mm",
                    "3mm",
                    "经销商：好超值(天津)信息技术有限公司"
                )
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)

                LODOP.ADD_PRINT_TEXT(
                    "44.1mm",
                    "3mm",
                    "37mm",
                    "3mm",
                    "本品牌商品销售价以小程序扫码结果为准"
                )
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)

                LODOP.ADD_PRINT_TEXT("51mm", "3mm", "5mm", "5mm", "¥")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 10)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                LODOP.ADD_PRINT_TEXT("49mm", "4mm", priceOneWidth, "5mm", sale_price[0])
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 24)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                LODOP.ADD_PRINT_TEXT(
                    "53.9mm",
                    priceTwoLeft,
                    "15mm",
                    "5mm",
                    "." + sale_price[1]
                )
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 12)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                if(print_info.code.length > 11){
                    LODOP.ADD_PRINT_BARCODE(
                        "63mm",
                        "3mm",
                        "34mm",
                        "7mm",
                        "128Auto",
                        print_info.code
                    )
                }else{
                    LODOP.ADD_PRINT_BARCODE(
                        "63mm",
                        "5mm",
                        "34mm",
                        "7mm",
                        "128Auto",
                        print_info.code
                    )
                }


                LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 0)

                LODOP.ADD_PRINT_TEXT("71mm", "6mm", "40mm", "6mm", print_info.code)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 10)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 2)
                LODOP.SET_PRINT_STYLEA(0, "Horient", 2)

                if (print_info.co_model && TAG_IMG[print_info.co_model].src) {
                    LODOP.ADD_PRINT_IMAGE(
                        "77.5mm",
                        "31mm",
                        "7mm",
                        "7mm",
                        `<img  border='0' src='${TAG_IMG[print_info.co_model].src}' />`
                    )
                    LODOP.SET_PRINT_STYLEA(0, "Stretch", 2) //(可变形)扩展缩放模式
                    LODOP.SET_PRINT_STYLEA(0, "Vorient", 1)
                }

                LODOP.SET_PRINT_COPIES(number) //指定份数
                // LODOP.PRINT_DESIGN();
                //LODOP.SET_PRINTER_INDEX('吊牌打印机');
                // LODOP.PREVIEW();
                LODOP.PRINT() //执行打印
            }
            else if(temp_type == 4){//4*9-rfid
                LODOP.PRINT_INITA("0mm", "0mm", "40", "90mm", "任务名")
                LODOP.SET_PRINT_PAGESIZE(1, 400, 900, "")

                LODOP.ADD_PRINT_TEXT("22mm", "3mm", "12mm", "3mm", "尺码")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                var specTop='19.5mm'
                var specSize=13
                // TJ00022634
                if (print_info.spec_value.length >5 && print_info.spec_value.length <=7){
                    specTop='21mm'
                    specSize=10
                    // JM01278895
                }else if(print_info.spec_value.length >7){
                    specTop='21.5mm'
                    specSize=8
                    // TS00024403
                }
                LODOP.ADD_PRINT_TEXT(specTop,"10mm","33mm","3mm",print_info.spec_value.substring(0, 12)) //11
                LODOP.SET_PRINT_STYLEA(0, "FontSize", specSize)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                //横线 实线
                LODOP.ADD_PRINT_LINE("28mm","2mm", "28mm", "RightMargin:2mm",0, 1)

                if (priceLength == 1) {
                    priceOneWidth = "8mm"
                    priceTwoLeft = "12mm"
                } else if (priceLength == 2) {
                    priceOneWidth = "13mm"
                    priceTwoLeft = "17mm"
                } else if (priceLength == 3) {
                    priceOneWidth = "18.5mm"
                    priceTwoLeft = "22.5mm"
                } else if (priceLength == 4) {
                    priceOneWidth = "23.8mm"
                    priceTwoLeft = "27.8mm"
                }

                LODOP.ADD_PRINT_TEXT("29.7mm", "3mm", "12mm", "3mm", "品牌：")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.ADD_PRINT_TEXT("29.7mm","1mm","RightMargin:3mm","3mm",print_info.brand_name.substring(0, 10))
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)


                LODOP.ADD_PRINT_TEXT("32.6mm", "3mm", "12mm", "3mm", "货号：")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.ADD_PRINT_TEXT("32.6mm", "1mm", "RightMargin:3mm", "3mm", print_info.spu_no.substring(0, 14))
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                LODOP.ADD_PRINT_TEXT("35.6mm", "3mm", "12mm", "3mm", "吊牌价：")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.ADD_PRINT_TEXT("35.6mm", "1mm", "RightMargin:3mm", "3mm", "￥"+print_info.tag_price)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                //横线 实线
                LODOP.ADD_PRINT_LINE("39.4mm","2mm", "39.4mm", "RightMargin:2mm",0, 1)

                LODOP.ADD_PRINT_TEXT( "40.4mm", "3mm", "37mm", "3mm", "产地：")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                LODOP.ADD_PRINT_TEXT( "40.4mm", "3mm", "RightMargin:3mm", "3mm", "见吊牌或水洗标")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                LODOP.ADD_PRINT_TEXT( "42.7mm", "3mm", "37mm", "3mm", "经销商：")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                LODOP.ADD_PRINT_TEXT( "42.7mm", "3mm", "RightMargin:3mm", "3mm", "好超值(天津)信息技术有限公司")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                LODOP.ADD_PRINT_TEXT("45.1mm", "-2mm", "RightMargin:3mm", "3mm", "本品牌商品销售价以小程序扫码结果为准")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                LODOP.ADD_PRINT_TEXT("51mm", "3mm", "5mm", "5mm", "¥")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 10)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                LODOP.ADD_PRINT_TEXT("49mm", "4mm", priceOneWidth, "5mm", sale_price[0])
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 24)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                LODOP.ADD_PRINT_TEXT( "53.9mm", priceTwoLeft, "15mm", "5mm", "." + sale_price[1])
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 12)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                if(print_info.code.length > 11){
                    LODOP.ADD_PRINT_BARCODE( "63mm", "3mm", "34mm", "7mm", "128Auto", print_info.code)
                }else{
                    LODOP.ADD_PRINT_BARCODE( "63mm", "5mm", "34mm", "7mm", "128Auto", print_info.code)
                }


                LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 0)

                LODOP.ADD_PRINT_TEXT("71mm", "6mm", "40mm", "6mm", print_info.code)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 10)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 2)
                LODOP.SET_PRINT_STYLEA(0, "Horient", 2)

                if (print_info.co_model && TAG_IMG[print_info.co_model].src) {
                    LODOP.ADD_PRINT_IMAGE( "77.5mm", "31mm", "7mm", "7mm", `<img  border='0' src='${TAG_IMG[print_info.co_model].src}' />` )
                    LODOP.SET_PRINT_STYLEA(0, "Stretch", 2) //(可变形)扩展缩放模式
                    LODOP.SET_PRINT_STYLEA(0, "Vorient", 1)
                }

                LODOP.SET_PRINT_COPIES(number) //指定份数
                // LODOP.PRINT_DESIGN();
                //LODOP.SET_PRINTER_INDEX('吊牌打印机');
                // LODOP.PREVIEW();
                LODOP.PRINT() //执行打印
            }
            else if(temp_type == 3){
                LODOP.PRINT_INITA("0mm", "0mm", "50", "100mm", "任务名")

                LODOP.SET_PRINT_PAGESIZE(1, 550, 950, "")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 16)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                // LL00000081
                if (print_info.spec_value.length <= 7) {
                    LODOP.ADD_PRINT_TEXT("22mm", "5mm", "12mm", "3mm", "尺码")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 8)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                    LODOP.ADD_PRINT_TEXT("19.5mm","12mm","42mm","3mm",print_info.spec_value)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 16)
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                }else if(print_info.spec_value.length > 7 && print_info.spec_value.length <= 11){
                    // if (print_info.spec_value.length >= 5&&print_info.spec_value.length <=9)
                    LODOP.ADD_PRINT_TEXT("22mm", "5mm", "12mm", "3mm", "尺码")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 8)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                    LODOP.ADD_PRINT_TEXT("20.5mm","12mm","42mm","3mm",print_info.spec_value) //11
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 12)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                }else{
                    // if (print_info.spec_value.length >= 5&&print_info.spec_value.length <=9)
                    LODOP.ADD_PRINT_TEXT("22mm", "5mm", "12mm", "3mm", "尺码")
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 8)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                    LODOP.ADD_PRINT_TEXT("21.5mm","12mm","42mm","3mm",print_info.spec_value) //11
                    LODOP.SET_PRINT_STYLEA(0, "FontSize", 10)
                    LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                    LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                }


                //横线 实线
                LODOP.ADD_PRINT_LINE("28mm","4.5mm", "28mm", "RightMargin:4.5mm",0, 1)

                //品牌
                LODOP.ADD_PRINT_TEXT("30.8mm", "5mm", "12mm", "3mm", "品牌")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                //品牌名称
                LODOP.ADD_PRINT_TEXT("30.8mm","1mm","RightMargin:5mm","3mm",print_info.brand_name)
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                //货号
                LODOP.ADD_PRINT_TEXT("34.1mm", "5mm", "12mm", "3mm", "货号")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                //货号名称
                LODOP.ADD_PRINT_TEXT("34.1mm","1mm","RightMargin:5mm", "3mm", print_info.spu_no)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                //货号
                LODOP.ADD_PRINT_TEXT("37.4mm", "5mm", "12mm", "3mm", "吊牌价")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                //货号名称
                LODOP.ADD_PRINT_TEXT("37.4mm","1mm","RightMargin:5mm", "3mm", "￥"+print_info.tag_price)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                //横线 实线
                LODOP.ADD_PRINT_LINE("43mm","4mm", "43mm", "RightMargin:4.5mm",0, 1)

                //产地
                LODOP.ADD_PRINT_TEXT("45.1mm","5mm","40mm","3mm","产地")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                LODOP.ADD_PRINT_TEXT("45.1mm","1mm","RightMargin:5mm","3mm","见吊牌或水洗标")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                //经销商
                LODOP.ADD_PRINT_TEXT("48.4mm","5mm","42mm","3mm","经销商")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                LODOP.ADD_PRINT_TEXT("48.4mm","1mm","RightMargin:5mm","3mm","好超值(天津)信息技术有限公司")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 7)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                //销售价 ￥
                LODOP.ADD_PRINT_TEXT("54mm", "5mm", "5mm", "4mm", "¥")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 15)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                //元
                LODOP.ADD_PRINT_TEXT("52mm","4mm",priceOneWidth,"8.4mm",sale_price[0])
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 34)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                //角分
                LODOP.ADD_PRINT_TEXT("59.3mm",priceTwoLeft,"15mm","4mm","." + sale_price[1])
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 14)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                //黑色背景
                // LODOP.ADD_PRINT_HTM("54.6mm","3mm","RightMargin:2mm","12mm","<div style='background-color: #0A0E11; width: 47mm;height: 12mm;'></div>");//一个矩形设置颜色为#FF0000

                //条形码
                LODOP.ADD_PRINT_BARCODE("69.2mm","7.7mm","38mm","8mm","128Auto",print_info.code)
                LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 0)

                LODOP.ADD_PRINT_TEXT("78.2mm", "7mm", "40mm", "6mm", print_info.code)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 10)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 2)
                LODOP.SET_PRINT_STYLEA(0, "Horient", 2)

                if (print_info.co_model && TAG_IMG[print_info.co_model].src) {
                    LODOP.ADD_PRINT_IMAGE("91mm","43mm","6mm","6mm",`<img  border='0' src='${TAG_IMG[print_info.co_model].src}' />`)
                    LODOP.SET_PRINT_STYLEA(0, "Stretch", 2) //(可变形)扩展缩放模式
                    LODOP.SET_PRINT_STYLEA(0, "Vorient", 1)
                }

                LODOP.SET_PRINT_COPIES(number) //指定份数
                // LODOP.PRINT_DESIGN();
                //LODOP.SET_PRINTER_INDEX('吊牌打印机');
                // LODOP.PREVIEW();
                LODOP.PRINT() //执行打印
                // LODOP.PRINT_DESIGN();
            }
            else if(temp_type == 5){ // 6*4 TSC打印机打印
                LODOP.PRINT_INITA("0mm", "0mm", "60", "42.1mm", "任务名")
                LODOP.SET_PRINT_PAGESIZE(2, 600, 421, "")

                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 16)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                if (print_info.co_model && TAG_IMG[print_info.co_model].src) {
                    LODOP.ADD_PRINT_IMAGE("2mm","6mm","2.5mm","2.5mm",`<img  border='0' src='${TAG_IMG[print_info.co_model].src}' />`)
                    LODOP.SET_PRINT_STYLEA(0, "Stretch", 2) //(可变形)扩展缩放模式
                    LODOP.SET_PRINT_STYLEA(0, "Vorient", 0)
                }

                //尺码
                LODOP.ADD_PRINT_TEXT("12mm", "6mm", "14mm", "3mm", "尺码:")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                //规格
                var specTop="10.5mm"
                var specSize=13
                if (print_info.spec_value.length >5 && print_info.spec_value.length <=7){
                    specTop="11.5mm"
                    specSize=10
                }else if(print_info.spec_value.length >7){
                    specTop="12.5mm"
                    specSize=8
                }
                LODOP.ADD_PRINT_TEXT(specTop,"14mm","27mm","3mm",print_info.spec_value.substring(0, 12))
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", specSize)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                //横线 实线
                LODOP.ADD_PRINT_LINE("17mm","2mm", "17mm", "RightMargin:2mm",0, 1)

                //品牌
                LODOP.ADD_PRINT_TEXT("18mm", "6mm", "14mm", "3mm", "品牌:")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                //品牌
                LODOP.ADD_PRINT_TEXT("18mm","1mm","RightMargin:3mm","3mm",print_info.brand_name.substring(0, 10))
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                //货号
                LODOP.ADD_PRINT_TEXT("20.5mm", "6mm", "14mm", "3mm", "货号:")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                //货号名称
                LODOP.ADD_PRINT_TEXT("20.5mm","1mm","RightMargin:3mm","3mm",print_info.spu_no.substring(0, 14))
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                //吊牌价
                LODOP.ADD_PRINT_TEXT("23mm", "6mm", "14mm", "3mm", "吊牌价:")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                //吊牌价
                LODOP.ADD_PRINT_TEXT("23mm","1mm","RightMargin:3mm","3mm","￥"+print_info.tag_price)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 5)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                //横线 实线
                LODOP.ADD_PRINT_LINE("26mm","2mm", "26mm", "RightMargin:2mm",0, 1)

                //产地
                LODOP.ADD_PRINT_TEXT("27mm","6mm","8mm","2mm","产地:")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 4)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                LODOP.ADD_PRINT_TEXT("27mm","1mm","RightMargin:3mm","2mm","见吊牌或水洗标")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 4)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                //经销商
                LODOP.ADD_PRINT_TEXT("29mm","6mm","10mm","2mm","经销商:")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 4)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                LODOP.ADD_PRINT_TEXT("29mm","1mm","RightMargin:3mm","2mm","好超值(天津)信息技术有限公司")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 4)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                LODOP.ADD_PRINT_TEXT("31mm","1mm","RightMargin:3mm","2mm","本品牌商品销售价以小程序扫码结果为准")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 4)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3)

                //黑色背景
                // LODOP.ADD_PRINT_HTM("34mm","0mm","50mm","8mm","<div style='background-color: #0A0E11; width: 50mm;height: 8mm;'></div>");//一个矩形设置颜色为#FF0000
                // LODOP.SET_PRINT_STYLE("FontColor", "#fff")
                //销售价 ￥
                LODOP.ADD_PRINT_TEXT("35mm", priceSignleft, "5mm", "4mm", "¥")
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                //元
                LODOP.ADD_PRINT_TEXT("33.5mm", priceOneLeft, priceOneWidth, "4mm", sale_price[0])
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 20)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)
                // LODOP.SET_PRINT_STYLEA(0, "FontColor", "#FFF")

                //角分
                LODOP.ADD_PRINT_TEXT("37mm",priceTwoLeft,"15mm","4mm","." + sale_price[1])
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9)
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1)

                LODOP.SET_PRINT_STYLE("FontColor", "#000")
                //条形码
                LODOP.ADD_PRINT_BARCODE("43.2mm","9mm","37.5mm","8mm","128Auto",print_info.code)
                LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 0)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 2)

                LODOP.ADD_PRINT_TEXT("51mm", "7mm", "34mm", "6mm", print_info.code)
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑")
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 10)
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 2)
                LODOP.SET_PRINT_STYLEA(0, "Horient", 2)

                LODOP.SET_PRINT_COPIES(number) //指定份数
                // LODOP.PRINT_DESIGN();
                //LODOP.SET_PRINTER_INDEX('吊牌打印机');
                if (temp_type == 5) {
                    LODOP.SET_PRINT_STYLEA(0,"AngleOfPageInside",180);
                }
                LODOP.SET_PRINT_MODE("POS_BASEON_PAPER",true);//设置以纸张边缘为基点
                // LODOP.PREVIEW();
                LODOP.PRINT() //执行打印
            }
            else if(temp_type == 6){
                $.ajax({
                    url: 'http://localhost:81/BigoffsLabelPrint',
                    type: 'GET',
                    data:{
                        size: print_info.spec_value,
                        brand_name: print_info.brand_name,
                        spu_no: print_info.spu_no,
                        tag_price: print_info.tag_price,
                        sale_price: print_info.sale_price,
                        code: print_info.code,
                        icon_type: print_info.co_model,
                        temp_type: 2 //在艾利打印机模版标识是2
                    },
                    dataType: 'JSON',
                    success: function (result) {
                        if (result.isCheckDataOk == true){//数据已发送打印机
                            console.log('艾利打印机数据已发送, code'+print_info.code+"; result:", result);
                        }else{
                            layer.msg(result.checkMessage);
                        }
                    },
                    error: function (error) {
                        console.log(typeof (error));
                        layer.msg("吊牌打印失败！")
                    }
                });
            }
            else if(temp_type == 7 || temp_type == 8){
                print_temp_type = 2;
                if (temp_type == 8){
                    print_temp_type = 1;
                }
                $.ajax({
                    url: 'http://localhost:81/BigoffsLabelPrint',
                    type: 'GET',
                    data:{
                        size: print_info.spec_value,
                        brand_name: print_info.brand_name,
                        spu_no: print_info.spu_no,
                        tag_price: print_info.tag_price,
                        sale_price: print_info.sale_price,
                        code: print_info.code,
                        epc_code: print_info.epc_code,
                        icon_type: print_info.co_model,
                        temp_type: print_temp_type
                    },
                    dataType: 'JSON',
                    success: function (result) {
                        if (result.isCheckDataOk == true){//数据已发送打印机
                            console.log('艾利打印机数据已发送, code'+print_info.code+"; result:", result);
                            $('#wait_bind').html(print_info.code+' 打印成功 ');
                        }else{
                            layer.msg(result.checkMessage, {icon: 5,anim: 6});
                            $('#wait_bind').html(print_info.code+' 打印失败 ');
                        }
                    },
                    error: function (error) {
                        console.log(typeof (error));
                        layer.msg("吊牌打印失败！", {icon: 5,anim: 6})
                    }
                });
            }
            else if(temp_type == 9 || temp_type == 10){
                print_temp_type = 2;
                if (temp_type == 10){
                    print_temp_type = 1;
                }
                $.ajax({
                    url: 'http://localhost:82/api/print',
                    type: 'GET',
                    data:{
                        size: print_info.spec_value,
                        brand_name: print_info.brand_name,
                        spu_no: print_info.spu_no,
                        tag_price: print_info.tag_price,
                        sale_price: print_info.sale_price,
                        code: print_info.code,
                        epc_code: print_info.epc_code,
                        icon_type: print_info.co_model,
                        temp_type: print_temp_type
                    },
                    dataType: 'JSON',
                    success: function (result) {
                        console.log("吊牌打印成功");
                        console.log(result);
                        if (result.status == "Print success"){//数据已发送打印机
                            console.log('艾利打印机数据已发送, code'+print_info.code+"; result:", result);
                            $('#wait_bind').html(print_info.code+' 打印成功 ');
                        }else{
                            layer.msg(result.checkMessage, {icon: 5,anim: 6});
                            $('#wait_bind').html(print_info.code+' 打印失败 ');
                        }
                    },
                    error: function (error) {
                        console.log("吊牌打印失败");
                        console.log(typeof (error));
                        layer.msg("吊牌打印失败！", {icon: 5,anim: 6})
                    }
                });
            }
        }
        //清空
        $().click(function () {
            $('#tableb_copy').html($('#tableb').html());
        });
        //条码打印时，加减打印数量
        $(function () {
            //加减
            $(".rate_ul_r_jian").click(function(){
                var values=$(this).siblings(".rate_ul_r_input");
                var valuesNumber=parseFloat(values.val());
                if(valuesNumber==NaN){
                    values.val(0);

                }else{
                    if(valuesNumber<1){
                        values.val(0);
                    }else{
                        values.val(valuesNumber-1);
                    }
                }
            });
            $(".rate_ul_r_jia").click(function(){
                var values=$(this).siblings(".rate_ul_r_input");
                var valuesNumber=parseFloat(values.val());
                if(valuesNumber==NaN){
                    values.val(0);

                }else{
                    if(valuesNumber>=1000000){
                        values.val(1000000);
                    }else{
                        values.val(valuesNumber+1);
                    }
                }
            });
        });
        //自定义验证规则
        form.verify({
            temp_type: function(value){
                if(value.length == 0){
                    return '请选择吊牌模版';
                }
            },
            produce_type: function (value) {
                if(value.length == 0){
                    return '请选择生产类型';
                }
            },
            in_store_type: function (value, data) {
                if(value.length == 0 && $('#produce_type').val() == 2){
                    return '请选择入库方式';
                }
            }
        });
        function checkTempType(sign = 1){// 1调拨店内码  2调拨条码  3新品入库条码
            if ($('#temp_type').val().length == 0){
                $('#temp_type').css('border-color', 'red');
                layer.msg('请选择吊牌模版', {icon: 5,anim: 6});
                if (sign == 1){
                    $('#unique_code').val('');
                    $('#epc_switch_unique').prop("checked", false);
                    $('#unique_code_rfid').val('');
                    $('#epc_switch_unique_rfid').prop("checked", false);
                }else if(sign == 2){
                    $('#barcode').val('');
                    $('#epc_switch_unique').prop("checked", false);
                    $('#barcode_rfid').val('');
                    $('#epc_switch_unique_rfid').prop("checked", false);
                }
                else if(sign == 3){
                    $('#in_store_barode').val('');
                    $('#epc_switch_instore_barcode').prop("checked", false);
                    $('#in_store_barode_rfid').val('');
                    $('#epc_switch_instore_barcode_rfid').prop("checked", false);
                }
                form.render('checkbox')
                return false;
            }

            return true;
        }
        function checkProduceType(sign = 1){// 1调拨店内码  2调拨条码  3新品入库条码
            if ($('#produce_type').val().length == 0){
                $('#produce_type').css('border-color', 'red');
                layer.msg('请选择生产类型', {icon: 5,anim: 6});
                if (sign == 1){
                    $('#unique_code').val('');
                    $('#epc_switch_unique').prop("checked", false);
                    $('#unique_code_rfid').val('');
                    $('#epc_switch_unique_rfid').prop("checked", false);
                }else if(sign == 2){
                    $('#barcode').val('');
                    $('#epc_switch_unique').prop("checked", false);
                    $('#barcode_rfid').val('');
                    $('#epc_switch_unique_rfid').prop("checked", false);
                }else if(sign == 3){
                    $('#in_store_barode').val('');
                    $('#epc_switch_instore_barcode').prop("checked", false);
                    $('#in_store_barode_rfid').val('');
                    $('#epc_switch_instore_barcode_rfid').prop("checked", false);
                }
                form.render('checkbox')
                return false;
            }
            return true;
        }

        /**
         * 扩展Array方法, 去除数组中空白数据
         */
        // Array.prototype.notempty = function() {
        //     var arr = [];
        //     this.map(function(val, index) {
        //         //过滤规则为，不为空串、不为null、不为undefined，也可自行修改
        //         if (val !== "" && val != undefined) {
        //             arr.push(val);
        //         }
        //     });
        //     return arr;
        // }

        $('#barcode').blur(function () {
            $('#barcode').css('border-color', '');
        });
        $('#arrival_order_no').blur(function () {
            $('#arrival_order_no').css('border-color', '');
        });
        $('#arrival_order_no_rfid').blur(function () {
            $('#arrival_order_no_rfid').css('border-color', '');
        });

        //切换吊牌模版
        form.on('select(temp_type)', function(data){
            console.log('吊牌模版', data.value, $('#produce_type').val());

            var e = data.elem;
            //获取选中的文本
            var text =e[e.selectedIndex].text;
            $('#temp_name').html(text);

            if ($('#produce_type').val() == 1 || $('#produce_type').val() == ''){//调拨生产
                //隐藏新品入库div
                $('#in_store_barcode_print_show').html('');
                $('#in_store_barcode_print_show_rfid').html('');
                $('#in_store_print').hide();
                $('#in_store_print_rfid').hide();
                $('#in_store_type_div').hide();
                if(data.value == 3 || data.value == 4 || data.value == 6 || data.value == 5){//rfid吊牌
                    //隐藏普通吊牌的div
                    $('#allot_unique_code_print_show').html('');
                    $('#allot_barcode_print_show').html('');
                    $('#allot_print').hide();
                    //展示rfid的吊牌div
                    element.tabChange('tab_info_rfid', 1);//切换到：店内码
                    $('#allot_unique_code_print_show_rfid').html($('#allot_unique_code_print_rfid').html());
                    $('#allot_barcode_print_show_rfid').html('');
                    $('#allot_print_rfid').show();
                    $('#epc_switch_unique_rfid').prop("checked", false);
                    $('#print_type_rfid').val(1);
                    $('#epc_switch_rfid').show();
                    $('#check_rfid_div').show();
                    $('#unique_code_rfid').focus();
                }else if(data.value == 7 || data.value == 8 || data.value == 9 || data.value == 10){
                    //隐藏普通吊牌的div
                    $('#allot_unique_code_print_show').html('');
                    $('#allot_barcode_print_show').html('');
                    $('#allot_print').hide();

                    //展示rfid的吊牌div
                    element.tabChange('tab_info_rfid', 1);//切换到：店内码
                    $('#allot_unique_code_print_show_rfid').html($('#allot_unique_code_aili_print_bind_rfid').html());
                    $('#allot_barcode_print_show_rfid').html('');
                    $('#allot_print_rfid').show();
                    $('#epc_switch_rfid').hide();
                    $('#print_type_rfid').val(1);
                    $('#check_rfid_div').hide();

                    $('#unique_code').focus();
                }else{//普通吊牌
                    //隐藏rfid吊牌的div
                    $('#allot_unique_code_print_show_rfid').html('');
                    $('#allot_barcode_print_show_rfid').html('');
                    $('#allot_print_rfid').hide();
                    //展示普通吊牌的div
                    element.tabChange('tab_info', 1);//切换到：店内码
                    $('#allot_unique_code_print_show').html($('#allot_unique_code_print').html());
                    $('#allot_barcode_print_show').html('');
                    $('#allot_print').show();
                    $('#epc_switch_unique').prop("checked", false);
                    $('#print_type').val(1);
                    $('#check_rfid_div').show();
                }
            }else{//新品入库
                //隐藏调拨相关div
                $('#allot_barcode_print_show').html('');
                $('#allot_unique_code_print_show').html('');
                $('#allot_print').hide();
                $('#allot_barcode_print_show_rfid').html('');
                $('#allot_unique_code_print_show_rfid').html('');
                $('#allot_print_rfid').hide();
                $('#allot_no').hide();
                //展示新品入库div
                $('#in_store_type_div').show();
                if (data.value == 3 || data.value == 6  || data.value == 5){
                    $('#epc_switch_barcode_rfid').show();
                    //隐藏普通吊牌div
                    $('#in_store_barcode_print_show').html('');
                    $('#in_store_print').hide();
                    //展示rfid吊牌div
                    $('#in_store_barcode_print_show_rfid').html($('#in_store_barcode_print_rfid').html());
                    $('#epc_switch_instore_barcode').prop("checked", false);
                    $('#in_store_print_rfid').show();

                    $('#arrival_order_no_rfid').focus();
                }else if (data.value == 7 || data.value == 8 || data.value == 9 || data.value == 10){
                    //隐藏普通吊牌div
                    $('#in_store_barcode_print_show').html('');
                    $('#in_store_print').hide();
                    //展示rfid吊牌div
                    $('#in_store_barcode_print_show_rfid').html($('#in_store_barcode_aili_print_rfid').html());
                    $('#epc_switch_instore_barcode').prop("checked", false);
                    $('#in_store_print_rfid').show();
                    $('#epc_switch_barcode_rfid').hide();
                    $('#arrival_order_no_rfid').focus();
                }else{
                    //隐藏rfid吊牌div
                    $('#in_store_barcode_print_show_rfid').html('');
                    $('#in_store_print_rfid').hide();
                    //展示普通吊牌div
                    $('#in_store_barcode_print_show').html($('#in_store_barcode_print').html());
                    $('#epc_switch_instore_barcode').prop("checked", false);
                    $('#in_store_print').show();
                }
            }
            if ($('#temp_type').val() != 7 && $('#temp_type').val() != 8) {
                stopAndDisconnect();//关闭epc
            }
            form.render();
        });
        //切换生产类型
        form.on('select(produce_type)', function(data){
            if (data.value == 1){//调拨生产
                console.log('切换生产类型===========， 模版类型：', $('#temp_type').val());
                //隐藏新品入库相关div
                $('#in_store_print').hide();
                $('#in_store_barcode_print_show').html('');
                $('#in_store_barcode_print_show_rfid').html('');
                $('#in_store_print_rfid').hide();
                $('#in_store_type_div').hide();

                // $('#allot_barcode_print_show').html('');

                //展示调拨相关div
                if ($('#temp_type').val() == 3 || $('#temp_type').val() == 4 || $('#temp_type').val() == 6 || $('#temp_type').val() == 5){//rifd吊牌
                    $('#epc_switch_rfid').show();
                    //隐藏普通吊牌的div
                    $('#allot_unique_code_print_show').html('');
                    $('#allot_barcode_print_show').html('');
                    $('#allot_print').hide();
                    //展示rfid的吊牌div
                    element.tabChange('tab_info_rfid', 1);//切换到：店内码
                    $('#allot_unique_code_print_show_rfid').html($('#allot_unique_code_print_rfid').html());
                    $('#allot_barcode_print_show_rfid').html('');
                    $('#allot_print_rfid').show();
                    $('#epc_switch_unique_rfid').prop("checked", false);
                    $('#print_type_rfid').val(1);
                    $('#check_rfid_div').show();
                    $('#unique_code_rfid').focus();

                }else if($('#temp_type').val() == 7 || $('#temp_type').val() == 8 || $('#temp_type').val() == 9 || $('#temp_type').val() == 10){
                    //隐藏普通吊牌的div
                    $('#allot_unique_code_print_show').html('');
                    $('#allot_barcode_print_show').html('');
                    $('#allot_print').hide();

                    //展示rfid的吊牌div
                    element.tabChange('tab_info_rfid', 1);//切换到：店内码
                    $('#allot_unique_code_print_show_rfid').html($('#allot_unique_code_aili_print_bind_rfid').html());
                    $('#allot_barcode_print_show_rfid').html('');
                    $('#allot_print_rfid').show();
                    $('#epc_switch_rfid').hide();
                    $('#print_type_rfid').val(1);
                    $('#check_rfid_div').hide();
                    $('#unique_code').focus();
                }else{//普通吊牌
                    //隐藏rfid吊牌的div
                    $('#allot_unique_code_print_show_rfid').html('');
                    $('#allot_barcode_print_show_rfid').html('');
                    $('#allot_print_rfid').hide();
                    //展示普通吊牌的div
                    element.tabChange('tab_info', 1);//切换到：店内码
                    $('#allot_unique_code_print_show').html($('#allot_unique_code_print').html());
                    $('#allot_barcode_print_show').html('');
                    $('#allot_print').show();
                    $('#epc_switch_unique').prop("checked", false);
                    $('#print_type').val(1);
                    $('#check_rfid_div').show();
                }
                $('#allot_no').show();
            }else if(data.value == 2){//新品入库
                //隐藏调拨相关div
                $('#allot_barcode_print_show').html('');
                $('#allot_unique_code_print_show').html('');
                $('#allot_print').hide();
                $('#allot_barcode_print_show_rfid').html('');
                $('#allot_unique_code_print_show_rfid').html('');
                $('#allot_print_rfid').hide();
                $('#allot_no').hide();
                //展示新品入库div
                $('#in_store_type_div').show();
                if ($('#temp_type').val() == 3 || $('#temp_type').val() == 6 || $('#temp_type').val() == 5){//rfid吊牌
                    //隐藏普通吊牌div
                    $('#epc_switch_barcode_rfid').show();
                    $('#in_store_barcode_print_show').html('');
                    $('#in_store_print').hide();
                    //展示rfid吊牌div
                    $('#in_store_barcode_print_show_rfid').html($('#in_store_barcode_print_rfid').html());
                    $('#epc_switch_instore_barcode').prop("checked", false);
                    $('#in_store_print_rfid').show();

                    $('#arrival_order_no_rfid').focus();
                }else if ($('#temp_type').val() == 7 || $('#temp_type').val() == 8 || $('#temp_type').val() == 9 || $('#temp_type').val() == 10){
                    //隐藏普通吊牌div
                    $('#in_store_barcode_print_show').html('');
                    $('#in_store_print').hide();
                    //展示rfid吊牌div
                    $('#in_store_barcode_print_show_rfid').html($('#in_store_barcode_aili_print_rfid').html());
                    $('#epc_switch_barcode_rfid').hide();
                    $('#in_store_print_rfid').show();

                    $('#arrival_order_no_rfid').focus();
                }else{//普通吊牌
                    //隐藏rfid吊牌div
                    $('#in_store_barcode_print_show_rfid').html('');
                    $('#in_store_print_rfid').hide();
                    //展示普通吊牌div
                    $('#in_store_barcode_print_show').html($('#in_store_barcode_print').html());
                    $('#epc_switch_instore_barcode').prop("checked", false);
                    $('#in_store_print').show();
                }
            }
            if ($('#temp_type').val() != 7 && $('#temp_type').val() != 8){
                stopAndDisconnect();//关闭epc
            }
            form.render();
        });

        /***调拨生产 普通吊牌 && 艾利打印机***/
        //调拨生产 店内码打印吊牌
        function print_unique_tag(info) {
            console.log('店内码打印吊牌');
            if (info.unique_code.length == 0){
                $('#unique_code').css('border-color', 'red');
                layer.msg('请扫描店内码', {icon: 5,anim: 6});
                return false;
            }

            if ( ($('#temp_type').val() == 7 || $('#temp_type').val() == 8) && ($('#print_type_rfid').val() == 2 || $('#in_store_type').val() == 2)){
                layer.msg("艾利打印机仅支持支店内码打印！",{icon:2,time:3500});
                return false;
            }

            info.print_type = 1;
            //rfid开关 0关闭 1开启
            if($('#epc_switch_unique').is(":checked")){
                info.epc_switch = 1;
            }else{
                info.epc_switch = 0;
            }
            info.unique_code = info.unique_code.replace(/^\s+|\s+$/g,'');

            //调拨生产 检测店内码是否出库
            if(info.produce_type == 1){
                $.post('/tagPrint/checkUniqueOut', info, function (r) {
                    console.log('r======================', r);
                    if (r.code === 200 && r.data.status == 2) {
                        layer.msg("店内码已出库", {
                            icon: 0,
                            time: 3000
                        });
                    }
                });
            }


            $.post('/tagPrint/printTag', info, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                console.log('返回结果111：', r);
                if (r.code === 200) {
                    if (info.epc_code != '') {
                        goodsCodes.push(info.epc_code) // 合并数组
                    }
                    console.log('goods codes======================', goodsCodes);
                    //展示商品基础信息
                    show_print_tag_info(r.data.tag_info, info.epc_switch);
                    //打印吊牌
                    console.log('==================================**********************************========================================');
                    print_tag(r.data.tag_info, $('#temp_type').val(), 1);
                    $('#unique_code').val('');
                } else {
                    layer.msg(r.msg, {
                        icon: 2,
                        time: 3000
                    });
                    $('#unique_code').val('');
                    $('#print_unique_info').html('');
                    $('#wait_bind').html('');
                }
                loading.hide();
            });
        }

        //调拨 条码打印吊牌
        function print_barcode_tag(info) {
            $('#wait_bind').html('');
            $('#print_unique_info').html('');

            if (info.barcode.length == 0){
                $('#barcode').css('border-color', 'red');
                layer.msg('请扫描条形码', {icon: 5,anim: 6});
                return false;
            }
            info.print_type = 2;

            //rfid开关 0关闭 1开启
            if($('#epc_switch_unique').is(":checked")){
                info.epc_switch = 1;
            }else{
                info.epc_switch = 0;
            }

            if(info.epc_switch == 0){//条码且未开启rfid
                $('#print_barcode_num').val(1);
                layer.open({
                    type: 1
                    ,title:'条码数量'
                    ,area: ['400px', '200px']
                    ,content: $('#print_price_nums')
                    ,btn: ['确定', '取消']
                    ,success: function(layero, index){
                        this.enterConfirm = function(event){
                            if(event.keyCode === 13){
                                $(".layui-layer-btn0").click();
                                return false; //阻止系统默认回车事件
                            }
                        };
                        $(document).on('keydown', this.enterConfirm); //监听键盘事件

                        // 点击确定按钮回调事件
                        $(".layui-layer-btn0").on("click",function() {
                            info.barcode_num = $('#print_barcode_num').val();
                            layer.close(index);
                            print_allot_tag(info, info.barcode_num);
                        })
                    }
                    ,end: function(){
                        $(document).off('keydown', this.enterConfirm); //解除键盘事件
                    }
                    // ,yes: function(index, layero){
                    //     info.barcode_num = $('#print_barcode_num').val();
                    //     layer.close(index);
                    //     print_allot_tag(info, info.barcode_num);
                    // }
                    ,btnAlign: 'c'
                });
                $('#print_barcode_num').focus();
            }else{
                info.barcode_num = 1;
                print_allot_tag(info, 1);
            }
        }

        //调拨条码打印吊牌
        function print_allot_tag(info, barcode_nums) {
            console.log('调拨 条码 打印吊牌');
            $.post('/tagPrint/printTag', info, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    if (info.epc_code != '') {
                        goodsCodes.push(info.epc_code) // 合并数组
                    }
                    console.log('goods codes======================', goodsCodes);
                    //展示商品基础信息
                    show_print_tag_info(r.data.tag_info, info.epc_switch);
                    //打印吊牌
                    print_tag(r.data.tag_info, $('#temp_type').val(), barcode_nums);
                    $('#barcode').val('');
                } else {
                    layer.msg(r.msg, {
                        icon: 2,
                        time: 3000
                    });
                    $('#barcode').val('');
                    $('#print_unique_info').html('');
                    $('#wait_bind').html('');
                }
                loading.hide();
            });
        }

        //调拨生产 普通吊牌 tab标签切换
        element.on('tab(tab_info)', function(){
            $('#print_type').val(this.getAttribute('lay-id'));
            if (this.getAttribute('lay-id') == 1){//店内码
                $('#allot_unique_code_print_show').html($('#allot_unique_code_print').html());
                $('#allot_barcode_print_show').html('');
                $('#unique_code').focus();
                $('#epc_switch').css('margin-top', '2.5%');
                form.render();
            }else{//条形码
                $('#allot_barcode_print_show').html($('#allot_barcode_print').html());
                $('#allot_unique_code_print_show').html('');
                $('#barcode').focus();
                $('#epc_switch').css('margin-top', '8.8%');
                form.render();
            }
        });

        //调拨生产 普通吊牌 店内码 打印按钮 打印吊牌
        form.on('submit(allot_print_uniqe)', function(data){
            print_unique_tag(data.field);
            return false;
        });

        //调拨生产 普通吊牌 条形码 打印按钮 打印吊牌
        form.on('submit(allot_print_barcode)', function(data){
            print_barcode_tag(data.field);
            return false;
        });

        //调拨生产 普通吊牌 店内码 回车打印吊牌
        $("body").on("keydown", "#unique_code", function (e) {
            if(e.keyCode == 13) {
                data = layui.form.val(`tag_form`);
                if (!checkTempType(1)){
                    return false;
                }
                if (!checkProduceType(1)){
                    return false;
                }
                print_unique_tag(data);
                return false;
            }
        });

        //调拨生产 普通吊牌 条码 回车打印吊牌
        $("body").on("keydown", "#barcode", function (e) {
            if(e.keyCode == 13) {
                console.log('调拨条码打印');
                data = layui.form.val(`tag_form`);
                if (!checkTempType(2)){
                    return false;
                }
                if (!checkProduceType(2)){
                    return false;
                }
                console.log('调拨条码打印===============', data);
                print_barcode_tag(data);
                return false;
            }
        });

        /***新品入库***/
        //新品入库 条码打印吊牌
        function print_instore_barcode_tag(info) {
            $('#print_unique_info').html('');
            $('#wait_bind').html('');

            if (info.arrival_order_no.length == 0){
                $('#arrival_order_no').css('border-color', 'red');
                layer.msg('请输入到货预约单号', {icon: 5,anim: 6});
                $('#arrival_order_no').focus();
                return false;
            }
            if (info.in_store_barode.length == 0){
                $('#in_store_barode').css('border-color', 'red');
                layer.msg('请扫描条形码', {icon: 5,anim: 6});
                return false;
            }
            info.print_type = 2;
            //rfid开关 0关闭 1开启
            if($('#epc_switch_instore_barcode').is(":checked")){
                info.epc_switch = 1;
            }else{
                info.epc_switch = 0;
            }

            if($('#in_store_type').val() == 2 && info.epc_switch == 0){//条码入库且未开启rfid
                $('#print_barcode_num').val(1);
                layer.open({
                    type: 1
                    ,title:'条码数量'
                    ,area: ['400px', '200px']
                    ,content: $('#print_price_nums')
                    ,btn: ['确定', '取消']
                    ,success: function(layero, index){
                        this.enterConfirm = function(event){
                            if(event.keyCode === 13){
                                $(".layui-layer-btn0").click();
                                return false; //阻止系统默认回车事件
                            }
                        };
                        $(document).on('keydown', this.enterConfirm); //监听键盘事件

                        // 点击确定按钮回调事件
                        $(".layui-layer-btn0").on("click",function() {
                            info.barcode_num = $('#print_barcode_num').val();
                            layer.close(index);
                            print_instore_tag(info, info.barcode_num);
                        })
                    }
                    ,end: function(){
                        $(document).off('keydown', this.enterConfirm); //解除键盘事件
                    }

                    // ,yes: function(index, layero){
                    //     info.barcode_num = $('#print_barcode_num').val();
                    //     layer.close(index);
                    //     print_instore_tag(info, info.barcode_num);
                    // }
                    ,btnAlign: 'c'
                });
                $('#print_barcode_num').focus();
            }else{//店内码入库 或 条码入库且开启rfid
                info.barcode_num = 1;
                print_instore_tag(info, 1);
            }
        }

        //新品入库打印吊牌
        function print_instore_tag(info, barcode_nums) {

            if ( ($('#temp_type').val() == 7 || $('#temp_type').val() == 8) && ($('#print_type_rfid').val() == 2 || $('#in_store_type').val() == 2)){
                layer.msg("艾利打印机仅支持支店内码打印！",{icon:2,time:3500});
                return false;
            }

            $.post('/tagPrint/printTag', info, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    if (info.epc_code != '') {
                        goodsCodes.push(info.epc_code) // 合并数组
                    }
                    console.log('goods codes======================', goodsCodes);
                    //展示商品基础信息
                    show_print_tag_info(r.data.tag_info, info.epc_switch);
                    //打印吊牌
                    print_tag(r.data.tag_info, $('#temp_type').val(), barcode_nums);
                    $('#in_store_barode').val('');
                } else {
                    layer.msg(r.msg, {
                        icon: 2,
                        time: 3000
                    });
                    $('#in_store_barode').val('');
                    $('#print_unique_info').html('');
                    $('#wait_bind').html('');
                }
                loading.hide();
            });
        }

        //新品入库 普通吊牌 到货预约单回车操作
        $("body").on("keydown", "#arrival_order_no", function (e) {
            if(e.keyCode == 13) {
                $("#in_store_barode").focus();
                return false;
            }
        });

        //新品入库 普通吊牌 打印按钮 打印吊牌
        form.on('submit(instore_print_barcode)', function(data){
            print_instore_barcode_tag(data.field);
            return false;
        });

        //新品入库 监听 回车打印新品入库吊牌
        $("body").on("keydown","#in_store_barode", function (e) {
            if(e.keyCode == 13) {
                data = layui.form.val(`tag_form`);
                if (!checkTempType(3)){
                    return false;
                }
                if (!checkProduceType(3)){
                    return false;
                }
                print_instore_barcode_tag(data);
                return false;
            }
        });


        /***调拨生产 rfid吊牌***/
        //调拨生产 店内码 打印rfid吊牌
        function print_unique_tag_rfid(info) {
            console.log('店内码打印吊牌 rfid', info);
            if (info.unique_code.length == 0){
                $('#unique_code_rfid').css('border-color', 'red');
                layer.msg('请扫描店内码', {icon: 5,anim: 6});
                return false;
            }
            info.print_type = 1;
            //rfid开关 0关闭 1开启
            info.epc_switch = 0;//传递关闭值，先打印吊牌，后绑定epc
            info.unique_code = info.unique_code.replace(/^\s+|\s+$/g,'');

            //调拨生产 检测店内码是否出库
            if(info.produce_type == 1){
                $.post('/tagPrint/checkUniqueOut', info, function (r) {
                    if (r.code === 200 && r.data.status == 2) {
                        layer.msg("店内码已出库", {
                            icon: 0,
                            time: 3000
                        });
                    }
                });
            }

            $.post('/tagPrint/printTagRfid', info, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                console.log('返回结果：', r);
                if (r.code === 200) {
                    // if (info.epc_code != '') {
                    //     goodsCodes.push(info.epc_code) // 合并数组
                    // }
                    //赋值价签打印的log_id,绑定rfid时需要更新上epc码
                    $('#tag_print_log_id').val(r.data.tag_print_log_id);
                    //展示商品基础信息
                    show_print_tag_info(r.data.tag_info, info.epc_switch);
                    //打印吊牌
                    print_tag(r.data.tag_info, $('#temp_type').val(), 1);
                    //如果epc不为空，则绑定
                    if (info.epc_code != ''){
                        bindEpc();
                    }
                } else {
                    layer.msg(r.msg, {
                        icon: 2,
                        time: 3000
                    });
                    $('#unique_code_rfid').val('');
                    $('#print_unique_info').html('');
                    $('#wait_bind').html('');
                }
                loading.hide();
            });
        }

        //调拨生产 条形码 打印rfid吊牌
        function print_barcode_tag_rfid(info) {
            $('#wait_bind').html('');
            $('#print_unique_info').html('');

            if (info.barcode.length == 0){
                $('#barcode').css('border-color', 'red');
                layer.msg('请扫描条形码', {icon: 5,anim: 6});
                return false;
            }
            info.print_type = 2;
            //rfid开关 0关闭 1开启
            if($('#epc_switch_unique_rfid').is(":checked")){
                info.epc_switch = 1;
            }else{
                info.epc_switch = 0;
            }

            if(info.epc_switch == 0){//条码且未开启rfid
                $('#print_barcode_num').val(1);
                layer.open({
                    type: 1
                    ,title:'条码数量'
                    ,area: ['400px', '200px']
                    ,content: $('#print_price_nums')
                    ,btn: ['确定', '取消']
                    // ,yes: function(index, layero){
                    //     info.barcode_num = $('#print_barcode_num').val();
                    //     layer.close(index);
                    //     print_allot_tag_rfid(info, info.barcode_num);
                    // }
                    ,success: function(layero, index){
                        this.enterConfirm = function(event){
                            if(event.keyCode === 13){
                                $(".layui-layer-btn0").click();
                                return false; //阻止系统默认回车事件
                            }
                        };
                        $(document).on('keydown', this.enterConfirm); //监听键盘事件

                        // 点击确定按钮回调事件
                        $(".layui-layer-btn0").on("click",function() {
                            info.barcode_num = $('#print_barcode_num').val();
                            layer.close(index);
                            print_allot_tag_rfid(info, info.barcode_num);
                        })
                    }
                    ,end: function(){
                        $(document).off('keydown', this.enterConfirm); //解除键盘事件
                    }
                    ,btnAlign: 'c'
                });
                $('#print_barcode_num').focus();
            }else{
                info.barcode_num = 1;
                print_allot_tag_rfid(info, 1);
            }
        }

        //调拨生产 调拨条码rfid吊牌
        function print_allot_tag_rfid(info, barcode_nums) {
            console.log('调拨 条码 rfid 打印吊牌', info);

            if (info.barcode.length == 0){
                $('#barcode_rfid').css('border-color', 'red');
                layer.msg('请扫条形码', {icon: 5,anim: 6});
                return false;
            }
            info.print_type = 2;
            //rfid开关 0关闭 1开启
            info.epc_switch = 0;//传递关闭值，先打印吊牌，后绑定epc

            $.post('/tagPrint/printTagRfid', info, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    // if (info.epc_code != '') {
                    //     goodsCodes.push(info.epc_code) // 合并数组
                    // }
                    //赋值价签打印的log_id,绑定rfid时需要更新上epc码
                    $('#tag_print_log_id').val(r.data.tag_print_log_id);
                    //展示商品基础信息
                    show_print_tag_info(r.data.tag_info, info.epc_switch);
                    //打印吊牌
                    print_tag(r.data.tag_info, $('#temp_type').val(), barcode_nums);
                    //如果epc不为空，则绑定
                    if (info.epc_code != ''){
                        bindEpc();
                    }
                } else {
                    layer.msg(r.msg, {
                        icon: 2,
                        time: 3000
                    });
                    $('#barcode_rfid').val('');
                    $('#print_unique_info').html('');
                    $('#wait_bind').html('');
                }
                loading.hide();
            });
        }

        //调拨生产 rfid 切换标签
        element.on('tab(tab_info_rfid)', function(){
            $('#print_type_rfid').val(this.getAttribute('lay-id'));
            if (this.getAttribute('lay-id') == 1){//店内码
                if($('#temp_type').val() == 7){
                    $('#allot_unique_code_print_show_rfid').html($('#allot_unique_code_aili_print_bind_rfid').html());
                    $('#allot_barcode_print_show_rfid').html('');
                    $('#allot_print_rfid').show();
                    $('#epc_switch_rfid').hide();
                    $('#print_type_rfid').val(1);

                    $('#unique_code_rfid').focus();
                }else{
                    $('#allot_unique_code_print_show_rfid').html($('#allot_unique_code_print_rfid').html());
                    $('#allot_barcode_print_show_rfid').html('');
                    $('#unique_code_rfid').focus();
                    $('#epc_switch_rfid').css('margin-top', '8.9%');
                }
                form.render();
            }else{//条形码
                if ( $('#temp_type').val() == 7 || $('#temp_type').val() == 8){
                    layer.msg("艾利打印机仅支持支店内码打印！",{icon:2,time:3500});
                    return false;
                }

                $('#allot_barcode_print_show_rfid').html($('#allot_barcode_print_rfid').html());
                $('#allot_unique_code_print_show_rfid').html('');
                $('#barcode_rfid').focus();
                $('#epc_switch_rfid').css('margin-top', '15.2%');
                form.render();
            }
        });

        //调拨生产 rfid吊牌 监听条形码回车操作
        $("body").on("keydown", "#barcode_rfid", function (e) {
            if(e.keyCode == 13) {
                console.log('rfid吊牌 调拨条码打印');
                data = layui.form.val(`tag_form`);
                if (!checkTempType(2)){
                    return false;
                }
                if (!checkProduceType(2)){
                    return false;
                }
                console.log('调拨条码打印 rfid===============', data);
                $('#barcode_rfid').blur();
                print_barcode_tag_rfid(data);
                return false;
            }
        });

        //调拨生产 rfid吊牌 条形码 打印按钮 获取吊牌信息
        form.on('submit(allot_print_barcode_rfid)', function(data){
            data.field.epc_code = '';
            print_barcode_tag_rfid(data.field);
            return false;
        });

        //调拨生产 rfid吊牌 条码绑定rfid 点击绑定按钮操作
        form.on('submit(allot_bind_barcode_rfid)', function (data) {
            bindEpc();
        })

        //调拨生产 rfid吊牌 监听店内码回车操作 获取吊牌信息
        $("body").on("keydown", "#unique_code_rfid", function (e) {
            if(e.keyCode == 13) {
                data = layui.form.val(`tag_form`);
                if (!checkTempType(1)){
                    return false;
                }
                if (!checkProduceType(1)){
                    return false;
                }
                $('#unique_code_rfid').blur();
                print_unique_tag_rfid(data);
                return false;
            }
        });

        //调拨生产 rfid吊牌 店内码绑定rfid 点击绑定按钮操作
        form.on('submit(allot_bind_uniqe_rfid)', function (data) {
            bindEpc();
        });

        //调拨生产 rfid吊牌 店内码 打印按钮 获取商品信息
        form.on('submit(allot_print_uniqe_rfid)', function(data){
            data.field.epc_code = '';
            print_unique_tag_rfid(data.field);
            return false;
        });


        /***新品入库 rfid吊牌***/
        //新品入库 rfid吊牌 到货预约单回车操作
        $("body").on("keydown", "#arrival_order_no_rfid", function (e) {
            if(e.keyCode == 13) {
                $("#in_store_barode_rfid").focus();
                return false;
            }
        });

        //新品入库 rfid吊牌 条码打印吊牌
        function print_instore_barcode_tag_rfid(info) {
            $('#print_unique_info').html('');
            $('#wait_bind').html('');

            if (info.arrival_order_no.length == 0){
                $('#arrival_order_no_rfid').css('border-color', 'red');
                layer.msg('请输入到货预约单号', {icon: 5,anim: 6});
                $('#arrival_order_no_rfid').focus();
                return false;
            }
            if (info.in_store_barode.length == 0){
                $('#in_store_barode_rfid').css('border-color', 'red');
                layer.msg('请扫描条形码', {icon: 5,anim: 6});
                $('#in_store_barode_rfid').focus();
                return false;
            }
            info.print_type = 2;
            //rfid开关 0关闭 1开启
            if($('#epc_switch_instore_barcode_rfid').is(":checked")){
                info.epc_switch = 1;
            }else{
                info.epc_switch = 0;
            }

            if($('#in_store_type').val() == 2 && info.epc_switch == 0){//条码入库且未开启rfid
                $('#print_barcode_num').val(1);
                layer.open({
                    type: 1
                    ,title:'条码数量'
                    ,area: ['400px', '200px']
                    ,content: $('#print_price_nums')
                    ,btn: ['确定', '取消']
                    // ,yes: function(index, layero){
                    //     info.barcode_num = $('#print_barcode_num').val();
                    //     layer.close(index);
                    //     print_instore_tag_rfid(info, info.barcode_num);
                    // }
                    ,success: function(layero, index){
                        this.enterConfirm = function(event){
                            if(event.keyCode === 13){
                                $(".layui-layer-btn0").click();
                                return false; //阻止系统默认回车事件
                            }
                        };
                        $(document).on('keydown', this.enterConfirm); //监听键盘事件

                        // 点击确定按钮回调事件
                        $(".layui-layer-btn0").on("click",function() {
                            info.barcode_num = $('#print_barcode_num').val();
                            layer.close(index);
                            print_instore_tag_rfid(info, info.barcode_num);
                        })
                    }
                    ,end: function(){
                        $(document).off('keydown', this.enterConfirm); //解除键盘事件
                    }
                    ,btnAlign: 'c'
                });
                $('#print_barcode_num').focus();
            }else{//店内码入库 或 条码入库且开启rfid
                info.barcode_num = 1;
                print_instore_tag_rfid(info, 1);
            }
        }

        //新品入库 打印rfid吊牌
        function print_instore_tag_rfid(info, barcode_nums) {
            console.log('新品入库 条码 rfid 打印吊牌', info);

            //rfid开关 0关闭 1开启
            info.epc_switch = 0;//传递关闭值，先打印吊牌，后绑定epc
            $.post('/tagPrint/printTagRfid', info, function (r) {
                var loading = $(this).find('.layui-icon');
                if (loading.is(":visible")) return;
                loading.show();
                if (r.code === 200) {
                    $('#in_store_barode_rfid').blur();
                    // if (info.epc_code != '') {
                    //     goodsCodes.push(info.epc_code) // 合并数组
                    // }
                    // console.log('goods codes======================', goodsCodes);
                    //赋值价签打印的log_id,绑定rfid时需要更新上epc码
                    $('#tag_print_log_id').val(r.data.tag_print_log_id);
                    //展示商品基础信息
                    show_print_tag_info(r.data.tag_info, info.epc_switch);
                    //打印吊牌
                    print_tag(r.data.tag_info, $('#temp_type').val(), barcode_nums);
                    $('#in_store_unique_code_rfid').val(r.data.tag_info.code);
                    $('#in_store_barode').val('');
                    //如果epc不为空，则绑定
                    if (info.epc_code != ''){
                        bindEpc();
                    }
                } else {
                    layer.msg(r.msg, {
                        icon: 2,
                        time: 3000
                    });
                    $('#in_store_barode').val('');
                    $('#in_store_unique_code_rfid').val('');
                    $('#print_unique_info').html('');
                    $('#wait_bind').html('');
                }
                loading.hide();
            });
        }

        //新品入库 rfid吊牌 监听 监听条形码回车 获取吊牌信息
        $("body").on("keydown","#in_store_barode_rfid", function (e) {
            if(e.keyCode == 13) {
                data = layui.form.val(`tag_form`);
                if (!checkTempType(3)){
                    return false;
                }
                if (!checkProduceType(3)){
                    return false;
                }
                print_instore_barcode_tag_rfid(data);
                return false;
            }
        });

        //新品入库 rfid吊牌 打印按钮 打印吊牌
        form.on('submit(instore_print_barcode_rfid)', function(data){
            print_instore_barcode_tag_rfid(data.field);
            return false;
        });

        //新品入库 rfid吊牌 绑定按钮
        form.on('submit(instore_print_barcode_bind_rfid)', function (data) {
            bindEpc();
        });

        //rfid吊牌 将商品和epc绑定
        function bindEpc() {
            info = layui.form.val(`tag_form`);
            info.is_again_print = 0;
            info.print_type = info.print_type_rfid;
            epc_code = info.epc_code;

            if (info.produce_type == 2 && (info.temp_type == 3 || info.temp_type == 6 || info.temp_type == 5)){
                info.unique_code = info.in_store_unique_code;
            }

            console.log('bind epc info:', info);
            $.ajax({
                url: '/tagPrint/bindEpc',
                type: 'post',
                data: JSON.stringify(info),
                dataType: "json",
                contentType: 'application/json',
                processData: false,
                success: function (data) {
                    if (data.code !== 200 && data.data.is_again_print == 1) {
                        layer.open({
                            type: 1
                            ,title: false //不显示标题栏
                            ,closeBtn: false
                            ,area: '300px;'
                            ,shade: 0.8
                            ,id: 'LAY_layuipro' //设定一个id，防止重复弹出
                            ,btn: ['确定', '取消']
                            ,btnAlign: 'c'
                            ,moveType: 1 //拖拽模式，0或者1
                            ,shadeClose: true
                            ,content: '<div style="padding: 50px; line-height: 22px; background-color: #393D49; color: #fff; font-weight: 300;">此店内码已被绑定RFID，是否重新绑定？</div>'
                            ,success: function(layero, index){
                                this.enterConfirm = function(event){
                                    if(event.keyCode === 13){
                                        $(".layui-layer-btn0").click();
                                        return false; //阻止系统默认回车事件
                                    }
                                };
                                $(document).on('keydown', this.enterConfirm); //监听键盘事件

                                // 点击确定按钮回调事件
                                $(".layui-layer-btn0").on("click",function() {
                                    info.is_again_print = 1;
                                    $.ajax({
                                        url: '/tagPrint/bindEpc',
                                        type: 'post',
                                        data: JSON.stringify(info),
                                        dataType: "json",
                                        contentType: 'application/json',
                                        processData: false,
                                        success: function (data) {
                                            $('#wait_bind').html('绑定成功');
                                            $("#epc_code").val('');
                                            if ($('#print_type_rfid').val() == 1 || $('#print_type_rfid').val() == ''){
                                                $('#unique_code_rfid').val('');
                                            }else if($('#print_type_rfid').val() == 2){
                                                $('#barcode').val('');
                                            }
                                            goodsCodes.push(epc_code) // 合并数组
                                            layer.close(layer.index);
                                        },
                                        error: function (e) {
                                            layer.alert("绑定RFID失败！")
                                        },
                                    });
                                })


                                this.escQuit = function(event){
                                    if(event.keyCode === 0x1B){
                                        layer.close(index);
                                        return false; //阻止系统默认回车事件
                                    }
                                };
                                $(document).on('keydown', this.escQuit); //监听键盘事件


                            },
                            end: function(){
                                $('#unique_code_rfid').focus();
                                $(document).off('keydown', this.enterConfirm); //解除键盘事件
                                $(document).off('keydown', this.escQuit); //解除键盘事件
                            }
                        });
                    }else if(data.code == 200 && data.data == true){
                        $('#print_unique_info').html('');
                        $('#wait_bind').html('绑定成功');
                        $("#epc_code").val('');
                        if ($('#produce_type').val() == 1){
                            if ($('#print_type_rfid').val() == 1 || $('#print_type_rfid').val() == ''){
                                $('#unique_code_rfid').val('');
                                $('#unique_code_rfid').focus();
                            }else if($('#print_type_rfid').val() == 2){
                                $('#barcode_rfid').val('');
                                $('#barcode_rfid').focus();
                            }
                        }else{
                            $("#in_store_barode_rfid").val('');
                            $("#in_store_unique_code_rfid").val('');
                        }
                        goodsCodes.push(epc_code) // 合并数组
                    }else{
                        layer.open({
                            type: 1
                            ,title: false //不显示标题栏
                            ,closeBtn: false
                            ,area: '300px;'
                            ,shade: 0.8
                            ,id: 'LAY_001' //设定一个id，防止重复弹出
                            ,content: '<div style="padding: 50px; line-height: 22px; background-color: #393D49; color: #fff; font-weight: 300;">'+data.msg+'</div>'
                            ,btn: ['确定']
                            ,btnAlign: 'c'
                            ,success: function(layero, index){
                                this.enterConfirm = function(event){
                                    if(event.keyCode === 13){
                                        $(".layui-layer-btn0").click();
                                        return false; //阻止系统默认回车事件
                                    }
                                };
                                $(document).on('keydown', this.enterConfirm); //监听键盘事件

                                // 点击确定按钮回调事件
                                $(".layui-layer-btn0").on("click",function() {
                                })
                            },
                            end: function(){
                                $(document).off('keydown', this.enterConfirm); //解除键盘事件
                                if(data.code == 32000){
                                    if (info.produce_type == 2 && info.temp_type == 3){
                                        $('#in_store_barode_rfid').focus();
                                    }else if($('#print_type_rfid').val() == 1){
                                        $('#unique_code_rfid').focus();
                                    }else{
                                        $('#barcode_rfid').focus();
                                    }
                                }
                            }
                        });
                    }
                },
                error: function (e) {
                    layer.alert("绑定RFID失败！")
                },
            });
        }

        //rfid吊牌 切换入库方式
        form.on('select(in_store_type)', function(data){
            $('#arrival_order_no_rfid').focus();
            stopAndDisconnect();//关闭epc
            form.render();
        });


        /***调拨生产 艾利打印机rfid吊牌打印且绑定***/
        //调拨生产 普通吊牌 店内码 回车打印吊牌
        $("body").on("keydown", "#unique_code_aili_rfid", function (e) {
            if(e.keyCode == 13) {
                data = layui.form.val(`tag_form`);
                if (!checkTempType(1)){
                    return false;
                }
                if (!checkProduceType(1)){
                    return false;
                }
                print_unique_tag(data);
                return false;
            }
        });
    })
</script>
</body>
</html>
