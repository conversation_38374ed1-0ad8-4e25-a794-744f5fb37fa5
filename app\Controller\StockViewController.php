<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Constants\SerialType;
use App\Exception\BusinessException;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\CategoryServiceInterface;
use App\JsonRpc\PurchasePriceServiceInterface;
use App\JsonRpc\SerialNoServiceInterface;
use App\JsonRpc\SkuServiceInterface;
use App\JsonRpc\StockViewServiceInterface;
use App\JsonRpc\TaskServiceInterface;
use App\JsonRpc\ProduceAreaServiceInterface;
use App\Library\Facades\SyncTaskService;
use App\Library\StockView\StockViewCommon;
use App\Service\CommonService;
use App\Service\HashService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use App\Library\Facades\AdminService;
use function foo\func;
use function React\Promise\map;

/**
 * @Controller()
 */
class StockViewController extends AbstractController
{

    const CACHE_EXPIRE = 60*60*24;
    const MAX_EXPORT = 100000;
    const MAX_HAND_LOCK = 50000;
    const EXPORT_BATCH_SIZE = 10000;
    const MAX_HAND_NUM = 50000;

    // 设置PHP超时时间
    const MAX_EXECUTION_TIME = '100000';
    // 设置PHP临时允许内存大小
    const MEMORY_LIMIT = '256M';

    const SPEC_KEY_EC_MAP = [
//        'goods_size' => '商品尺码',
//        'color' => '颜色',
//        'size' => '尺寸',
//        'cloth_size' => '服装尺码',
//        'material' => '材质',
//        'length' => '长度',
//        'weight' => '重量',
//        'tastes' => '口味',
//        'volume' => '容量',
//        'packing_size' => '包装箱规格',
        'spu_gender' => '性别',
        'spu_season' => '季节',
//        'spec_key1' => '规格项1',
//        'spec_val1' => '规格值1',
//        'spec_key2' => '规格项2',
//        'spec_val2' => '规格值2',
    ];

    const UNIQUE_CODE_HEAD = [
//        'result' => '导出结果',
        'purchase_no' => '采购单',
        'sup_name' => '供应商',
        'pur_name' => '采购商',
        'co_model_text' => '合作模式',
        'settle_way_text' => '结算模式',
        'cost_price' => '成本价',
        'tag_price' => '吊牌价',
        'sale_price' => '售价',
        'in_ware_age' => '库龄(天)',
        'image' => '图片',
        'warehouse_name' => '仓库',
        'shelf_code' => '货架号',
        'spu_id' => 'SPU',
        'sku_id' => 'SKU',
        'spu_no' => '货号',
//            'spec_names' => '尺码',
        'barcode' => '条形码',
        'brand_name' => '品牌',
        "category_names" => '品类',
        'cate1' => '大类',
        'cate2' => '中类',
        'cate3' => '小类',
        'in_stock_no' => '批次号',
        'unique_code' => '店内码',
        'epc_code' => 'EPC',
        'is_imperfect_name' => '残次类型',
        'lock_type_text' => '锁定类型',
        'in_store_time' => '入库时间',
        'latest_in_store_time' => '最新入库时间',
        'updated_at' => '更新时间',
//        'tallied_at' => '最近理货时间',
        'pick_admin_name' => '拣货人',
        'task_no' => '任务单号',
        'in_w_name' => '调入仓',
        'status_name' => '状态',

    ];

    const SHELF_STOCK_HEAD = [
        'image' => '图片',
        'warehouse_name' => '仓库',
        'shelf_code' => '货架号',
        'spu_id' => 'SPU',
        'sku_id' => 'SKU',
        'spu_no' => '货号',
//            'combination' => '尺码',
        'barcode' => '条形码',
        'brand_name' => '品牌',
        'cate1' => '大类',
        'cate2' => '中类',
        'cate3' => '小类',
        'num' => '数量',
        'b_num' => '条码数量',
        'u_num' => '店内码数量',
        'spu_season' => '季节',
        'spu_gender' => '性别',
        'goods_name' => '商品名称',
        'goods_sale_price' => '销售价'
    ];

    /**
     * @Inject ()
     * @var HashService
     */
    private $HashService;

    /**
     * @Inject ()
     * @var CategoryServiceInterface
     */
    private $CategoryService;

    /**
     * @Inject ()
     * @var PurchasePriceServiceInterface
     */
    private $PurchasePriceService;

    /**
     * @Inject ()
     * @var SkuServiceInterface
     */
    private $SkuService;

    /**
     * @Inject ()
     * @var StockViewServiceInterface
     */
    private $StockViewService;

    /**
     * @Inject ()
     * @var SerialNoServiceInterface
     */
    private $SerialNoService;

    /**
     * @Inject()
     * @var TaskServiceInterface
     */
    private $TaskService;

    /**
     * @Inject()
     * @var ProduceAreaServiceInterface
     */
    private $ProduceAreaService;

    /**
     * 店内码库存查询列表
     * @RequestMapping(path="/stockView/allList", methods="get,post")
     */
    public function allList(RequestInterface $request)
    {
        $params = $request -> all();

        if (isset($params['where']['codes']) && !empty($params['where']['codes'])) {
            $params['where']['codes'] = explode(PHP_EOL,$params['where']['codes']);
        }
        if (isset($params['where']['brand_id']) && !empty($params['where']['brand_id'])) {
            $params['where']['brand_id'] = explode(',',$params['where']['brand_id']);
        }
        if (isset($params['where']['category_id']) && !empty($params['where']['category_id'])) {
            $params['where']['category_id'] = explode(',',$params['where']['category_id']);
        }
        $userInfo = $this->getUserInfo();
        $adminInfo = ['admin_id' => $userInfo['uid'],'admin_name' => $userInfo['nickname']];
        if ($request -> isMethod('POST')) {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 10;
            $where = $params['where'] ?? [];
            $export = $params['export'] ?? 0;
            try {
                $authBrandIds = getBrandIdsByAdminId(intval($adminInfo['admin_id']));
                logger() -> debug('品牌权限ids',$authBrandIds);
                $where['auth_brand_ids'] = $authBrandIds;
                $adminId = $this->getUserId();
                $authWIds = AdminService::organizeWareHouseData($adminId);
                if (1 == $export) {

                    ///////////////////////////////////////////统一下载-start/////////////////////////////////////
                    // 异步任务导出
                    $where['auth_w_ids'] = $authWIds;
                    $where['admin_id'] = $adminInfo['admin_id'];
                    $taskId = $this -> asyncExport(
                        ['where' => $where,'admin_info' => $adminInfo],
                        array_merge(self::SHELF_STOCK_HEAD,self::SPEC_KEY_EC_MAP),
                        'stock_view/getShelfStockExportList',
                        '货架库存导出-',
                        'shelf_stock_export_params_'.$userInfo['uid']
                    );
//                    $ret = $this -> StockViewService -> getList($authWIds,$adminId,(int)$export,(int)$page,(int)$pageSize,$where);
                    return $this -> returnApi(ResponseCode::SUCCESS,'任务已创建，请到统一下载任务列表查看',['task_id' => $taskId]);
                } else {
                    if(isset($where['page'])) unset($where['page']);
                    if(isset($where['limit'])) unset($where['limit']);
                    var_dump('库存列表=======');
                    $ret = $this -> StockViewService -> getList($authWIds,$adminId,(int)$export,(int)$page,(int)$limit,$where);
                }

                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
            } catch (\Exception $exception) {
                $ret = $exception -> getMessage();
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
            }

        }
        return $this -> show("/stockView/allList");
    }

    public function allList_bak(RequestInterface $request)
    {
        $params = $request -> all();

        if (isset($params['where']['codes']) && !empty($params['where']['codes'])) {
            $params['where']['codes'] = explode(PHP_EOL,$params['where']['codes']);
        }
        if (isset($params['where']['brand_id']) && !empty($params['where']['brand_id'])) {
            $params['where']['brand_id'] = explode(',',$params['where']['brand_id']);
        }

        if ($request -> isMethod('POST')) {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 10;
            $where = $params['where'] ?? [];
            $export = $params['export'] ?? 0;
            try {
                $adminId = $this->getUserId();
                $authWIds = AdminService::organizeWareHouseData($adminId);
                if (1 == $export) {

                    if(isset($where['page'])) unset($where['page']);
                    if(isset($where['limit'])) unset($where['limit']);

                    // 获取总数
                    $total = $this -> StockViewService -> getShelfStockListTotal($authWIds,$where);
                    var_dump('库存导出====总数=='.$total);
                    if ($total > self::MAX_EXPORT) {
                        throw new BusinessException('导出数据总量不能超过'.self::MAX_EXPORT.'！');
                    }

                    // 分批次获取数据并逐次写入
                    $filename = '库存导出_'.date( 'YmdHis' ) . '.xls';
                    $writer = new \XLSXWriter();
                    $sheet1 = 'sheet1';
                    $pageSize = self::EXPORT_BATCH_SIZE;
                    $times = ceil($total/$pageSize);
                    var_dump('库存导出总数为：'.$total.'，需要分批'.$times.'次导出');
                    // 根据总数分批获取
                    $dataTotal = 0;
                    $header = array_merge(self::SHELF_STOCK_HEAD,self::SPEC_KEY_EC_MAP);
                    if (!empty( $header )) {
                        $writer->writeSheetRow( $sheet1, array_values( $header ) );
                    }
                    for ($i = 1;$i<=$times;$i++) {
                        var_dump('第'.$i.'次导出');
                        $t1 = microtime(true);
                        $ret = $this -> StockViewService -> getList($authWIds,$adminId,(int)$export,(int)$i,(int)$pageSize,$where);
                        // 取出的数据
                        $data = $ret['data'];

                        $headerKeys = array_keys( $header );
                        foreach ($data as $value) {
                            $row = [];
                            foreach ($headerKeys as $key) {
                                // 为防止导出文件字段显示成科学计数，以下字段添加反引号 '`'
                                $val = $value[$key] ?? '';
                                $row[] = in_array($key,['spu_id','sku_id','spu_no','barcode']) ? '`'.$val : $val;
                            }
                            $writer->writeSheetRow( $sheet1, $row );
                            $dataTotal++;
                        }
                        var_dump('第'.$i.'次导出耗时：'.number_format(microtime(true)-$t1, 3, '.', '').' s');

                    }

                    // 临时路径
                    $baseDir = 'runtime/';
                    $filePath = $baseDir . $filename;
                    // 输出二进制流到本地文件
                    $t1 = microtime(true);
                    $writer->writeToFile( $filePath );
                    var_dump('输出二进制流到本地文件数据总数：'.$dataTotal.'，耗时：'.number_format(microtime(true)-$t1, 3, '.', '').' s');

                    $t1 = microtime(true);
                    $url = upload( $filename, $filePath, 5 );
                    var_dump('上传至OSS耗时：'.number_format(microtime(true)-$t1, 3, '.', '').' s');

                    unlink( $filePath );
                    return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['url'=>$url]);
                } else {
                    if(isset($where['page'])) unset($where['page']);
                    if(isset($where['limit'])) unset($where['limit']);
                    var_dump('库存列表=======');
                    $ret = $this -> StockViewService -> getList($authWIds,$adminId,(int)$export,(int)$page,(int)$limit,$where);
                }

                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
            } catch (\Exception $exception) {
                $ret = $exception -> getMessage();
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
            }

        }
        return $this -> show("/stockView/allList");
    }

    private function _delDataForShelfStockExport($adminId) {
        var_dump('清除库存查询上次留下的数据');
        $redis = redis();
        $notifyKey = 'stock_query_notify_key'.$adminId;
        $headKey = 'stock_query_head_key'.$adminId;
        $pipelineKey = 'stock_query_key_'.$adminId;
        $res1 = $redis -> del($notifyKey);
        $res2 = $redis -> del($headKey);
        $res3 = $redis -> del($pipelineKey);
        return $res1&&$res2&&$res3;
    }

    private function _delDataForUniqueCodeExport($adminId) {
        var_dump('清除店内码查询上次留下的数据');
        $redis = redis();
        $notifyKey = 'unique_code_query_notify_key'.$adminId;
        $headKey = 'unique_code_query_head_key'.$adminId;
        $pipelineKey = 'unique_code_query_key_'.$adminId;
        $res1 = $redis -> del($notifyKey);
        $res2 = $redis -> del($headKey);
        $res3 = $redis -> del($pipelineKey);
        var_dump('店内码查询res1=',$res1);
        var_dump('店内码查询res2=',$res2);
        var_dump('店内码查询res3=',$res3);
        return $res1&&$res2&&$res3;
    }

    /**
     * 前端轮询获取库存查询结果
     * @RequestMapping(path="/stockView/getAllListNotifyResult", methods="get,post")
     */
    public function getAllListNotifyResult(RequestInterface $request)
    {
        $adminId = $this -> getUserId();
        if (!$adminId) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, "请登录" );
        }
        //$params = $request->all();
        $redis = redis();
        $notifyKey = 'stock_query_notify_key'.$adminId;
        $headKey = 'stock_query_head_key'.$adminId;
        $pipelineKey = 'stock_query_key_'.$adminId;
        $notifyRet = $redis -> get($notifyKey);
        $headKeyRet = $redis -> get($headKey);
        if ($headKeyRet) $headKeyRet = json_decode($headKeyRet,true);
        if ('1' == $notifyRet) {
            ini_set('max_execution_time', self::MAX_EXECUTION_TIME);// 设置PHP超时时间
            ini_set('memory_limit', self::MEMORY_LIMIT);// 设置PHP临时允许内存大小
            $ret = $this -> HashService ->getDataAllFromHash($pipelineKey);
            // 取出来完，就清理本次缓存的数据，避免在下载过程中轮询重复请求取出
            $r = $this -> _delDataForShelfStockExport($adminId);
            if($ret && $r) {
                // 追加规格信息
                //$this -> _appendShelfStockData($ret);
                $fileName = '库存统计_';
                $url = exportToExcel($headKeyRet,$ret,$fileName);
                $returnData = [
                    'result' => "1",
                    'msg' => '请稍候...',
                    'data' => ['url' => $url],
                ];

            }
            return $this -> returnApi(ResponseCode::SUCCESS,$returnData['msg'],$returnData);
        } else {
            $returnData = [
                'result' => "0",
                'msg' => '无结果',
                'data' => [],
            ];
            return $this -> returnApi(ResponseCode::SERVER_ERROR,$returnData['msg'],$returnData);
        }

    }

    /**
     * 店内码库存查询列表
     * @RequestMapping(path="/stockView/uniqueCodeList", methods="get,post")
     */
    public function uniqueCodeList(RequestInterface $request)
    {
        $params = $request -> all();
        $params = $this ->handleParams($params);
        $userInfo = $this->getUserInfo();
        $adminInfo = ['admin_id' => $userInfo['uid'],'admin_name' => $userInfo['nickname']];
        $wId = $params['w_id'] ?? 0;
        $inStockNo = $params['in_stock_no'] ?? '';
        $shelfCode = $params['shelf_code'] ?? '';
        $skuId = $params['sku_id'] ?? 0;
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;
        $where = $params['where'] ?? [];
        $export = $params['export'] ?? 0;
        $adminId = $this -> getUserId();

        // 进入列表页是不是初始化进来的，若初始化，则传给前端，前端不请求接口
        $is_init = 1;
        logger() -> debug('请求对象信息',[$request -> isMethod('GET'),$params]);
        if ($request -> isMethod('GET') && ($wId || $inStockNo || $shelfCode || $skuId || $params['type'] == 'link')) {
            $is_init = 0;
        }

        //获取生产区的店内码
        $produceUniques = [];
        if (isset($params['area_type']) && !empty($params['area_type']) && isset($params['source_order_no']) && !empty($params['source_order_no'])){
            $is_init = 0;
            $source_order_no = $params['source_order_no'] ?? '';
            $produceAreaWhere = [
                "sign_type" => 1,
                "source_order_nos" => [$params['source_order_no']],
            ];
            switch ($params['area_type']){
                case "reback":#已回库
                    $produceAreaWhere['reback'] = true;
                    break;
                case "surplus":#剩余
                    $produceAreaWhere['surplus'] = true;
                    break;
                default:
                    break;
            }
            $produceAreaInfo = $this->ProduceAreaService->getDetailS($produceAreaWhere);
            $produceUniques = implode("\n", array_filter(array_column($produceAreaInfo, 'unique_code')));
        }

        if ($request -> isMethod('POST')) {
            try {
                $authWIds = AdminService::organizeWareHouseData($adminId);
                if ($wId) {
                    array_push($authWIds,$wId);
                }
                if ($shelfCode && !empty($where['shelf_code'])) {
                    array_push($where['shelf_code'],$shelfCode);
                } else {
                    if ($shelfCode) {
                        $where['shelf_code'] = [$shelfCode];
                    }
                }
                if ((int)$skuId > 0 && !empty($where['sku_id'])) {
                    array_push($where['sku_id'],$skuId);
                } else {
                    if ((int)$skuId > 0) {
                        $where['sku_id'] = [$skuId];
                    }
                }
                if (1 == $export) {
                    $canExport = StockViewCommon::canExport(['redis_key'=> StockViewCommon::getExportKey(['admin_id' => $adminId])]);
                    logger() -> debug('是否正在导出',[$canExport]);
                    if (!$canExport) {
                        return $this -> returnApi(ResponseCode::SERVER_ERROR,'正在导出，请稍候再试');
                    }
                    $where['auth_w_ids'] = $authWIds;
                    $where['admin_id'] = $adminInfo['admin_id'];
                    $taskId = $this -> exportAsync(['where' => $where, 'admin_info' => $adminInfo,]);
                    return $this -> returnApi(ResponseCode::SUCCESS,'任务已创建，请到统一下载任务列表查看',['task_id' => $taskId]);
                }
                else {
                    if(isset($where['page'])) unset($where['page']);
                    if(isset($where['limit'])) unset($where['limit']);
                    var_dump('店内码列表=======');
//                    $ret = $this -> StockViewService -> getUniqueCodeList($authWIds,$adminId,$export,(int)$page,(int)$limit,$where);
                    $ret = $this -> StockViewService -> getUniqueCodeList($authWIds,$adminId,$export,(int)$page,(int)$limit,$where);
                }

                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
            } catch (\Exception $exception) {
                $ret = $exception -> getMessage();
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
            }

        }

        return $this -> show("/stockView/uniqueCodeList",[
            'is_init'=>$is_init,
            'w_id'=>$wId,
            'in_stock_no'=>$inStockNo,
            'shelf_code'=>$shelfCode,
            'sku_id'=>$skuId,
            'produce_uniques' => $produceUniques
            ]
        );
    }

    /**
     * 店内码库存查询列表(新)
     * @RequestMapping(path="/stockView/uniqueCodeListNew", methods="get,post")
     */
    public function uniqueCodeListNew(RequestInterface $request)
    {
        $params = $request -> all();
        $params = $this ->handleParams($params);
        $userInfo = $this->getUserInfo();
        $adminInfo = ['admin_id' => $userInfo['uid'],'admin_name' => $userInfo['nickname']];
        $wId = $params['w_id'] ?? 0;
        $inStockNo = $params['in_stock_no'] ?? '';
        $shelfCode = $params['shelf_code'] ?? '';
        $skuId = $params['sku_id'] ?? 0;
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;
        $where = $params['where'] ?? [];
        $export = $params['export'] ?? 0;
        $adminId = $this -> getUserId();

        //获取生产区的店内码
        $produceUniques = [];
        if (isset($params['area_type']) && !empty($params['area_type']) && isset($params['source_order_no']) && !empty($params['source_order_no'])){
            $source_order_no = $params['source_order_no'] ?? '';
            $produceAreaWhere = [
                "sign_type" => 1,
                "source_order_nos" => [$params['source_order_no']],
            ];
            switch ($params['area_type']){
                case "reback":#已回库
                    $produceAreaWhere['reback'] = true;
                    break;
                case "surplus":#剩余
                    $produceAreaWhere['surplus'] = true;
                    break;
                default:
                    break;
            }
            $produceAreaInfo = $this->ProduceAreaService->getDetailS($produceAreaWhere);
            $produceUniques = implode("\n", array_filter(array_column($produceAreaInfo, 'unique_code')));
        }

        if ($request -> isMethod('POST')) {
            try {
                $authWIds = AdminService::organizeWareHouseData($adminId);
                if ($wId) {
                    array_push($authWIds,$wId);
                }
                if ($shelfCode && !empty($where['shelf_code'])) {
                    array_push($where['shelf_code'],$shelfCode);
                } else {
                    if ($shelfCode) {
                        $where['shelf_code'] = [$shelfCode];
                    }
                }
                if ((int)$skuId > 0 && !empty($where['sku_id'])) {
                    array_push($where['sku_id'],$skuId);
                } else {
                    if ((int)$skuId > 0) {
                        $where['sku_id'] = [$skuId];
                    }
                }
                $authBrandIds = getBrandIdsByAdminId(intval($adminInfo['admin_id']));
                logger() -> debug('品牌权限ids',$authBrandIds);
                $where['auth_brand_ids'] = $authBrandIds;
                $where['auth_w_ids'] = $authWIds;
                $where['admin_id'] = $adminInfo['admin_id'];
                if(1 == $export) { // 新导出
                    $total = $this -> StockViewService -> getUniqueTotal($where);
                    logger() -> debug('实时导出总数',[$total]);
                    // 如果总数大于100000
                    if ($total > 100000) {
                        return $this -> returnApi(ResponseCode::SERVER_ERROR,'下载数量过多！');
                    }

                    $canExport = StockViewCommon::canExport(['redis_key'=> StockViewCommon::getExportKey(['admin_id' => $adminId])]);
                    logger() -> debug('是否正在导出',[$canExport]);
                    if (!$canExport) {
                        return $this -> returnApi(ResponseCode::SERVER_ERROR,'正在导出，请稍候再试');
                    }

                    $taskId = $this -> newExportAsync(['where' => $where, 'admin_info' => $adminInfo,]);
                    return $this -> returnApi(ResponseCode::SUCCESS,'任务已创建，请到统一下载任务列表查看',['task_id' => $taskId]);
                }
                if(2 == $export) { // ads导出
                    $total = $this -> StockViewService -> getUniqueTotalAds($where);
                    logger() -> debug('ads导出总数',[$total]);
                    $canExport = StockViewCommon::canExport(['redis_key'=> StockViewCommon::getExportKey(['admin_id' => $adminId])]);
                    logger() -> debug('是否正在导出',[$canExport]);
                    if (!$canExport) {
                        return $this -> returnApi(ResponseCode::SERVER_ERROR,'正在导出，请稍候再试');
                    }
                    $taskId = $this -> exportAsyncAds(['where' => $where, 'admin_info' => $adminInfo,]);
                    return $this -> returnApi(ResponseCode::SUCCESS,'任务已创建，请到统一下载任务列表查看',['task_id' => $taskId]);
                }
                else {
                    if(isset($where['page'])) unset($where['page']);
                    if(isset($where['limit'])) unset($where['limit']);
                    $ret = $this -> StockViewService -> getUniqueCodeListNew($authWIds,$adminId,$export,(int)$page,(int)$limit,$where);
                }

                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
            } catch (\Exception $exception) {
                $ret = $exception -> getMessage();
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
            }

        }

        return $this -> show("/stockView/uniqueCodeList",[
                'w_id'=>$wId,
                'in_stock_no'=>$inStockNo,
                'shelf_code'=>$shelfCode,
                'sku_id'=>$skuId,
                'produce_uniques' => $produceUniques
            ]
        );
    }

    private function handleParams($params) {
        if (isset($params['where']['unique_code']) && !empty($params['where']['unique_code'])) {
            $params['where']['unique_code'] = explode(PHP_EOL,$params['where']['unique_code']);
        }

        if (isset($params['where']['spu_no']) && !empty($params['where']['spu_no'])) {
            $params['where']['spu_no'] = explode(PHP_EOL,$params['where']['spu_no']);
        }

        if (isset($params['where']['epc_code']) && !empty($params['where']['epc_code'])) {
            $params['where']['epc_code'] = explode(PHP_EOL,$params['where']['epc_code']);
        }

        if (isset($params['where']['barcode']) && !empty($params['where']['barcode'])) {
            $params['where']['barcode'] = explode(PHP_EOL,$params['where']['barcode']);
        }

        if (isset($params['where']['spu_id']) && !empty($params['where']['spu_id'])) {
            $params['where']['spu_id'] = explode(PHP_EOL,$params['where']['spu_id']);
        }

        if (isset($params['where']['sku_id']) && !empty($params['where']['sku_id'])) {
            $params['where']['sku_id'] = explode(PHP_EOL,$params['where']['sku_id']);
        }
        if (isset($params['where']['arrival_shop_at']) && !empty($params['where']['arrival_shop_at'])) {
            $params['where']['arrival_shop_at'] = explode(PHP_EOL,$params['where']['arrival_shop_at']);
        }

        if (isset($params['where']['in_stock_no']) && !empty($params['where']['in_stock_no'])) {
            $params['where']['in_stock_no'] = explode(PHP_EOL,$params['where']['in_stock_no']);
        }

        if (isset($params['where']['purchase_no']) && !empty($params['where']['purchase_no'])) {
            $params['where']['purchase_no'] = explode(PHP_EOL,$params['where']['purchase_no']);
        }

        if (isset($params['where']['shelf_code']) && !empty($params['where']['shelf_code'])) {
            $params['where']['shelf_code'] = explode(PHP_EOL,$params['where']['shelf_code']);
        }

        if (isset($params['where']['is_imperfect']) && !empty($params['where']['is_imperfect'])) {
            $params['where']['is_imperfect'] = explode(',',$params['where']['is_imperfect']);
        }
        if (isset($params['where']['status']) && !empty($params['where']['status'])) {
            $params['where']['status'] = explode(',',$params['where']['status']);
        }
        if (isset($params['where']['brand_id']) && !empty($params['where']['brand_id'])) {
            $params['where']['brand_id'] = explode(',',$params['where']['brand_id']);
        }
        if (isset($params['where']['sup_id']) && !empty($params['where']['sup_id'])) {
            $params['where']['sup_id'] = explode(',',$params['where']['sup_id']);
        }
        if (isset($params['where']['w_id']) && !empty($params['where']['w_id'])) {
            $params['where']['w_id'] = explode(',',$params['where']['w_id']);
        }

        unset($params['where']['date_range']);
        return $params;
    }

    public function appendData(&$data)
    {
        if ($data) {
            $combinations = join(',',array_column($data,'combination'));

            $combinations = $combinations ? $combinations : '';
            $specRes =  $this -> SkuService -> getSpecNameBatch(explode(',',$combinations));
            $specRes = $specRes ? array_column($specRes,NULL,'spec_kv_id') : [];

            // 追加成本价
            $where = [
                'time' => currentTime(),
                'sku_list' => $data
            ];
            //$skuCostPriceMap = $this -> PurchasePriceService -> getSkuPrice($where);
            $wIds = array_column($data,'w_id');
            $inStockNos = array_column($data,'in_stock_no');
            $skuIds = array_column($data,'sku_id');
            $skuCostPriceMap = $this -> StockViewService -> costPriceList($wIds,$inStockNos,$skuIds);

            // spu性别
            $spuIds = array_column($data,'spu_id');
            $SpuProperties = $this -> StockViewService -> getSpuProperties($spuIds);
            $SpuPropertiesGroup = collect($SpuProperties) -> groupBy('spu_id');


            // 类目
            $cateIds = array_column($data,'category_id');
            $CategoryNamePath = $this -> CategoryService -> getCategoryNamePathByBatch($cateIds);
            $CategoryNamePathMap = array_column($CategoryNamePath,NULL,'id');

            foreach ($data as &$v) {
                $combination = $v['combination'] ? $v['combination'] : '';
                $combinationArr = explode(',',$combination);
                foreach ($combinationArr as $v2) {
                    $v['spec_names'][] =  $specRes[$v2]['spec_name'];
                }

                // sku成本价
                //$uniqueKey = $v['sup_id'].'_'.$v['co_model'].'_'.$v['settle_way'].'_'.$v['sku_id'];
                $key = $v['w_id'].'_'.$v['in_stock_no'].'_'.$v['sku_id'];
                //$costPrice = $skuCostPriceMap[$uniqueKey]['price'];
                $costPrice = $skuCostPriceMap[$key]['price'] ?? 0;
                $v['cost_price'] = fen2yuan($costPrice);

                // 类目路径
                $v['name_path'] = $CategoryNamePathMap[$v['category_id']]['name_path'];

                // spu属性-性别
                $spuPropertyList = $SpuPropertiesGroup -> get($v['spu_id']);
                $spuProperty = collect($spuPropertyList) -> where('property_key_id',1) -> values()-> all(); // 属性key，性别=1
                $v['spu_gender'] = $spuProperty[0]['property_value_name'] ?? '';

                // 追加店内码日志最新操作类型
//                $where2 = [
//                    'unique_code' => $v['unique_code'],//'NR000000051'
//                ];
//                $uLog = getUniqueCodeLog($where2);
//                var_dump('uLog==',$uLog);
//                if (!empty($uLog['data'])) {
//                    $v['unique_code_op_type_name'] = PublicCode::UNIQUE_CODE_LOG_TYPE[$uLog['data'][0]['operation_type']];
//                } else {
//                    $v['unique_code_op_type_name'] = '';
//                }

            }
        }
    }


    /**
     * 店内码详情
     * @RequestMapping(path="/stockView/uniqueCodeDetail", methods="get,post")
     */
    public function uniqueCodeDetail(RequestInterface $request)
    {
        $params = $request -> all();
        $unique_code = $params['unique_code'] ?? '';
        $uDetail = $this -> StockViewService -> getUniqueCodeOne($unique_code);

        // 追加尺码信息
        if ($uDetail['combination']) {

            // 追加规格信息
            //$commService = new CommonService;
            //$commService -> appendSpec($billListData,array_column($billListData,'combination'),'size','尺码');
            $specRes =  $this -> SkuService -> getSpecNameBatch(explode(',',$uDetail['combination']));
            $specRes = $specRes ? array_column($specRes,NULL,'spec_kv_id') : [];
            $combinationArr = explode(',',$uDetail['combination']);
            $specs = [];
            foreach ($combinationArr as $v2) {
                array_push($specs,$specRes[$v2]['spec_name']);
            }
            $uDetail['spec_names'] = join(',',$specs);
        }
        return $this -> show("/stockView/uniqueCodeDetail",['detail' => $uDetail]);
    }

    /**
     * 店内码日志列表
     * @RequestMapping(path="/stockView/getUniqueLogList", methods="get,post")
     */
    public function getUniqueLogList(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'unique_code' => 'required|string',
        ],[
            'unique_code.required'=> '店内码必传',
            'unique_code.string'=> '店内码必须是字符串',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        try {
            $where = [
                'unique_code' => $params['unique_code']
            ];
            $ret = getUniqueCodeLog($where,1,10000);
            foreach ($ret['data'] as &$v) {
                $v['operation_type'] = PublicCode::UNIQUE_CODE_LOG_TYPE[$v['operation_type']];
            }
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }

    }

    private function _getSpecKeysAssoc($spec_names_union) {

        $speckKeyCEMap = array_flip(self::SPEC_KEY_EC_MAP);

        $result = [];
        foreach ($spec_names_union as $name) {
            foreach ($speckKeyCEMap as $c => $e) {
                if ($c == $name) {
                    $result[$e] = $c;
                }
            }
        }
        return $result;
    }

    ///////////////////////////////////////////////////店内码查询及导出/////////////////////////////////
    /**
     * 前端轮询获取店内码查询结果
     * @RequestMapping(path="/stockView/getUniqueCodeListNotifyResult", methods="get,post")
     */
    public function getUniqueCodeListNotifyResult(RequestInterface $request)
    {
        $adminId = $this -> getUserId();
        if (!$adminId) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, "请登录" );
        }
        //$params = $request->all();

        $redis = redis();
        $notifyKey = 'unique_code_query_notify_key'.$adminId;
        $headKey = 'unique_code_query_head_key'.$adminId;
        $pipelineKey = 'unique_code_query_key_'.$adminId;
        $notifyRet = $redis -> get($notifyKey);
        $headKeyRet = $redis -> get($headKey);
        if ($headKeyRet) $headKeyRet = json_decode($headKeyRet,true);
        if ('1' == $notifyRet) {
            ini_set('max_execution_time', self::MAX_EXECUTION_TIME);// 设置PHP超时时间
            ini_set('memory_limit', self::MEMORY_LIMIT);// 设置PHP临时允许内存大小
            $ret = $this -> HashService ->getDataAllFromHash($pipelineKey);
            logger() -> debug('redis取出结果=====',[$ret]);
            // 取出来完，就清理本次缓存的数据，避免在下载过程中轮询重复请求取出
            $r = $this -> _delDataForUniqueCodeExport($adminId);
            if($ret && $r) {
                // 追加规格信息
                //$this -> _appendShelfStockData($ret);
                $fileName = '店内码库存统计_';
                $url = exportToExcel($headKeyRet,$ret,$fileName);
                $returnData = [
                    'result' => "1",
                    'msg' => '请稍候...',
                    'data' => ['url' => $url],
                ];

            }
            return $this -> returnApi(ResponseCode::SUCCESS,$returnData['msg'],$returnData);
        } else {
            $returnData = [
                'result' => "0",
                'msg' => '无结果',
                'data' => [],
            ];
            return $this -> returnApi(ResponseCode::SERVER_ERROR,$returnData['msg'],$returnData);
        }

    }

    /**
     * 手动锁定
     * @RequestMapping(path="/stockView/handLock", methods="get,post")
     */
    public function handLock(RequestInterface $request)
    {
        $params = $request -> all();
        $params = $this ->handleParams($params);

        $where = $params['where'] ?? [];
//        $where['status'] = $where['status'][0];
        $userInfo = $this->getUserInfo();
        $authWIds = AdminService::organizeWareHouseData($userInfo['uid']);
        $execParams = [
            'auth_w_ids' => $authWIds,
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname'],
            'where' => $where,
        ];
        logger()->debug('手动锁定参数====',[$execParams]);
        try {
            $where['auth_w_ids'] = $execParams['auth_w_ids'];
            $where['admin_id'] = $execParams['admin_id'];
            $this -> _checkTotal($where);
            $key = 'unique_code_hand_lock_key_'.$userInfo['uid'];
            // 参数存入redis
            $redis = redis();
            $redis ->set($key,json_encode([$execParams]));
            // 开始锁定，走异步任务
            $taskParams = [];
            $taskParams['service_name'] = 'stock-view:handLock';
            $taskParams['admin_id'] = $userInfo['uid'];
            $taskParams['admin_name'] = $userInfo['nickname'];
            $taskParams['task_name'] = '店内码批量手工锁定';
            $taskParams['sys_type'] = 'wms';
            $taskParams['params'] = json_encode([['data_redis_key' => $key]]);
            $taskInfo = SyncTaskService::add($taskParams,60);

            return $this->returnApi(ResponseCode::SUCCESS, '提交成功，任务将异步处理，执行结果请查看异步列表！',$taskInfo);
        } catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }

    }

    /**
     * 手动锁定
     * @RequestMapping(path="/stockView/handUnlock", methods="get,post")
     */
    public function handUnlock(RequestInterface $request)
    {
        $params = $request -> all();
        $params = $this ->handleParams($params);

        $where = $params['where'] ?? [];
//        $where['status'] = $where['status'][0];
        $userInfo = $this->getUserInfo();
        $authWIds = AdminService::organizeWareHouseData($userInfo['uid']);
        // 默认过滤掉暂存区货架 暂存区货架以ZCQ开头
        $where['not_zcq_pre'] = StockViewCommon::SHELF_CODE_PRE;
        $execParams = [
            'auth_w_ids' => $authWIds,
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname'],
            'where' => $where,
        ];
        logger()->debug('手动解锁参数====',[$execParams]);
        try {
            $where['auth_w_ids'] = $execParams['auth_w_ids'];
            $where['admin_id'] = $execParams['admin_id'];
            $this -> _checkTotal($where);
            $key = 'unique_code_hand_unlock_key_'.$userInfo['uid'];
            // 参数存入redis
            $redis = redis();
            $redis -> set($key,json_encode([$execParams]));
            // 开始解锁，走异步任务
            $taskParams = [];
            $taskParams['service_name'] = 'stock-view:handUnlock';
            $taskParams['admin_id'] = $userInfo['uid'];
            $taskParams['admin_name'] = $userInfo['nickname'];
            $taskParams['task_name'] = '店内码批量手工解锁';
            $taskParams['sys_type'] = 'wms';
            $taskParams['params'] = json_encode([['data_redis_key' => $key]]);
            $taskInfo = SyncTaskService::add($taskParams,60);

            return $this->returnApi(ResponseCode::SUCCESS, '提交成功，任务将异步处理，执行结果请查看异步列表！',$taskInfo);
        } catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }

    }

    private function _checkTotal($where) {
        // 获取总数
//        $where['status'] = [$where['status']];
        $total = $this -> StockViewService -> getUniqueTotal($where);
        logger() -> debug('操作总数为：'.$total);
        if ($total > self::MAX_HAND_NUM) {
            throw new BusinessException('操作数量超过最大限制 '.self::MAX_HAND_NUM.' 条');
        }

        if ((int)$total === 0) {
            throw new BusinessException('操作数量为0，请选择正确的查询条件！');
        }
    }

    private function exportAsync($params)
    {
        logger() -> debug('异步导出任务-入参',[$params]);
        $headerMap = array_merge(self::UNIQUE_CODE_HEAD,self::SPEC_KEY_EC_MAP);
        //导出表头
        $header = ['fields' => array_keys($headerMap),'names' => array_values($headerMap)];

        $userInfo = $this->getUserInfo();
        $key = 'unique_code_export_params_'.$userInfo['uid'];
        // 参数存入redis
        $redis = redis();
        $redis -> set($key,json_encode([$params['where']]));
        $redis -> expire($key,self::CACHE_EXPIRE);
        //添加任务
        $data['serial_no'] = $this->SerialNoService->generate( SerialType::WO_PROMOTION );
        $data['name'] = '店内码导出-'.date('YmdHis');
        $data['where'] = json_encode(['data_redis_key'=>$key], JSON_UNESCAPED_UNICODE);
        $data['status'] = 1;
        $data['admin_id'] = $params['admin_info']['admin_id'];
        $data['admin_name'] = $params['admin_info']['admin_name'];
        $data['header'] = json_encode($header, JSON_UNESCAPED_UNICODE);
        $data['type'] = 2;
        $data['service'] = 'stock_view/getUniqueCodeExportList';
        $data['is_limit'] = 1;
        $data['system'] = 'wms';
        logger() -> debug('异步导出任务-data',[$data]);
        try {
            return $this->TaskService->addTask($data);
        } catch (\Throwable $e) {
            logger() -> info('创建导出任务单失败',[$e->getMessage()]);
            throw new BusinessException($e ->getMessage());
        }

    }

    private function asyncExport($params,$headerMap,$service,$filename,$key)
    {
        logger() -> debug('货架库存异步导出任务-入参',[$params]);
        //导出表头
        $header = ['fields' => array_keys($headerMap),'names' => array_values($headerMap)];

        // 参数存入redis
        $redis = redis();
        $redis -> set($key,json_encode([$params['where']]));
        $redis -> expire($key,self::CACHE_EXPIRE);
        //添加任务
        $data['serial_no'] = $this->SerialNoService->generate( SerialType::WO_PROMOTION );
        $data['name'] = $filename.date('YmdHis');
        $data['where'] = json_encode(['data_redis_key'=>$key], JSON_UNESCAPED_UNICODE);
        $data['status'] = 1;
        $data['admin_id'] = $params['admin_info']['admin_id'];
        $data['admin_name'] = $params['admin_info']['admin_name'];
        $data['header'] = json_encode($header, JSON_UNESCAPED_UNICODE);
        $data['type'] = 2;
        $data['service'] = $service;
        $data['is_limit'] = 0;
        $data['system'] = 'wms';
        logger() -> debug('异步导出任务-data',[$data]);
        try {
            return $this->TaskService->addTask($data);
        } catch (\Throwable $e) {
            logger() -> info('创建导出任务单失败',[$e->getMessage()]);
            throw new BusinessException($e ->getMessage());
        }

    }

    private function newExportAsync($params)
    {
        logger() -> debug('新异步导出任务-入参',[$params]);
        $headerMap = array_merge(self::UNIQUE_CODE_HEAD,self::SPEC_KEY_EC_MAP);
        $fields = ['purchase_no','sup_name','pur_name','co_model_text','settle_way_text','cost_price','tag_price','sale_price','in_ware_age','image','warehouse_name','shelf_code','spu_id','sku_id','spu_no','barcode','brand_name','category_names','in_stock_no','unique_code','epc_code','is_imperfect_name','lock_type_text','in_store_time','latest_in_store_time','updated_at','pick_admin_name','task_no','in_w_name','status_name','spu_gender','spu_season'];
        $names = ['采购单','供应商','采购商','合作模式','结算模式','成本价','吊牌价','售价','库龄(天)','图片','仓库','货架号','SPU','SKU','货号','条形码','品牌','大类','中类','小类','批次号','店内码','EPC','残次类型','锁定类型','入库时间','最新入库时间','更新时间','拣货人','任务单号','调入仓','状态','性别','季节'];
        //导出表头
        $header = [
            'fields' => $fields,
            'names' => $names,
            'fix_specs'=>[
                'template'=>['商品尺码','颜色'],
                'field'=>'kv_names',
                'is_fixed'=>true
            ]
        ];
        
        $userInfo = $this->getUserInfo();
        $key = 'new_unique_code_export_params_'.$userInfo['uid'];
        // 参数存入redis
        $redis = redis();
        $redis -> set($key,json_encode([$params['where']]));
        $redis -> expire($key,self::CACHE_EXPIRE);
        //添加任务
        $data['serial_no'] = $this->SerialNoService->generate( SerialType::WO_PROMOTION );
        $data['name'] = '新店内码导出-'.date('YmdHis');
        $data['where'] = json_encode(['data_redis_key'=>$key], JSON_UNESCAPED_UNICODE);
        $data['status'] = 1;
        $data['admin_id'] = $params['admin_info']['admin_id'];
        $data['admin_name'] = $params['admin_info']['admin_name'];
        $data['header'] = json_encode($header, JSON_UNESCAPED_UNICODE);
        $data['type'] = 2;
        $data['service'] = 'stock_view/getUniqueCodeExportListNew';
        $data['is_limit'] = 1;
        $data['system'] = 'wms';

        logger() -> debug('新导出-异步导出任务-data',[$data]);
        try {
            return $this->TaskService->addTask($data);
        } catch (\Throwable $e) {
            logger() -> info('创建新导出任务单失败',[$e->getMessage()]);
            throw new BusinessException($e ->getMessage());
        }

    }

    private function exportAsyncAds($params)
    {
        logger() -> debug('ads异步导出任务-入参',[$params]);
        $fields = ['warehouse_name','purchase_no','sup_name','pur_name','co_model_text','settle_way_text','cost_price','tag_price','sale_price','in_ware_age','shelf_code','spu_id','sku_id','spu_no','barcode','brand_name','category_names','in_stock_no','unique_code','epc_code','is_imperfect_name','lock_type_text','in_store_time','latest_in_store_time','updated_at','arrival_shop_at','in_w_name','status_name','spu_gender','spu_season'];
        $names = ['仓库','采购单','供应商','采购商','合作模式','结算模式','成本价','吊牌价','售价','库龄(天)','货架号','SPU','SKU','货号','条形码','品牌','大类','中类','小类','批次号','店内码','EPC','残次类型','锁定类型','入库时间','最新入库时间','更新时间','计划到店日期','调入仓','状态','性别','季节'];
        //导出表头
        $header = [
            'fields' => $fields,
            'names' => $names,
            'fix_specs'=>[
                'template'=>['商品尺码','颜色'],
                'field'=>'kv_names',
                'is_fixed'=>true
            ]
        ];

        $userInfo = $this->getUserInfo();
        $key = 'ads_unique_code_export_params_'.$userInfo['uid'];
        // 参数存入redis
        $redis = redis();
        $redis -> set($key,json_encode([$params['where']]));
        $redis -> expire($key,self::CACHE_EXPIRE);
        //添加任务
        $data['serial_no'] = $this->SerialNoService->generate( SerialType::WO_PROMOTION );
        $data['name'] = 'ads店内码导出-'.date('YmdHis');
        $data['where'] = json_encode(['data_redis_key'=>$key], JSON_UNESCAPED_UNICODE);
        $data['status'] = 1;
        $data['admin_id'] = $params['admin_info']['admin_id'];
        $data['admin_name'] = $params['admin_info']['admin_name'];
        $data['header'] = json_encode($header, JSON_UNESCAPED_UNICODE);
        $data['type'] = 2;
        $data['service'] = 'stock_view/getUniqueCodeExportListNewAds';
        $data['is_limit'] = 1;
        $data['system'] = 'wms';

        logger() -> debug('新导出-异步导出任务-data',[$data]);
        try {
            return $this->TaskService->addTask($data);
        } catch (\Throwable $e) {
            logger() -> info('创建新导出任务单失败',[$e->getMessage()]);
            throw new BusinessException($e ->getMessage());
        }

    }

    /**
     * 店内码重试
     * @RequestMapping(path="/stockView/retry", methods="get,post")
     */
    public function retry(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'serial_no' => 'required|string',
        ],[
            'serial_no.required'=> 'serial_no必传',
            'serial_no.string'=> 'serial_no必须是字符串',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        try {
            $ret = $this->TaskService->updateTask($params['serial_no'],['status'=>1]);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功',$ret);
        } catch (\Throwable $e) {
            logger() -> info('店内码重试',[$e->getMessage()]);
            throw new BusinessException($e ->getMessage());
        }

    }

    /**
     * 获取所有下拉框数据
     * @RequestMapping(path="/stockView/getCategoryTree", methods="get,post")
     */
    public function getCategoryTree(RequestInterface $request)
    {
        // type: 1=超市   2=鞋服
        $type1 = $this -> CategoryService -> getCategoryTreeByParams(['type'=>1]);
        $type2 = $this -> CategoryService -> getCategoryTreeByParams(['type'=>2]);

        $result = [
            [
                'id' => -1,
                'name' => '超市',
                'value' => -1,
                'children' => $type1
            ],
            [
                'id' => -2,
                'name' => '鞋服',
                'value' => -2,
                'children' => $type2
            ]
        ];
        return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
    }

    /**
     * 导出任务允许
     * @RequestMapping(path="/stockView/makeCanExport", methods="get,post")
     */
    public function makeCanExport(RequestInterface $request)
    {
        try {
            $userInfo = $this->getUserInfo();
            logger() -> debug('导出任务缓存标记删除',[$userInfo]);
            StockViewCommon::makeCanExport(['redis_key'=> StockViewCommon::getExportKey(['admin_id' => $userInfo['uid']])]);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        } catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }

    }

}
