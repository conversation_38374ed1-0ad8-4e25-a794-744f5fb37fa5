<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\ProduceAreaServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;
use App\JsonRpc\SerialNoServiceInterface;
use App\Constants\SerialType;
use App\Library\Facades\TaskService;

/**
 * @Controller()
 */
class ProduceAreaController extends AbstractController
{
    /**
     * @Inject()
     * @var ValidatorFactoryInterface
     */
    private $validator;
    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;
    /**
     * @Inject()
     * @var ProduceAreaServiceInterface
     */
    private $ProduceAreaService;
    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject()
     * @var SerialNoServiceInterface
     */
    private $SerialNoService;

    /**
     * 列表
     * @RequestMapping(path="/produceArea/list", methods="get,post")
     */
    public function list()
    {
        // 用户
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);
        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 来源类型
        $source_type = PublicCode::produce_area_source_type;
        // 查询方式
        $sign_type = PublicCode::produce_area_sign_type;
        // 状态
        $status = PublicCode::produce_area_status;

        $endTime = date('Y-m-d H:i:s', time());
        $startTime = date('Y-m-d H:i:s', strtotime("-1 month", time()));

        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            if (!isset($search['w_ids']) || empty($search['w_ids'])){
                $search['w_ids'] = $userWIds;
            }else{
                $search['w_ids'] = explode(',', $search['w_ids']);
            }
            $export = $params['export'] ?? 0;// 0列表 1导出

            if ($export == 1) {
                //添加任务
                $data['name'] = '生产区货品导出' . '-' . date('YmdHis');
                $data['where'] = json_encode($search, JSON_UNESCAPED_UNICODE);
                $data['is_excel'] = 1;
                $data['system'] = 'wms';
                $data['serial_no'] = $this->SerialNoService->generate(SerialType::WO_PD);
                $data['status'] = 1;
                $data['admin_id'] = $userInfo['uid'];
                $data['admin_name'] = $userInfo['nickname'];
                $data['header'] = json_encode(['fields'=>array_keys($this->exportListHeader()),'names' => array_values($this->exportListHeader())], JSON_UNESCAPED_UNICODE);
                $data['type'] = 2;
                $data['service'] = 'produce_area/exportList';
                $data['is_limit'] = 0;

                $task_id = TaskService::addTask($data);
                return $this->returnApi(ResponseCode::SUCCESS, '明细异步下载已提交', ['task_id' => $task_id]);
            } else {
                $list = $this->ProduceAreaService->list($export, (int)$page, (int)$pageLimit, $search);
                if ($list['data']) {
                    foreach ($list['data'] as &$item) {
                        $item['w_name'] = $warehouse[$item['w_id']];
                        $item['source_type'] = $source_type[$item['source_type']] ?? '';
                        $item['content'] = $item['sign_type'] == 1 ? $item['unique_code'] : $item['barcode'];
                        $item['status'] = $status[$item['status']];
                    }
                }
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
            }
        }

        return $this->show('produce_area/list', [
            'warehouse_list' => $warehouse,
            'source_type' => $source_type,
            'sign_type' => $sign_type,
            'status' => $status,
            'start_time' => $startTime,
            'end_time' => $endTime
        ]);
    }

    /**
     * 汇总列表
     * @RequestMapping(path="/produceArea/summaryList", methods="get,post")
     */
    public function summaryList(){
        $params = $this->request->all();
        $page = $params['page'] ?? 1;
        $pageLimit = $params['limit'] ?? $this->pageLimit();
        $search = $params['search'] ?? [];
        if (!isset($search['status']) || empty($search['status']) ){
            $search['status'] = 0;
        }
        $export = $params['export'] ?? 0;// 0列表 1导出
        if (!isset($search['status']) || $search['status'] === "" || intval($search['status']) !== 0){
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', [], ['count' => 0, 'limit' => $pageLimit]);
        }
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);
        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 来源类型
        $source_type = PublicCode::produce_area_source_type;
        // 状态
        $status = PublicCode::produce_area_status;

        if (!isset($search['w_ids']) || empty($search['w_ids'])){
            $search['w_ids'] = $userWIds;
        }else{
            $search['w_ids'] = explode(',', $search['w_ids']);
        }


        if ($export == 1) {
            //添加任务
            $data['name'] = '生产区货品汇总导出' . '-' . date('YmdHis');
            $data['where'] = json_encode($search, JSON_UNESCAPED_UNICODE);
            $data['is_excel'] = 1;
            $data['system'] = 'wms';
            $data['serial_no'] = $this->SerialNoService->generate(SerialType::WO_PD_SM);
            $data['status'] = 1;
            $data['admin_id'] = $userInfo['uid'];
            $data['admin_name'] = $userInfo['nickname'];
            $data['header'] = json_encode(['fields'=>array_keys($this->exportHeader()),'names' => array_values($this->exportHeader())], JSON_UNESCAPED_UNICODE);
            $data['type'] = 2;
            $data['service'] = 'produce_area/exportSummaryList';
            $data['is_limit'] = 0;

            $task_id = TaskService::addTask($data);
            return $this->returnApi(ResponseCode::SUCCESS, '明细异步下载已提交', ['task_id' => $task_id]);

        } else {
            $list = $this->ProduceAreaService->summaryList($export, (int)$page, (int)$pageLimit, $search);
            if ($list['data']) {
                foreach ($list['data'] as &$item) {
                    $item['w_name'] = $warehouse[$item['w_id']];
                    $item['source_type'] = $source_type[$item['source_type']] ?? '';
                }
            }
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
    }

    private function exportListHeader()
    {
        return [
            'id' => 'ID',
            'w_name' => '仓库',
            'source_type' => '来源类型',
            'source_order_no' => '来源单号',
            'content' => '店内码/条形码',
            'produce_num' => '已生产数量',
            'reback_num' => '已回库数量',
            'surplus_num' => '剩余数量',
            'status' => '状态',
            'created_at' => '创建时间'
        ];
    }

    private function exportHeader()
    {
        return [
            'w_name' => '仓库',
            'source_type' => '生产类型',
            'source_order_no' => '来源单号',
            'sum_num' => '数量',
            'sum_produce_num' => '已生产数量',
            'sum_pack_num' => '已装箱数量',
            'sum_reback_num' => '已回库数量',
            'sum_surplus_num' => '剩余数量',
            'finish_num' => '已完成数量'
        ];
    }

}