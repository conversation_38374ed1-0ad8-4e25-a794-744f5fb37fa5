<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <link href="/static/layui/css/layui.css" rel="stylesheet"/>
    <link href="/static/css/pearCommon.css" rel="stylesheet"/>
    <link href="/static/css/myui.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/static/layui/formSelects-v4.css"/>
    <style>
        .is_back .layui-form-checkbox[lay-skin="primary"] span {
            color: red;
        }
    </style>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form layui-form-pane" action="" lay-filter='tag_form'>
            <div class="layui-form-item">
                <div class="layui-col-xs3">
                    <label class="layui-form-label">仓库</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <select name="w_ids" lay-search class="layui-select"  xm-select="warehouse" xm-select-search xm-select-skin="default" xm-select-show-count="1">
                            <option value="">请选择</option>
                            @if ($warehouse_list)
                                @foreach ($warehouse_list as $key => $value)
                                    <option value={{ $key }}>{{ $value }}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
                <div class="layui-col-xs3">
                    <label class="layui-form-label">生产类型</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <select name="source_type" lay-search>
                            <option value="">请选择</option>
                            @if ($source_type)
                                @foreach ($source_type as $key => $value)
                                    <option value={{ $key }}>{{ $value }}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
                <div class="layui-col-xs3">
                    <label class="layui-form-label">来源单号</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <input type="text" name="source_order_no" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-xs3">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <select name="status" id="status" lay-search>
                            <option value="">请选择</option>
                            @if ($status)
                                @foreach ($status as $key => $value)
                                    <option @if($key == 0) selected @endif value={{ $key }}>{{ $value }}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-col-xs6">
                    <label class="layui-form-label">货品类型</label>
                    <div class="layui-input-inline" style="width: 30%">
                        <select name="sign_type" lay-search>
                            <option value="">请选择</option>
                            @if ($sign_type)
                                @foreach ($sign_type as $key => $value)
                                    <option value={{ $key }}>{{ $value }}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                    <textarea name="content" placeholder="一行一个" rows="2" style="width: 48%;"></textarea>
                </div>
                <div class="layui-col-xs6">
                    <label class="layui-form-label">装箱</label>
                    <div class="layui-input-inline " style="width: 13%;">
                        <input type="checkbox" name="is_produce" lay-skin="primary" title="未生产" value="0">
                    </div>
                    <div class="layui-input-inline " style="width: 13%;">
                        <input type="checkbox" name="is_packing" lay-skin="primary" title="未装箱" value="0">
                    </div>
                    <div class="layui-input-inline is_back" style="width: 13%;">
                        <input type="checkbox" name="is_reback" lay-skin="primary" title="已回库" value="0">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-col-xs6">
                    <label class="layui-form-label">创建时间</label>
                    <div class="layui-input-inline " style="width: 30%;">
                        <input type="text" name="start_time" id="date1" placeholder="yyyy-MM-dd" autocomplete="off" class="layui-input" value="{{$start_time}}">
                    </div>
                    <div class="layui-input-inline " style="width: 30%;">
                        <input type="text" name="end_time" id="date2" placeholder="yyyy-MM-dd" autocomplete="off" class="layui-input" value="{{$end_time}}">
                    </div>
                </div>

                <div class="layui-col-xs6"  style="justify-content: flex-end;">
                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="query" style="margin: 5px">
                        <i class="layui-icon layui-icon-search"></i>
                        查询
                    </button>
                    <button type="reset" class="pear-btn pear-btn-md" style="margin: 5px">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="export" style="margin: 5px">
                        <i class="layui-icon layui-icon-normal"></i>
                        导出
                    </button>
                </div>
            </div>

        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-tab" lay-filter="tab_info">
        <ul class="layui-tab-title" >
            <li class="layui-this" lay-id="1">明细</li>
            <li lay-id="2">汇总(<span id="hz_nums">0</span>)</li>
            <input id="lay_id" type="hidden" value="1">
        </ul>
        <div class="layui-tab-content">
            <div class="layui-card-body layui-tab-item layui-show">
                <table id="dataTable" lay-filter="dataTable"></table>
            </div>
            <div class="layui-card-body layui-tab-item">
                <table id="dataTable_hz" lay-filter="dataTable_hz"></table>
            </div>
        </div>
    </div>
</div>

<script src="/static/layui/layui.js"></script>
<script>
    layui.use(['jquery', 'table', 'element', 'form', 'layer', 'laydate','formSelects', 'iframeTools'], function () {
        var formSelects = layui.formSelects;
        var table = layui.table;
        var $ = layui.jquery;
        var form = layui.form;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var element = layui.element;// Tab的切换功能，切换事件监听等，需要依赖element模块
        var iframeTools = layui.iframeTools;

        var dataTable;
        var dataTable_hz;

        //表格初始化
        mingxi();//默认明细页签
        hz_nums({'status': 0});
        //获取汇总数量
        function hz_nums(search){
            $.ajax({
                url: '/produceArea/summaryList',
                type: 'post',
                data: JSON.stringify({
                    search : search
                }),
                dataType: "json",
                contentType: 'application/json',
                processData: false,
                success: function (data) {
                    $('#hz_nums').html(data.count)
                }
            });
        }
        //明细
        function mingxi() {
            data = layui.form.val(`tag_form`);
            dataTable = table.render({
                elem: '#dataTable'
                , url: "/produceArea/list"
                , method: 'post'
                , where: {search: data}
                , page: true //开启分页
                , skin: 'line'
                , cols: [[ //表头
                    {field: 'id', title: 'ID', sort: true, width: 80}
                    , {field: 'w_name', title: '仓库'}
                    , {field: 'source_type', title: '生产类型'}
                    , {field: 'source_order_no', title: '来源单号'}
                    , {field: 'content', title: '店内码/条形码'}
                    , {field: 'num', title: '数量'}
                    , {field: 'produce_num', title: '已生产数量'}
                    , {field: 'pack_num', title: '已装箱数量'}
                    , {field: 'reback_num', title: '已回库数量'}
                    , {field: 'surplus_num', title: '剩余数量'}
                    , {field: 'status', title: '状态'}
                    , {field: 'created_at', title: '创建时间'}
                ]]
            });
        }
        //汇总
        function huizong() {
            data = layui.form.val(`tag_form`);
            dataTable_hz = table.render({
                elem: '#dataTable_hz'
                , url: "/produceArea/summaryList"
                , method: 'post'
                , where: {search: data}
                , page: true //开启分页
                , skin: 'line'
                , cols: [[ //表头
                    {field: 'w_name', title: '仓库'}
                    , {field: 'source_type', title: '生产类型'}
                    , {field: 'source_order_no', title: '来源单号'}
                    , {field: 'sum_num', title: '数量'}
                    , {field: 'sum_produce_num', title: '已生产数量'}
                    , {field: 'sum_pack_num', title: '已装箱数量'}
                    , {field: 'sum_reback_num', title: '已回库数量', templet:function (d) {
                            let sum_reback_num = d.sum_reback_num
                            if (sum_reback_num > 0){
                                return '<p><a style="color:blue;border-bottom:1px solid blue" lay-event="reback">' + sum_reback_num + '</a></p>'
                            }else{
                                return '<p>' + sum_reback_num + '</p>'
                            }
                        }}
                    , {field: 'sum_surplus_num', title: '剩余数量',
                        templet:function (d) {
                            let sum_surplus_num = d.sum_surplus_num
                            if (sum_surplus_num > 0) {
                                return '<p><a style="color:blue;border-bottom:1px solid blue" lay-event="surplus">' + sum_surplus_num + '</a></p>'
                            }else{
                                return '<p>' + sum_surplus_num + '</p>'
                            }
                        }}
                    , {field: 'finish_num', title: '已完成数量'}
                ]]
            });
        }
        //监听页签切换
        element.on('tab(tab_info)', function(){
            $('#lay_id').val(this.getAttribute('lay-id'));
            if (this.getAttribute('lay-id') == 1){//明细
                mingxi();
            }else{//汇总
                huizong();
                data = layui.form.val(`tag_form`);
                //检测是否选择状态值
                if (data.status !== "0"){
                    layer.msg('只允许对未完成的明细进行汇总!', {icon: 5,anim: 6});
                    return false;
                }
            }


        });

        // 监听table编辑事件
        table.on("tool(dataTable_hz)", function (obj) {
            var data = obj.data; //获得当前行数据
            console.log(data);
            var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
            console.log(layEvent);
            // var tr = obj.tr; //获得当前行 tr 的 DOM 对象（如果有的话）
            if(layEvent === 'reback') { //编辑
                iframeTools.addTab('店内码查询', "/stockView/uniqueCodeList?w_id="+ data.w_id + '&source_order_no='+ data.source_order_no + '&area_type=reback' + '&type=link')
            }else if (layEvent == 'surplus')
            {
                iframeTools.addTab('店内码查询', "/stockView/uniqueCodeList?w_id="+ data.w_id + '&source_order_no='+ data.source_order_no + '&area_type=surplus' + '&type=link')
            }
        });


        //查询按钮
        var median;
        form.on('submit(query)', function (data) {
            console.log(data);
            data.field.content = $.trim(data.field.content).split(/[(\r\n)\r\n]+/);
            if (median != null) {
                median.where = {};
            }
            hz_nums(data.field);
            if ($('#lay_id').val() == 1){
                table.reload('dataTable', {//重载
                    where: {search: data.field},
                    page: {curr: 1},
                    done: function () {
                        median = this;
                    }, page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
            }

            if ($('#lay_id').val() == 2) {
                //检测是否选择状态值
                if (data.field.status != 0){
                    layer.msg('只允许对未完成的明细进行汇总!', {icon: 5,anim: 6});
                    return false;
                }

                table.reload('dataTable_hz', {//重载
                    where: {search: data.field},
                    page: {curr: 1},
                    done: function () {
                        median = this;
                    }, page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
            }
            return false;
        });
        //导出按钮
        form.on('submit(export)', function (data) {
            data.field.content = $.trim(data.field.content).split(/[(\r\n)\r\n]+/);

            if ($('#lay_id').val() == 1) {
                $.ajax({
                    url: '/produceArea/list',
                    type: 'post',
                    data: JSON.stringify({search: data.field, export: 1}),
                    dataType: "json",
                    contentType: 'application/json',
                    processData: false,
                    success: function (data) {
                        if (data.code == 200) {
                            layer.msg(data.msg, {icon: 1, time: 1000})
                        } else {
                            layer.msg(data.msg, {icon: 2, time: 5000})
                        }
                    },
                    error: function (e) {
                        layer.close(load);
                        layer.msg('提交失败！', {icon: 2})
                    },
                });
            }

            if ($('#lay_id').val() == 2) {
                $.ajax({
                    url: '/produceArea/summaryList',
                    type: 'post',
                    data: JSON.stringify({search: data.field, export: 1}),
                    dataType: "json",
                    contentType: 'application/json',
                    processData: false,
                    success: function (data) {
                        if (data.code == 200) {
                            layer.msg(data.msg, {icon: 1, time: 1000})
                        } else {
                            layer.msg(data.msg, {icon: 2, time: 5000})
                        }
                    },
                    error: function (e) {
                        layer.close(load);
                        layer.msg('提交失败！', {icon: 2})
                    },
                });
            }

            return false;
        });
        refresh = function (param) {
            table.reload('dataTable');
            table.reload('dataTable_hz');
        };
        //日期
        laydate.render({
            elem: '#date1'
            , type: 'datetime'
            , trigger: 'click'
        });
        laydate.render({
            elem: '#date2'
            , type: 'datetime'
            , trigger: 'click'
            , format: 'yyyy-MM-dd 23:59:59'
        });
    })
</script>
</body>
</html>

