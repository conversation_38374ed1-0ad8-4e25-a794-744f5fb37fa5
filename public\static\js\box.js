init()
function init() {
    document.write("<script src=\"http://127.0.0.1:8000/CLodopfuncs.js\"></script>\n");
    document.write('<div id="boxCodePrint" style="display: none;">\n' +
        '    <style>\n' +
        '        .box-print-table{border-collapse: collapse;text-align: center;width: 77mm;height: 98mm;padding: 0;margin: 0;background-color: #ffffff;}.box-print-table tr td{padding: 0;margin: 0;}#boxCodePrint{padding: 0;margin: 0;}.box-print-table .box-print-main{height: 8mm;}.box-w-id{font-size: 30px;font-weight: 700;}.box-main-info{font-size: 18px;width: 55mm;font-weight: 700;}.box-main-title{font-size: 14px;width: 24mm;}.box-num{font-size: 30px;}.box-allot-no-suffix{font-size: 30px;}\n' +
        '    </style>\n' +
        '    <table border=1 cellSpacing=0 cellPadding=1 class="box-print-table">\n' +
        '        <tr class="box-print-main">\n' +
        '            <td colspan="2" class="box-w-id"></td>\n' +
        '        </tr>\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="20mm" class="box-main-title">收货方</td>\n' +
        '            <td width="58mm" class="box-main-info box-w-name"></td>\n' +
        '        </tr>\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="20mm" class="box-main-title">调拨单</td>\n' +
        '            <td width="58mm" class="box-main-info"><span class="box-allot-no-prefix"></span><span\n' +
        '                        class="box-allot-no-suffix"></span></td>\n' +
        '        </tr>\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="20mm" class="box-main-title">件数/品牌</td>\n' +
        '            <td width="58mm" class="box-main-info "><span class="box-num"></span>/<span class="box-brand"></span></td>\n' +
        '        </tr>\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="20mm" class="box-main-title">期望到店时间</td>\n' +
        '            <td width="58mm" class="box-main-info box-ask-date"></td>\n' +
        '        </tr>\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="20mm" class="box-main-title">装箱人</td>\n' +
        '            <td width="58mm" class="box-main-info box-admin-name"></td>\n' +
        '        </tr>\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="20mm" class="box-main-title">装箱时间</td>\n' +
        '            <td width="58mm" class="box-main-info box-time"></td>\n' +
        '        </tr>\n' +
        '        <tr class="box-print-main">\n' +
        '            <td width="20mm" class="box-main-title">重量kg/标题</td>\n' +
        '            <td width="58mm" class="box-main-info box-weight"></td>\n' +
        '        </tr>\n' +
        '        <tr style="height: 17mm">\n' +
        '            <td colspan="2">\n' +
        '            </td>\n' +
        '        </tr>\n' +
        '    </table>\n' +
        '</div>\n');
}

/**
 * 调拨箱号打印
 * @param data
 * [{
 *  source_no,
 *  w_id,
 *  in_w_name,
 *  brand_name_tips,
 *  pk_num,
 *  ask_date,
 *  pk_admin_name,
 *  pk_time,
 *  weight,
 *  box_no
 * }]
 * @param log true 打印写入日志 false 不写入
 */
function allotBoxPrint(data,log = false) {
    console.log('打印箱码-data',JSON.stringify(data))
    $.each(data, function (k, v) {
        if (true === log) setLog(v.allot_id, v.box_no);//记录调拨箱号打印日志
        var allot_no = v.source_no.split("-");
        if (0 < v.shoes_num) {
            $(".box-w-id").html(v.in_w_id + "/鞋类");
        } else {
            $(".box-w-id").html(v.in_w_id);
        }
        if (v.in_w_name.length > 10) {
            $(".box-w-name").html(v.in_w_name.substring(0, 10) + "...");
        } else {
            $(".box-w-name").html(v.in_w_name);
        }
        $(".box-allot-no-prefix").html(allot_no[0] + '-' + allot_no[1] + '-');
        $(".box-allot-no-suffix").html(allot_no[2]);
        $(".box-num").html(v.pk_num);
        if (v.brand_name_tips.length > 7) {
            $(".box-brand").html(v.brand_name_tips.substring(0, 7) + "...");
        } else {
            $(".box-brand").html(v.brand_name_tips);
        }
        $(".box-ask-date").html(v.ask_date);
        $(".box-admin-name").html(v.pk_admin_name);
        $(".box-time").html(v.pk_time);
        var title = v.weight;
        if(v.title) title += "/"+v.title
        if (title.length > 12) {
            $(".box-weight").html(title.substring(0, 12) + "...");
        } else {
            $(".box-weight").html(title);
        }
        LODOP.SET_LICENSES("","94D9E78FC9B721D1EBF7165B69114B39A35","","");
        LODOP.PRINT_INIT("");
        LODOP.SET_PRINT_PAGESIZE(1, '80mm', '100mm', "");
        LODOP.ADD_PRINT_TABLE("1mm", 0, 0, "100%", document.getElementById("boxCodePrint").innerHTML);
        LODOP.ADD_PRINT_BARCODE("81mm", "9mm", "60mm", "16mm", "128Auto", v.box_no);
        if (1 == v.allot_type) {
            LODOP.SET_PRINTER_INDEXA("箱唛打印");
        }
        //LODOP.PREVIEW();
        LODOP.PRINT();
    });

    /**
     * 设置打印日志
     * @param allot_id
     * @param box_no
     */
    function setLog(allot_id, box_no) {
        $.post("/common/setLog", {
            id: allot_id,
            router_name: '箱码打印',
            model_name: 'allot',
            remark: '箱号：' + box_no
        }, function (res) {
        });
    }
}