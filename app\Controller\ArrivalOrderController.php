<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Controller;

use App\Constants\ResponseCode;
use App\JsonRpc\SkuBarcodeServiceInterface;
use App\JsonRpc\InStoreServiceInterface;
use App\Library\Facades\AdminService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use App\Constants\PublicCode;
use App\Constants\SerialType;
use App\Library\Facades\TaskService;
use App\Exception\BusinessException;

/**
 * @Controller()
 */
class ArrivalOrderController extends AbstractController
{
    /**
     * @Inject()
     * @var \App\JsonRpc\WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var \App\JsonRpc\SupplierServiceInterface
     */
    private $supplierService;
    
    /**
     * @Inject()
     * @var \App\JsonRpc\ArrivalOrderServiceInterface
     */
    private $arrivalOrderService;

    /**
     * @Inject()
     * @var \App\JsonRpc\LogisticsInfoServiceInterface
     */
    private $logisticsInfoService;

    /**
     * @Inject()
     * @var \App\JsonRpc\ContractServiceInterface
     */
    private $contractService;

    /**
     * @Inject()
     * @var \App\JsonRpc\SkuServiceInterface
     */
    private $SkuService;

    /**
     * @Inject()
     * @var \App\JsonRpc\SpuServiceInterface
     */
    private $SpuService;

    /**
     * @Inject()
     * @var \App\JsonRpc\AttributeServiceInterface
     */
    private $AttributeService;

    /**
     * @Inject()
     * @var \App\JsonRpc\BrandServiceInterface
     */
    private $BrandService;

    /**
     * @Inject()
     * @var \App\JsonRpc\CategoryServiceInterface
     */
    private $CategoryService;

    /**
     * @Inject()
     * @var \App\JsonRpc\PurchaseOrderServiceInterface
     */
    private $PurchaseOrderService;

    /**
     * @Inject()
     * @var \App\JsonRpc\SerialNoServiceInterface
     */
    private $SerialNoService;

    /**
     * @Inject()
     * @var SkuBarcodeServiceInterface
     */
    private $SkuBarcodeService;

    /**
     * @Inject()
     * @var InStoreServiceInterface
     */
    private $InStoreService;

    private $status = PublicCode::arrival_status;
    private $send_type = [
        '1' => '物流',
        '2' => '自提',
        '3' => '发货方自送'
    ];

    /**
     * 到货任务
     * @RequestMapping(path="/arrivalOrder/list", methods="get,post")
     * @return mixed
     */
    public function list(RequestInterface $request)
    {
        $w_id = $request->input('w_id','');
        $start_time = $request->input('start_time');
        $end_time = $request->input('end_time');

        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        //获取仓库信息
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids'=>$wIds], ['id', 'name']);

        //获取调入仓
        $allot_warehouse_list = [];
        $allot_where = [
            'is_transfer' => 1,
            'allot_wids' => $wIds
        ];
        $arrival_allot_list = $this->arrivalOrderService->getOrders($allot_where);
        if (!empty($arrival_allot_list)){
            $allot_ids = array_column($arrival_allot_list, 'allot_w_id');
            $allot_warehouse_list = $this->WarehouseService->getWarehouses(['ids'=>$allot_ids], ['id', 'name']);
        }

        //获取供应商信息
        $supplier_list = $this->supplierService->getSuppliers([], ['id', 'name']);

        //品牌信息
        $brand_list = $this->BrandService->getBrands();

        $time = '';
        if(!empty($start_time) && !empty($end_time)){
            $time = $start_time . ' - ' . $end_time;
        }

        $data = [
            'w_ids' => explode(",", $w_id),
            'time' => $time,
            'warehouse_list' => $warehouse_list,
            'supplier_list' => $supplier_list,
            'brand_list' => $brand_list,
            'status_list' => $this->status,
            'allot_warehouse_list' => $allot_warehouse_list
        ];

        return $this->show('arrival/list', $data);
    }

    /**
     * @RequestMapping(path="/arrivalOrder/ajax_list", methods="get,post")
     * @return mixed
     */
    public function ajax_list(RequestInterface $request)
    {
        $brand_ids = $request->input('brand_ids');
        $warehouse_ids = $request->input('warehouse_ids');
        $supplier_ids = $request->input('supplier_ids');
        $status = $request->input('status');
        $arrival_code = $request->input('arrival_code');
        $purchase_code = $request->input('purchase_code');
        $barcode = $request->input('barcode');
        $delivery_sign = $request->input('delivery_sign');
        $phone = $request->input('phone');
        $box_no = $request->input('box_no');
        $third_party_no = $request->input('third_party_no');
        $arrive_time = $request->input('arrive_time');
        $arrival_shop_at = $request->input('arrival_shop_at');
        $is_transfer = $request->input('is_transfer');
        $allot_warehouse_ids = $request->input("allot_warehouse");
        $page = $request->input('page');
        $limit = $request->input('limit');
        $produce_diff = $request->input('produce_diff');
        $where = [];
        //获取采购单的id
        $purchase_detail_where = [];
        if (!empty($brand_ids)) {
            $purchase_detail_where['brand_ids'] = $brand_ids;
        }
        if (!empty($barcode)) {
            //获取barcode对应的sku
            $sku_list = $this->SkuService->getSkuBarcodeMap(['barcode' => $barcode]);
            if (!empty($sku_list)){
                $sku_id = $sku_list[0]['sku_id'];
                $purchase_detail_where['sku_id'] = $sku_id;
            }else{
                return $this->returnApi('200', '操作成功', [], ['count' => 0,'limit' => $limit]);
            }
        }
        if (!empty($purchase_detail_where)){
            $purchase_detail_where['get_purchase_order_id'] = true;
            $purchase_detail_list = $this->PurchaseOrderService->getOrderDetail($purchase_detail_where);
            if (!empty($purchase_detail_list)){
                $purchase_where = [
                    'ids' => array_column($purchase_detail_list,'purchase_order_id')
                ];
                $purchase_list = $this->PurchaseOrderService->getOrders($purchase_where, ['serial_no']);
                logger()->info('purchase_list', $purchase_list);
                if (!empty($purchase_list)){
                    $where['purchase_nos'] = array_column($purchase_list, 'serial_no');
                }else{
                    return $this->returnApi('200', '操作成功', [], ['count' => 0,'limit' => $limit]);
                }
            }else{
                return $this->returnApi('200', '操作成功', [], ['count' => 0,'limit' => $limit]);
            }
        }
        if (!empty($supplier_ids)) {
            $where['supplier_ids'] = $supplier_ids;
        }
        if (is_numeric($status)) {
            $where['status'] = $status;
        }
        if (!empty($arrival_code)) {
            $where['arrival_no'] = $arrival_code;
        }
        if (!empty($purchase_code)) {
            $where['purchase_no'] = $purchase_code;
        }

        if (!empty($delivery_sign)) {
            $where['delivery_sign'] = $delivery_sign;
        }
        if (!empty($phone)) {
            $where['phone'] = $phone;
        }
        if (!empty($box_no)){
            $where['box_no'] = $box_no;
        }
        if (!empty($third_party_no)){
            $where['third_party_no'] = $third_party_no;
        }

        if(!empty($arrive_time)){
            $time = explode(" - ", $arrive_time);
            $where['start_arrive_time'] = $time[0];
            $where['end_arrive_time'] = $time[1];
        }

        if (!empty($arrival_shop_at)) {
            $shopAt = explode(" - ", $arrival_shop_at);
            $where['start_arrival_shop_at'] = date('Y-m', strtotime($shopAt[0]));
            $where['end_arrival_shop_at'] = date('Y-m', strtotime($shopAt[1]));
        }

        if ($is_transfer != ""){
            $where['is_transfer'] = $is_transfer;
            $where['allot_warehouse_ids'] = $allot_warehouse_ids;
        }

        if ($produce_diff == 'true'){
            if (empty($where)){
//                throw new BusinessException('请设置期望到店时间');
                return $this->returnApi('500', '请设置期望到店时间', [], ['count' => 0,'limit' => $limit]);
            }
            $where['produce_diff'] = true;
        }
        if (!empty($warehouse_ids)) {
            $where['warehouse_ids'] = $warehouse_ids;
        }else {
            $where['warehouse_ids'] = AdminService::organizeWareHouseData($this->getUserId());
        }
        $where['get_produce'] = true;
        $where['get_tally'] = true;
        $arrival_info = $this->arrivalOrderService->arrivalList($where, intval($page), intval($limit));

        //获取到店时间
        $purchase_order_ids = array_unique(array_column($arrival_info['data'], 'purchase_order_id'));
        $purchase_arrival_shop_at_map = [];
        if (!empty($purchase_order_ids)){
            $purchase_arrival_shop_at_map = $this->PurchaseOrderService->getOrderArrivalShopAt(['order_ids' => $purchase_order_ids]);
        }

        foreach ($arrival_info['data'] as &$info) {
            $info['status'] = $this->status[$info['status']];
            $info['arrive_time'] = date('Y-m-d', strtotime($info['arrive_time']));
            $info['arrival_shop_at'] = isset($purchase_arrival_shop_at_map[$info['purchase_order_id']]) ? implode("、", $purchase_arrival_shop_at_map[$info['purchase_order_id']]) : "";
        }

        return $this->returnApi('200', '操作成功', $arrival_info['data'], ['count' => $arrival_info['total'],'limit' => $limit]);

    }

    /**
     * @RequestMapping(path="/arrivalOrder/down_arrival_list", methods="get,post")
     */
    public function down_arrival_list(RequestInterface $request)
    {
        $params = $request->all();
        $header = [
            'arrival_code' => '任务单号',
            'purchase_code' => '采购单号',
            'supplier_name' => '供应商',
            'warehouse_name' => '仓库',
            'brand_names' => '品牌',
            'status' => '状态',
            'arrive_num' => '预约数',
            'arrive_time' => '预约时间',
            'send_box' => '送货箱数',
            'send_num' => '送货件数',
            'express_code' => '车牌号\物流号',
            'linkman' => '送货负责人',
            'mobile' => '送货人电话',
            'send_type' => '送货方式',
            'recev_box' => '收货箱数',
            'recev_num' => '收货件数',
            'receiver' => '收货人',
            'recev_time' => '收货时间',
            'freight_price' => '支付运费',
            'allot_warehouse_name' => '预约到货门店',
            'in_num' => '入库数量',
            'print_num' => '生产数量',
            'tally_num' => '转架数量',
            'arrival_shop_at' => '到店时间'
        ];
        $data = [];
        //获取仓库信息
        $warehouse_list = $this->WarehouseService->getWarehouses([], ['id', 'name']);
        $warehouse_map = array_column($warehouse_list, 'name', 'id');

        $where = [];
        if (!empty($params['brand_ids'])) {
            $where['brand_ids'] = explode(',', $params['brand_ids']);
        }
        if (!empty($params['supplier_ids'])) {
            $where['supplier_ids'] = explode(',', $params['supplier_ids']);
        }
        if ($params['status'] != '') {
            $where['status'] = $params['status'];
        }
        if (!empty($params['arrival_code'])) {
            $where['arrival_no'] = $params['arrival_code'];
        }
        if (!empty($params['purchase_code'])) {
            $where['purchase_no'] = $params['purchase_code'];
        }
        if (!empty($params['barcode'])) {
            $where['barcode'] = $params['barcode'];
        }
        if (!empty($params['delivery_sign'])) {
            $where['delivery_sign'] = $params['delivery_sign'];
        }
        if (!empty($params['phone'])) {
            $where['phone'] = $params['phone'];
        }
        if (!empty($params['box_no'])){
            $where['box_no'] = $params['box_no'];
        }
        if (!empty($params['third_party_no'])){
            $where['third_party_no'] = $params['third_party_no'];
        }

        if(!empty($params['arrive_time'])){
            $time = explode(" - ", $params['arrive_time']);
            $where['start_arrive_time'] = $time[0];
            $where['end_arrive_time'] = $time[1];
        }
        if ($params['is_transfer'] != ""){
            $where['is_transfer'] = $params['is_transfer'];
            if (isset($params['allot_warehouse']) && !empty($params['allot_warehouse'])){
                $where['allot_warehouse_ids'] = explode(',', $params['allot_warehouse']);
            }
        }

        if (!empty($params['arrival_shop_at'])) {
            $shopAt = explode(" - ", $params['arrival_shop_at']);
            $where['start_arrival_shop_at'] = date('Y-m', strtotime($shopAt[0]));
            $where['end_arrival_shop_at'] = date('Y-m', strtotime($shopAt[1]));
        }

        if ($params['produce_diff'] == 'true'){
            if (empty($where)){
                $info = exportCsv($header, $data, '预约到货单');
                return $info;
            }
            $where['produce_diff'] = true;
        }
        if (!empty($params['warehouse_ids'])) {
            $where['warehouse_ids'] = $params['warehouse_ids'];
        }else {
            $where['warehouse_ids'] = AdminService::organizeWareHouseData($this->getUserId());
        }


        $page_id = 1;
        $current_page = 100;

        $where['get_produce'] = true;
        $where['get_tally'] = true;
        while (true) {
            logger()->info('where==================', [$where]);
            $arrival_list = $this->arrivalOrderService->arrivalList($where, $page_id, $current_page);
            if (empty($arrival_list['data'])) {
                break;
            }
            $page_id++;
            $arrival_codes = array_column($arrival_list['data'], 'arrival_code');
            $logistics_info = [];
            if (!empty($arrival_codes)) {
                //获取登记信息
                $logistics_where = [
                    'rela_nos' => $arrival_codes,
                    'rela_type' => 3
                ];
                $logistics_list = $this->logisticsInfoService->getLogisticsList($logistics_where, count($arrival_codes), 1 );

                if (!empty($logistics_list) && $logistics_list['total_number'] > 0) {
                    $logistics_info = array_combine(array_column($logistics_list['list'], 'rela_no'), array_values($logistics_list['list']));
                }
            }

            //获取到店时间
            $purchase_order_ids = array_unique(array_column($arrival_list['data'], 'purchase_order_id'));
            $purchase_arrival_shop_at_map = [];
            if (!empty($purchase_order_ids)){
                $purchase_arrival_shop_at_map = $this->PurchaseOrderService->getOrderArrivalShopAt(['order_ids' => $purchase_order_ids]);
            }

            foreach ($arrival_list['data'] as $info) {
                $a_code = $info['arrival_code'];
                $data[] = [
                    'arrival_code' => $a_code,
                    'purchase_code' => $info['purchase_code'],
                    'supplier_name' => $info['supplier_name'],
                    'warehouse_name' => $info['warehouse_name'],
                    'brand_names' => str_replace(","," ",$info['brand_names']),
                    'status' => isset($this->status[$info['status']]) ? $this->status[$info['status']] : '',
                    'arrive_num' => $info['arrive_num'],
                    'arrive_time' => $info['arrive_time'],
                    'send_box' => isset($logistics_info[$a_code]) ? $logistics_info[$a_code]['send_box'] : '',
                    'send_num' => isset($logistics_info[$a_code]) ? $logistics_info[$a_code]['send_num'] : '',
                    'express_code' => isset($logistics_info[$a_code]) ? $logistics_info[$a_code]['express_code'] : '',
                    'linkman' => isset($logistics_info[$a_code]) ? $logistics_info[$a_code]['linkman'] : '',
                    'mobile' => isset($logistics_info[$a_code]) ? $logistics_info[$a_code]['mobile'] : '',
                    'send_type' => isset($logistics_info[$a_code]) ? $this->send_type[$logistics_info[$a_code]['send_type']] : '',
                    'recev_box' => isset($logistics_info[$a_code]) ? $logistics_info[$a_code]['recev_box'] : '',
                    'recev_num' => isset($logistics_info[$a_code]) ? $logistics_info[$a_code]['recev_num'] : '',
                    'receiver' => isset($logistics_info[$a_code]) ? $logistics_info[$a_code]['receiver'] : '',
                    'recev_time' => isset($logistics_info[$a_code]) ? $logistics_info[$a_code]['recev_time'] : '',
                    'freight_price' => isset($logistics_info[$a_code]) ? ($logistics_info[$a_code]['freight_price']/100) : '',
                    'allot_warehouse_name' => $warehouse_map[$info['allot_w_id']] ?? '',
                    'in_num' => $info['in_num'] ?? 0,
                    'print_num' => $info['print_num'] ?? 0,
                    'tally_num' => $info['tally_num'] ?? 0,
                    'arrival_shop_at' => isset($purchase_arrival_shop_at_map[$info['purchase_order_id']]) ? "'".implode("、", $purchase_arrival_shop_at_map[$info['purchase_order_id']]) : ""
                ];
            }
        }

        $info = exportCsv($header, $data, '预约到货单');
        return $info;
    }

    /**
     * @RequestMapping(path="/arrivalOrder/down_arrival_detail", methods="get,post")
     */
    public function down_arrival_detail(RequestInterface $request)
    {
        $params = $request->all();
        $where = [];
        $header = [
            'arrival_code' => '预约单号',
            'in_stock_no' => '入库批次',
            'shelf_code' => '货架号',
            'spu_id' => 'SPU',
            'sku_id' => 'SKU',
            'barcode' => '条形码',
            'unique_code' => '店内码',
            'spu_no' => '货号',
            'brand' => '品牌',
            'category' => '品类',
            'spec_kv' => '规格',
            'num' => '入库数量（正常）',
            'diff_num' => '入库差异数量',
            'allot_warehouse_name' => '预约到货门店',
            'print_num' => '生产数量',
            'tally_num' => '转架数量',
            'arrival_shop_at' => '到店时间'
        ];
        if (!empty($params['brand_ids'])) {
            $where['brand_ids'] = explode(',', $params['brand_ids']);
        }
        if (!empty($params['supplier_ids'])) {
            $where['supplier_ids'] = explode(',', $params['supplier_ids']);
        }
        if ($params['status'] != '') {
            $where['status'] = $params['status'];
        }
        if (!empty($params['arrival_code'])) {
            $where['arrival_no'] = $params['arrival_code'];
        }
        if (!empty($params['purchase_code'])) {
            $where['purchase_no'] = $params['purchase_code'];
        }
        if (!empty($params['barcode'])) {
            $where['barcode'] = $params['barcode'];
        }
        if (!empty($params['delivery_sign'])) {
            $where['delivery_sign'] = $params['delivery_sign'];
        }
        if (!empty($params['phone'])) {
            $where['phone'] = $params['phone'];
        }
        if (!empty($params['box_no'])){
            $where['box_no'] = $params['box_no'];
        }
        if (!empty($params['third_party_no'])){
            $where['third_party_no'] = $params['third_party_no'];
        }
        if(!empty($params['arrive_time'])){
            $time = explode(" - ", $params['arrive_time']);
            $where['start_arrive_time'] = $time[0];
            $where['end_arrive_time'] = $time[1];
        }
        if ($params['is_transfer'] != ""){
            $where['is_transfer'] = $params['is_transfer'];
            if (isset($params['allot_warehouse']) && !empty($params['allot_warehouse'])){
                $where['allot_warehouse_ids'] = explode(',', $params['allot_warehouse']);
            }
        }
        if (!empty($params['arrival_shop_at'])) {
            $shopAt = explode(" - ", $params['arrival_shop_at']);
            $where['start_arrival_shop_at'] = date('Y-m', strtotime($shopAt[0]));
            $where['end_arrival_shop_at'] = date('Y-m', strtotime($shopAt[1]));
        }
        if ($params['produce_diff'] == 'true'){
            $where['produce_diff'] = true;
        }
        if (!empty($params['warehouse_ids'])) {
            $where['warehouse_ids'] = $params['warehouse_ids'];
        }else {
            $where['warehouse_ids'] = AdminService::organizeWareHouseData($this->getUserId());
        }

        $userInfo = $this->session->get('userInfo');
        //添加任务
        $data['name'] = '到货任务明细' . '-' . date('YmdHis');
        $data['where'] = json_encode($where, JSON_UNESCAPED_UNICODE);
        $data['is_excel'] = 1;
        $data['system'] = 'wms';
        $data['serial_no'] = $this->SerialNoService->generate(SerialType::WO_ARDETAIL);
        $data['status'] = 1;
        $data['admin_id'] = $userInfo['uid'];
        $data['admin_name'] = $userInfo['nickname'];
        $data['header'] = json_encode(['fields'=>array_keys($header),'names' => array_values($header)], JSON_UNESCAPED_UNICODE);
        $data['type'] = 2;
        $data['service'] = 'arrival_order/exportDetail';
        $data['is_limit'] = 0;

        $task_id = TaskService::addTask($data);
        return $this->returnApi(ResponseCode::SUCCESS, '明细异步下载已提交', ['task_id' => $task_id]);
    }

    /**
     * @RequestMapping(path="/arrivalOrder/detail/{arrival_id}", methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function detail()
    {
        $data['title'] = "任务单明细";

        $arrival_id = (int)$this->request->route('arrival_id');// $request->input('arrival_id');
        $where = ['arrival_id' => $arrival_id];
        $arrival_info = $this->arrivalOrderService->arrivalList($where, 1, 1);

        if (empty($arrival_info['data'])) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '任务单不存在');
        }

        $arrival_info = $arrival_info['data'][0];
        $arrival_info['status_name'] = $this->status[$arrival_info['status']];
        $arrival_info['arrive_time'] = date('Y-m-d', strtotime($arrival_info['arrive_time']));

        //获取采购模式
        $arrival_info['co_model_name'] = '未知';
        $contract_no = $arrival_info['contract_no'];
        if (!empty($contract_no)) {
            $contract_info = $this->contractService->getContractByCode($contract_no);
            if (!empty($contract_info)) {
                $arrival_info['co_model_name'] = $contract_info['co_model_name'];
            }
        }
        $data['arrival_info'] = $arrival_info;

        // 操作记录
        $log_list[] = [
            'type' => '新建预约到货',
            'content' => '预约到货单号：' . $arrival_info['arrival_code'],
            'admin_name' => $arrival_info['admin_name'],
            'created_at' => $arrival_info['created_at']
        ];
        if ($arrival_info['status'] == 1) {
            $logistics = $this->logisticsInfoService->getLogisticsInfoS(['rela_no' => $arrival_info['arrival_code']], ['id', 'admin_name', 'created_at']);
            if ($logistics) {
                foreach ($logistics as $logistic) {
                    $log_list[] = [
                        'type' => '到货登记',
                        'content' => '到货登记ID：' . $logistic['id'],
                        'admin_name' => $logistic['admin_name'],
                        'created_at' => $logistic['created_at']
                    ];
                }
            }
        }
        $data['log_list'] = $log_list;

        return $this->show('arrival/detail', $data);
    }

    /**
     * @RequestMapping(path="/arrivalOrder/down_arrival_product", methods="get,post")
     */
    public function down_arrival_product(RequestInterface $request)
    {
        $arrival_id = (int)$request->input('arrival_id');

        //获取预约单的商品信息
        $detail_list = $this->arrivalOrderService->getOrderDetail(['order_id' => $arrival_id]);
        if (!$detail_list) {

        }

        $skuIds = array_column($detail_list, 'sku_id');
        $skuList = $this->SkuService->checkSkuInfo(['sku_ids' => $skuIds]);

        $spuIds = array_unique(array_column($detail_list, 'spu_id'));
        $spuList = $this->SpuService->getSpus(['ids' => $spuIds]);

        // 主条码
        $skuBarcode = $this->SkuBarcodeService->getSkuMainBarcode($skuIds);

        // 规格
        $specList = $this->AttributeService->getSpecBySku($skuIds);
        $skuSpec = array_column($specList, 'com', 'sku');// sku=>规格
        // 品牌
        $brandIds = array_unique(array_column($spuList, 'brand_id'));
        $brandList = $this->BrandService->getBrands(['ids' => $brandIds]);
        $spuBrand = array_column($spuList, 'brand_id', 'id');// spu_id=>brand_id
        $brandName = array_column($brandList, 'name', 'id');// brand_id=>brand_name
        // 分类
        $categoryIds = array_unique(array_column($spuList, 'category_id'));
        $spuCategory = array_column($spuList, 'category_id', 'id');// spu_id=>category_id
        $categoryName = [];
        foreach ($categoryIds as $id) {
            $categoryName[$id] = $this->CategoryService->categoryList($id);// category_id=>值
        }
        // 货号
        var_export($spuList);
        $spuNo = array_column($spuList, 'spu_no', 'id');// spu_id=>spu_no
        // 上市时间
        $skuOnlineTime = array_column($skuList, 'online_time', 'id');// sku_id=>上市时间

        $data = [];
        foreach ($detail_list as $val) {
            // 明细
            $data[] = [
                'sku' => $val['sku_id'],
                'barcode' => $skuBarcode[$val['sku_id']] ?? '',
                'spu' => $val['spu_id'] ?? '',
                'spu_no' => $spuNo[$val['spu_id']] ?? '',
                'brand_name' => $brandName[$spuBrand[$val['spu_id']]] ?? '',
                'category' => $categoryName[$spuCategory[$val['spu_id']]] ?? '',
                'online_time' => $skuOnlineTime[$val['sku_id']] ?? '',
                'spec' => implode(' ', $skuSpec[$val['sku_id']] ?? []),
                'num' => $val['num']
            ];
        }
        $header = [
            'sku' => 'SKU',
            'barcode' => '条码',
            'spu' => 'SPU',
            'spu_no' => '货号',
            'brand_name' => '品牌',
            'category' => '品类',
            'online_time' => '上市时间',
            'spec' => '规格',
            'num' => '数量'
        ];
        $info = exportCsv($header, $data, '预约到货单货品明细');
        return $info;
    }

    /**
     * @RequestMapping(path="/arrivalOrder/add_instore/{arrival_code}", methods="get,post")
     * @return mixed
     */
    public function add_instore()
    {
        $arrival_code = $this->request->route('arrival_code');
        $data['arrival_code'] = $arrival_code;
        return $this->show('arrival/addInstore', $data);
    }
}